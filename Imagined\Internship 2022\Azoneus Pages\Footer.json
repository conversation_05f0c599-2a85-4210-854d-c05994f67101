{"content": [{"id": "6205bdd6", "settings": {"gap": "no", "structure": "20", "background_background": "gradient", "background_color": "#141843", "background_color_b": "#2F3E8A", "background_gradient_angle": {"unit": "deg", "size": 90, "sizes": []}, "padding": {"unit": "px", "top": "30", "right": "0", "bottom": "7", "left": "0", "isLinked": false}, "ae_dynamic_rules": [{"_id": "882817a"}], "slider_delay": "5000"}, "elements": [{"id": "59e009a7", "settings": {"_column_size": 50, "_inline_size": null, "_inline_size_tablet": 60, "ae_dynamic_rules": [{"_id": "4cfafd9"}], "slider_delay": "5000"}, "elements": [{"id": "4c8983be", "settings": {"ae_dynamic_rules": [{"_id": "4f71c16"}], "slider_delay": "5000"}, "elements": [{"id": "76d5efaa", "settings": {"_column_size": 100, "_inline_size": null, "_inline_size_tablet": 100, "ae_dynamic_rules": [{"_id": "cd03c01"}], "slider_delay": "5000"}, "elements": [{"id": "73249ff1", "settings": {"image": {"url": "https://azoneus.com/wp-content/uploads/2022/02/logo-300x88.png", "id": "", "source": "url"}, "align": "left", "width": {"unit": "px", "size": "", "sizes": []}, "width_tablet": {"unit": "px", "size": "", "sizes": []}, "width_mobile": {"unit": "px", "size": 140, "sizes": []}, "space": {"unit": "px", "size": 230, "sizes": []}, "space_tablet": {"unit": "px", "size": "", "sizes": []}, "space_mobile": {"unit": "px", "size": "", "sizes": []}, "css_filters_css_filter": "custom", "css_filters_brightness": {"unit": "px", "size": 200, "sizes": []}, "css_filters_contrast": {"unit": "px", "size": 200, "sizes": []}, "css_filters_saturate": {"unit": "px", "size": 0, "sizes": []}, "ae_dynamic_rules": [{"_id": "46d4b73"}]}, "elements": [], "isInner": false, "widgetType": "image", "elType": "widget"}, {"id": "7b2d45db", "settings": {"editor": "<p>Founded in 2010, AUTOMATION ZONE is one of the largest engineering companies in the USA, Saudi Arabia, and Turkey.</p>", "align": "left", "text_color": "#FFFFFF", "typography_typography": "custom", "typography_font_family": "Roboto", "typography_font_size": {"unit": "rem", "size": 1, "sizes": []}, "typography_font_size_tablet": {"unit": "rem", "size": "", "sizes": []}, "typography_font_size_mobile": {"unit": "rem", "size": 0.95, "sizes": []}, "typography_font_weight": "400", "_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "-20", "left": "0", "isLinked": false}, "_padding": {"unit": "px", "top": "0", "right": "80", "bottom": "0", "left": "0", "isLinked": false}, "_padding_mobile": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": true}, "ae_dynamic_rules": [{"_id": "0e16388"}]}, "elements": [], "isInner": false, "widgetType": "text-editor", "elType": "widget"}, {"id": "5dc4bf86", "settings": {"icon_list": [{"text": "<EMAIL>", "selected_icon": {"value": "far fa-envelope", "library": "fa-regular"}, "_id": "51c1669", "link": {"url": "mailto:<EMAIL>", "is_external": "", "nofollow": "", "custom_attributes": ""}}], "text_color": "#FFFFFF", "icon_typography_typography": "custom", "icon_typography_font_family": "Roboto", "icon_typography_font_size": {"unit": "px", "size": 18, "sizes": []}, "icon_typography_font_weight": "400", "_margin": {"unit": "px", "top": "-8", "right": "0", "bottom": "0", "left": "0", "isLinked": false}, "__globals__": {"icon_color": "globals/colors?id=secondary"}, "ae_dynamic_rules": [{"_id": "6350685"}]}, "elements": [], "isInner": false, "widgetType": "icon-list", "elType": "widget"}, {"id": "9f07e9b", "settings": {"social_icon_list": [{"social_icon": {"value": "fab fa-facebook", "library": "fa-brands"}, "_id": "68f1003", "link": {"url": "https://www.facebook.com/Automation-Zone-110192764947558/", "is_external": "true", "nofollow": "", "custom_attributes": ""}, "item_icon_color": "custom", "item_icon_primary_color": "#02010100"}, {"social_icon": {"value": "fas fa-envelope", "library": "fa-solid"}, "_id": "449303c", "link": {"url": "mailto:<EMAIL>", "is_external": "true", "nofollow": "", "custom_attributes": ""}, "item_icon_color": "custom", "item_icon_primary_color": "#02010100"}], "align": "left", "icon_size": {"unit": "px", "size": 20, "sizes": []}, "icon_padding": {"unit": "em", "size": 0.5, "sizes": []}, "icon_spacing": {"unit": "px", "size": 0, "sizes": []}, "hover_animation": "shrink", "_margin": {"unit": "px", "top": "-10", "right": "0", "bottom": "0", "left": "-14", "isLinked": false}, "_padding": {"unit": "px", "top": "0", "right": "0", "bottom": "10", "left": "0", "isLinked": false}, "ae_dynamic_rules": [{"_id": "f4254f3"}]}, "elements": [], "isInner": false, "widgetType": "social-icons", "elType": "widget"}, {"id": "4f0e82b3", "settings": {"shortcode": "Copyright © [hfe_current_year] Automation Zone | All Rights Reserved", "align": "left", "title_color": "#FFFFFF", "caption_typography_typography": "custom", "caption_typography_font_family": "Roboto", "caption_typography_font_size": {"unit": "px", "size": 16, "sizes": []}, "caption_typography_font_weight": "700", "caption_typography_letter_spacing": {"unit": "px", "size": 0.2, "sizes": []}, "caption_typography_word_spacing": {"unit": "px", "size": 2, "sizes": []}, "_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "-6", "isLinked": false}, "caption_typography_font_size_tablet": {"unit": "px", "size": 16, "sizes": []}, "hide_mobile": "hidden-mobile", "ae_dynamic_rules": [{"_id": "3156d74"}]}, "elements": [], "isInner": false, "widgetType": "copyright", "elType": "widget"}], "isInner": true, "elType": "column"}], "isInner": true, "elType": "section"}], "isInner": false, "elType": "column"}, {"id": "5bbbe4f8", "settings": {"_column_size": 50, "_inline_size": null, "_inline_size_tablet": 40, "ae_dynamic_rules": [{"_id": "2f747d9"}], "slider_delay": "5000"}, "elements": [{"id": "235ba7e9", "settings": {"ae_dynamic_rules": [{"_id": "71ba96c"}], "slider_delay": "5000"}, "elements": [{"id": "6725e4d", "settings": {"_column_size": 100, "_inline_size": null, "ae_dynamic_rules": [{"_id": "949d10e"}], "slider_delay": "5000"}, "elements": [{"id": "265509ae", "settings": {"image": {"url": "https://azoneus.com/wp-content/uploads/2022/02/aa-768x480.webp", "id": "", "source": "url"}, "width": {"unit": "px", "size": "", "sizes": []}, "width_tablet": {"unit": "px", "size": "", "sizes": []}, "width_mobile": {"unit": "px", "size": "", "sizes": []}, "space": {"unit": "px", "size": 400, "sizes": []}, "space_tablet": {"unit": "px", "size": 350, "sizes": []}, "space_mobile": {"unit": "px", "size": 250, "sizes": []}, "css_filters_css_filter": "custom", "css_filters_brightness": {"unit": "px", "size": 200, "sizes": []}, "css_filters_contrast": {"unit": "px", "size": 200, "sizes": []}, "css_filters_saturate": {"unit": "px", "size": 36, "sizes": []}, "_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "5", "left": "0", "isLinked": false}, "_padding": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "150", "isLinked": false}, "_margin_tablet": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "-100", "isLinked": false}, "_padding_tablet": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": true}, "_padding_mobile": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": true}, "ae_dynamic_rules": [{"_id": "02897c0"}]}, "elements": [], "isInner": false, "widgetType": "image", "elType": "widget"}, {"id": "51f1f6f1", "settings": {"shortcode": "Copyright © [hfe_current_year] Automation Zone | All Rights Reserved", "align": "left", "title_color": "#FFFFFF", "caption_typography_typography": "custom", "caption_typography_font_family": "Roboto", "caption_typography_font_size": {"unit": "px", "size": 16, "sizes": []}, "caption_typography_font_weight": "700", "caption_typography_letter_spacing": {"unit": "px", "size": 0.2, "sizes": []}, "caption_typography_word_spacing": {"unit": "px", "size": 2, "sizes": []}, "_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "-6", "isLinked": false}, "caption_typography_font_size_mobile": {"unit": "px", "size": 14, "sizes": []}, "_margin_mobile": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": true}, "hide_desktop": "hidden-desktop", "hide_tablet": "hidden-tablet", "ae_dynamic_rules": [{"_id": "66a35c5"}]}, "elements": [], "isInner": false, "widgetType": "copyright", "elType": "widget"}, {"id": "a3c2f57", "settings": {"editor": "<a href=\"https://azoneus.com/azoneus\" style=\"color:white;\">AUTOMATION ZONE</a>", "align": "right", "text_color": "#FFFFFF", "typography_typography": "custom", "typography_font_family": "Nunito", "typography_font_size": {"unit": "px", "size": 16, "sizes": []}, "typography_font_weight": "700", "_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": false}, "_padding": {"unit": "px", "top": "2", "right": "0", "bottom": "0", "left": "0", "isLinked": false}, "align_mobile": "left", "_padding_tablet": {"unit": "px", "top": "58", "right": "0", "bottom": "0", "left": "0", "isLinked": false}, "ae_dynamic_rules": [{"_id": "823f55c"}]}, "elements": [], "isInner": false, "widgetType": "text-editor", "elType": "widget"}], "isInner": true, "elType": "column"}], "isInner": true, "elType": "section"}], "isInner": false, "elType": "column"}], "isInner": false, "elType": "section"}], "page_settings": [], "version": "0.4", "title": "Azoneus_footer", "type": "page"}