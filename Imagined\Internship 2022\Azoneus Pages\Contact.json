{"content": [{"id": "39a0f7c0", "settings": {"layout": "full_width", "gap": "no", "background_background": "classic", "background_color": "#EAEAEA", "padding": {"unit": "px", "top": "30", "right": "0", "bottom": "0", "left": "0", "isLinked": false}, "ae_dynamic_rules": [{"_id": "2cadf4a"}], "slider_delay": "5000"}, "elements": [{"id": "782a6222", "settings": {"_column_size": 100, "_inline_size": null, "ae_dynamic_rules": [{"_id": "490e21a"}], "slider_delay": "5000"}, "elements": [{"id": "77e7bc97", "settings": {"ae_dynamic_rules": [{"_id": "aae500e"}], "slider_delay": "5000"}, "elements": [{"id": "3b17c66", "settings": {"_column_size": 100, "_inline_size": null, "ae_dynamic_rules": [{"_id": "6f240ae"}], "slider_delay": "5000"}, "elements": [{"id": "7fc589e4", "settings": {"title": "Contact Us", "typography_typography": "custom", "typography_font_family": "Nunito", "typography_font_size": {"unit": "px", "size": 40, "sizes": []}, "typography_font_size_mobile": {"unit": "px", "size": 26, "sizes": []}, "typography_font_weight": "600", "_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "-35", "left": "0", "isLinked": false}, "typography_text_transform": "uppercase", "ae_dynamic_rules": [{"_id": "af0fbb8"}]}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}, {"id": "7255edd7", "settings": {"width": {"unit": "px", "size": 25, "sizes": []}, "width_tablet": {"unit": "px", "size": "", "sizes": []}, "width_mobile": {"unit": "px", "size": 20, "sizes": []}, "text": "Divider", "color": "#F4A41C", "weight": {"unit": "px", "size": 4, "sizes": []}, "gap": {"unit": "px", "size": 3, "sizes": []}, "_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": false}, "_margin_mobile": {"unit": "px", "top": "0", "right": "0", "bottom": "-7", "left": "0", "isLinked": false}, "_padding": {"unit": "px", "top": "15", "right": "0", "bottom": "15", "left": "0", "isLinked": false}, "ae_dynamic_rules": [{"_id": "b7c5fe6"}]}, "elements": [], "isInner": false, "widgetType": "divider", "elType": "widget"}], "isInner": true, "elType": "column"}], "isInner": true, "elType": "section"}, {"id": "681be1b7", "settings": {"html": "<div class=\"elementor-shape elementor-shape-bottom\" data-negative=\"false\">\r\n\t\t\t<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 1000 100\" preserveAspectRatio=\"none\">\r\n\t<path class=\"elementor-shape-fill\" opacity=\"0.33\" d=\"M473,67.3c-203.9,88.3-263.1-34-320.3,0C66,119.1,0,59.7,0,59.7V0h1000v59.7 c0,0-62.1,26.1-94.9,29.3c-32.8,3.3-62.8-12.3-75.8-22.1C806,49.6,745.3,8.7,694.9,4.7S492.4,59,473,67.3z\"></path>\r\n\t<path class=\"elementor-shape-fill\" opacity=\"0.66\" d=\"M734,67.3c-45.5,0-77.2-23.2-129.1-39.1c-28.6-8.7-150.3-10.1-254,39.1 s-91.7-34.4-149.2,0C115.7,118.3,0,39.8,0,39.8V0h1000v36.5c0,0-28.2-18.5-92.1-18.5C810.2,18.1,775.7,67.3,734,67.3z\"></path>\r\n\t<path class=\"elementor-shape-fill\" d=\"M766.1,28.9c-200-57.5-266,65.5-395.1,19.5C242,1.8,242,5.4,184.8,20.6C128,35.8,132.3,44.9,89.9,52.5C28.6,63.7,0,0,0,0 h1000c0,0-9.9,40.9-83.6,48.1S829.6,47,766.1,28.9z\"></path>\r\n</svg>\t\t</div>", "_margin": {"unit": "px", "top": "83", "right": "0", "bottom": "0", "left": "0", "isLinked": false}, "_margin_tablet": {"unit": "px", "top": "25", "right": "0", "bottom": "0", "left": "0", "isLinked": false}, "_margin_mobile": {"unit": "px", "top": "-50", "right": "0", "bottom": "0", "left": "0", "isLinked": false}, "_padding": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": true}, "ae_dynamic_rules": [{"_id": "33e7e3e"}]}, "elements": [], "isInner": false, "widgetType": "html", "elType": "widget"}], "isInner": false, "elType": "column"}], "isInner": false, "elType": "section"}, {"id": "7675538b", "settings": {"structure": "20", "margin": {"unit": "px", "top": "35", "right": 0, "bottom": "35", "left": 0, "isLinked": true}, "ae_dynamic_rules": [{"_id": "31b2a46"}], "slider_delay": "5000"}, "elements": [{"id": "50bceb17", "settings": {"_column_size": 50, "_inline_size": 48.247, "margin": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": false}, "ae_dynamic_rules": [{"_id": "5d8cd26"}], "slider_delay": "5000"}, "elements": [{"id": "52a48d97", "settings": {"title": "Get In Touch", "typography_typography": "custom", "typography_font_family": "Nunito", "typography_font_size": {"unit": "px", "size": 40, "sizes": []}, "typography_font_size_mobile": {"unit": "px", "size": 28, "sizes": []}, "typography_font_weight": "600", "_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "20", "left": "0", "isLinked": false}, "__globals__": {"title_color": "globals/colors?id=secondary"}, "ae_dynamic_rules": [{"_id": "dcb5a97"}]}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}, {"id": "440bdbc8", "settings": {"shortcode": "[formidable id=2]", "ae_dynamic_rules": [{"_id": "b797ac8"}]}, "elements": [], "isInner": false, "widgetType": "shortcode", "elType": "widget"}], "isInner": false, "elType": "column"}, {"id": "c5118b2", "settings": {"_column_size": 50, "_inline_size": 51.753, "ae_dynamic_rules": [{"_id": "9ee4902"}], "slider_delay": "5000"}, "elements": [{"id": "1bc1c62", "settings": {"title": "Our Locations", "typography_typography": "custom", "typography_font_family": "Nunito", "typography_font_size": {"unit": "px", "size": 40, "sizes": []}, "typography_font_size_mobile": {"unit": "px", "size": 28, "sizes": []}, "typography_font_weight": "600", "_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "20", "left": "0", "isLinked": false}, "__globals__": {"title_color": "globals/colors?id=secondary"}, "ae_dynamic_rules": [{"_id": "8714c4d"}]}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}, {"id": "39af2ba9", "settings": {"selected_icon": {"value": "", "library": ""}, "title_text": "Head Office USA:", "description_text": "12307S, 80th Ave Palos Park, IL 60464 USA\n<br>\n001 (*************", "text_align": "left", "title_color": "#000000", "title_typography_typography": "custom", "title_typography_font_family": "Nunito", "title_typography_font_size": {"unit": "px", "size": 16, "sizes": []}, "title_typography_font_size_mobile": {"unit": "px", "size": 16, "sizes": []}, "title_typography_font_weight": "700", "title_typography_font_style": "italic", "title_typography_line_height": {"unit": "em", "size": 1.4, "sizes": []}, "description_color": "#000000", "description_typography_typography": "custom", "description_typography_font_family": "Nunito", "description_typography_font_size": {"unit": "px", "size": 14, "sizes": []}, "description_typography_font_weight": "400", "description_typography_line_height": {"unit": "em", "size": "", "sizes": []}, "_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "20", "left": "0", "isLinked": false}, "ae_dynamic_rules": [{"_id": "aa2038e"}]}, "elements": [], "isInner": false, "widgetType": "icon-box", "elType": "widget"}, {"id": "701e2a51", "settings": {"selected_icon": {"value": "", "library": ""}, "title_text": "Saudi Branch:", "description_text": "4700 KAUST Thuwal, 23955-6900 Saudi Arabia\n<br>\n00 966 569430005", "text_align": "left", "title_color": "#000000", "title_typography_typography": "custom", "title_typography_font_family": "Nunito", "title_typography_font_size": {"unit": "px", "size": 16, "sizes": []}, "title_typography_font_weight": "700", "title_typography_font_style": "italic", "title_typography_line_height": {"unit": "em", "size": 1.4, "sizes": []}, "description_color": "#000000", "description_typography_typography": "custom", "description_typography_font_family": "Nunito", "description_typography_font_size": {"unit": "px", "size": 14, "sizes": []}, "description_typography_font_weight": "400", "description_typography_line_height": {"unit": "em", "size": "", "sizes": []}, "_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "20", "left": "0", "isLinked": false}, "ae_dynamic_rules": [{"_id": "9818436"}]}, "elements": [], "isInner": false, "widgetType": "icon-box", "elType": "widget"}, {"id": "3e8de9ed", "settings": {"selected_icon": {"value": "", "library": ""}, "title_text": "Turkey Branch:", "description_text": "<PERSON><PERSON><PERSON><PERSON>, Unnamed Road Beylikduzu, 34520 Turkey\n<br>\n00 90 531 687 37 22", "text_align": "left", "title_color": "#000000", "title_typography_typography": "custom", "title_typography_font_family": "Nunito", "title_typography_font_size": {"unit": "px", "size": 16, "sizes": []}, "title_typography_font_weight": "700", "title_typography_font_style": "italic", "title_typography_line_height": {"unit": "em", "size": 1.4, "sizes": []}, "description_color": "#000000", "description_typography_typography": "custom", "description_typography_font_family": "Nunito", "description_typography_font_size": {"unit": "px", "size": 14, "sizes": []}, "description_typography_font_weight": "400", "description_typography_line_height": {"unit": "em", "size": "", "sizes": []}, "_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "20", "left": "0", "isLinked": false}, "ae_dynamic_rules": [{"_id": "12c1ac4"}]}, "elements": [], "isInner": false, "widgetType": "icon-box", "elType": "widget"}, {"id": "3c6e5e21", "settings": {"address": "Automation Zone 12307S S 80th Ave, Palos Park, IL 60464, United States", "height": {"unit": "px", "size": 300, "sizes": []}, "ae_dynamic_rules": [{"_id": "9e42e82"}]}, "elements": [], "isInner": false, "widgetType": "google_maps", "elType": "widget"}], "isInner": false, "elType": "column"}], "isInner": false, "elType": "section"}], "page_settings": [], "version": "0.4", "title": "Azoneus_contact", "type": "page"}