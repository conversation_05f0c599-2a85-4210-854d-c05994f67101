{"content": [{"id": "3ee83b1a", "settings": {"height": "min-height", "custom_height": {"unit": "px", "size": 470, "sizes": []}, "background_background": "classic", "background_image": {"url": "http://azoneus.com/wp-content/uploads/2022/03/beautiful-city-chongqing2000.webp", "id": "", "source": "url"}, "background_position": "bottom center", "background_repeat": "no-repeat", "background_size": "cover", "background_overlay_background": "classic", "background_overlay_color": "#000000BF", "margin": {"unit": "px", "top": "0", "right": 0, "bottom": "0", "left": 0, "isLinked": false}, "padding": {"unit": "px", "top": "120", "right": "0", "bottom": "0", "left": "0", "isLinked": false}, "ae_dynamic_rules": [{"_id": "52481be"}], "slider_delay": "5000"}, "elements": [{"id": "34039f01", "settings": {"_column_size": 100, "_inline_size": null, "ae_dynamic_rules": [{"_id": "9045b37"}], "slider_delay": "5000"}, "elements": [{"id": "5b73b2df", "settings": {"layout": "full_width", "structure": "30", "margin": {"unit": "px", "top": "90", "right": 0, "bottom": "0", "left": 0, "isLinked": false}, "hide_desktop": "hidden-desktop", "hide_tablet": "hidden-tablet", "hide_mobile": "hidden-mobile", "ae_dynamic_rules": [{"_id": "a513a17"}], "slider_delay": "5000"}, "elements": [{"id": "19e9ce26", "settings": {"_column_size": 33, "_inline_size": null, "content_position": "center", "background_background": "classic", "background_color": "#FFFFFF", "background_overlay_hover_background": "classic", "background_overlay_hover_color": "#F2F5F7", "border_radius": {"unit": "px", "top": "10", "right": "10", "bottom": "10", "left": "10", "isLinked": true}, "margin": {"unit": "px", "top": "10", "right": "10", "bottom": "10", "left": "10", "isLinked": true}, "padding": {"unit": "px", "top": "15", "right": "15", "bottom": "15", "left": "15", "isLinked": true}, "align": "center", "ae_dynamic_rules": [{"_id": "2aa1514"}], "slider_delay": "5000"}, "elements": [{"id": "71982c08", "settings": {"image": {"url": "https://azoneus.com/wp-content/uploads/2022/02/091132876011-stamp.png", "id": "", "source": "url"}, "space": {"unit": "px", "size": 75, "sizes": []}, "space_tablet": {"unit": "px", "size": "", "sizes": []}, "space_mobile": {"unit": "px", "size": "", "sizes": []}, "css_filters_css_filter": "custom", "css_filters_brightness": {"unit": "px", "size": 200, "sizes": []}, "css_filters_contrast": {"unit": "px", "size": 200, "sizes": []}, "css_filters_saturate": {"unit": "px", "size": 200, "sizes": []}, "css_filters_hue": {"unit": "px", "size": 255, "sizes": []}, "link": {"url": "http://localhost/azoneus/solutions#automation", "is_external": "", "nofollow": "", "custom_attributes": ""}, "ae_dynamic_rules": [{"_id": "65c3a02"}]}, "elements": [], "isInner": false, "widgetType": "image", "elType": "widget"}, {"id": "45aba27c", "settings": {"title": "Automation System Integrations", "align": "center", "title_color": "#000000", "typography_typography": "custom", "typography_font_family": "Nunito", "typography_font_size": {"unit": "px", "size": 20, "sizes": []}, "typography_font_weight": "600", "typography_line_height": {"unit": "em", "size": 1, "sizes": []}, "ae_dynamic_rules": [{"_id": "eaccd21"}]}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}], "isInner": true, "elType": "column"}, {"id": "593dcc08", "settings": {"_column_size": 33, "_inline_size": null, "content_position": "center", "background_background": "classic", "background_color": "#FFFFFF", "background_overlay_hover_background": "classic", "background_overlay_hover_color": "#F2F5F7", "border_radius": {"unit": "px", "top": "10", "right": "10", "bottom": "10", "left": "10", "isLinked": true}, "margin": {"unit": "px", "top": "10", "right": "10", "bottom": "10", "left": "10", "isLinked": true}, "padding": {"unit": "px", "top": "15", "right": "15", "bottom": "15", "left": "15", "isLinked": true}, "ae_dynamic_rules": [{"_id": "243cb69"}], "slider_delay": "5000"}, "elements": [{"id": "6eed6691", "settings": {"image": {"url": "https://azoneus.com/wp-content/uploads/2022/02/121039795003-hotel.png", "id": "", "source": "url", "alt": ""}, "space": {"unit": "px", "size": 75, "sizes": []}, "space_tablet": {"unit": "px", "size": "", "sizes": []}, "space_mobile": {"unit": "px", "size": "", "sizes": []}, "css_filters_css_filter": "custom", "css_filters_brightness": {"unit": "px", "size": 200, "sizes": []}, "css_filters_contrast": {"unit": "px", "size": 200, "sizes": []}, "css_filters_saturate": {"unit": "px", "size": 200, "sizes": []}, "css_filters_hue": {"unit": "px", "size": 255, "sizes": []}, "ae_dynamic_rules": [{"_id": "1a2745b"}]}, "elements": [], "isInner": false, "widgetType": "image", "elType": "widget"}, {"id": "12d6607d", "settings": {"title": "Building Management System", "align": "center", "title_color": "#000000", "typography_typography": "custom", "typography_font_family": "Nunito", "typography_font_size": {"unit": "px", "size": 20, "sizes": []}, "typography_font_weight": "600", "typography_line_height": {"unit": "em", "size": 1, "sizes": []}, "ae_dynamic_rules": [{"_id": "76ddc88"}]}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}], "isInner": true, "elType": "column"}, {"id": "694be083", "settings": {"_column_size": 33, "_inline_size": null, "content_position": "center", "background_background": "classic", "background_color": "#FFFFFF", "background_overlay_hover_background": "classic", "background_overlay_hover_color": "#F2F5F7", "border_radius": {"unit": "px", "top": "10", "right": "10", "bottom": "10", "left": "10", "isLinked": true}, "margin": {"unit": "px", "top": "10", "right": "10", "bottom": "10", "left": "10", "isLinked": true}, "padding": {"unit": "px", "top": "15", "right": "15", "bottom": "15", "left": "15", "isLinked": true}, "ae_dynamic_rules": [{"_id": "7d71c5f"}], "slider_delay": "5000"}, "elements": [{"id": "1e41900b", "settings": {"image": {"url": "https://azoneus.com/wp-content/uploads/2022/02/121025723004-computer.png", "id": "", "source": "url", "alt": ""}, "space": {"unit": "px", "size": 75, "sizes": []}, "space_tablet": {"unit": "px", "size": "", "sizes": []}, "space_mobile": {"unit": "px", "size": "", "sizes": []}, "css_filters_css_filter": "custom", "css_filters_brightness": {"unit": "px", "size": 200, "sizes": []}, "css_filters_contrast": {"unit": "px", "size": 200, "sizes": []}, "css_filters_saturate": {"unit": "px", "size": 200, "sizes": []}, "css_filters_hue": {"unit": "px", "size": 255, "sizes": []}, "ae_dynamic_rules": [{"_id": "e58d516"}]}, "elements": [], "isInner": false, "widgetType": "image", "elType": "widget"}, {"id": "3faa4459", "settings": {"title": "Energy Manager and Power Monitoring System", "align": "center", "title_color": "#000000", "typography_typography": "custom", "typography_font_family": "Nunito", "typography_font_size": {"unit": "px", "size": 20, "sizes": []}, "typography_font_weight": "600", "typography_line_height": {"unit": "em", "size": 1, "sizes": []}, "ae_dynamic_rules": [{"_id": "673e614"}]}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}], "isInner": true, "elType": "column"}], "isInner": true, "elType": "section"}, {"id": "7445aa0f", "settings": {"layout": "full_width", "structure": "30", "margin": {"unit": "px", "top": "0", "right": 0, "bottom": "0", "left": 0, "isLinked": false}, "hide_desktop": "hidden-desktop", "hide_tablet": "hidden-tablet", "hide_mobile": "hidden-mobile", "ae_dynamic_rules": [{"_id": "19c7961"}], "slider_delay": "5000"}, "elements": [{"id": "22f3976e", "settings": {"_column_size": 33, "_inline_size": null, "content_position": "center", "background_background": "classic", "background_color": "#FFFFFF", "background_overlay_hover_background": "classic", "background_overlay_hover_color": "#F2F5F7", "border_radius": {"unit": "px", "top": "10", "right": "10", "bottom": "10", "left": "10", "isLinked": true}, "margin": {"unit": "px", "top": "10", "right": "10", "bottom": "10", "left": "10", "isLinked": true}, "padding": {"unit": "px", "top": "15", "right": "15", "bottom": "15", "left": "15", "isLinked": true}, "align": "center", "ae_dynamic_rules": [{"_id": "4fdc18b"}], "slider_delay": "5000"}, "elements": [{"id": "5a60647", "settings": {"image": {"url": "https://azoneus.com/wp-content/uploads/2022/02/121035601006-microscope.png", "id": "", "source": "url", "alt": ""}, "space": {"unit": "px", "size": 75, "sizes": []}, "space_tablet": {"unit": "px", "size": "", "sizes": []}, "space_mobile": {"unit": "px", "size": "", "sizes": []}, "css_filters_css_filter": "custom", "css_filters_brightness": {"unit": "px", "size": 200, "sizes": []}, "css_filters_contrast": {"unit": "px", "size": 200, "sizes": []}, "css_filters_saturate": {"unit": "px", "size": 200, "sizes": []}, "css_filters_hue": {"unit": "px", "size": 255, "sizes": []}, "align": "center", "ae_dynamic_rules": [{"_id": "98c3268"}]}, "elements": [], "isInner": false, "widgetType": "image", "elType": "widget"}, {"id": "4bcdf5a5", "settings": {"title": "Lab Control System", "align": "center", "title_color": "#000000", "typography_typography": "custom", "typography_font_family": "Nunito", "typography_font_size": {"unit": "px", "size": 20, "sizes": []}, "typography_font_weight": "600", "typography_line_height": {"unit": "em", "size": 1, "sizes": []}, "ae_dynamic_rules": [{"_id": "8b8ed76"}]}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}], "isInner": true, "elType": "column"}, {"id": "46f636fd", "settings": {"_column_size": 33, "_inline_size": null, "content_position": "center", "background_background": "classic", "background_color": "#FFFFFF", "background_overlay_hover_background": "classic", "background_overlay_hover_color": "#F2F5F7", "border_radius": {"unit": "px", "top": "10", "right": "10", "bottom": "10", "left": "10", "isLinked": true}, "margin": {"unit": "px", "top": "10", "right": "10", "bottom": "10", "left": "10", "isLinked": true}, "padding": {"unit": "px", "top": "15", "right": "15", "bottom": "15", "left": "15", "isLinked": true}, "ae_dynamic_rules": [{"_id": "9389ea1"}], "slider_delay": "5000"}, "elements": [{"id": "eb109da", "settings": {"image": {"url": "https://azoneus.com/wp-content/uploads/2022/02/121019623005-control.png", "id": "", "source": "url", "alt": ""}, "space": {"unit": "px", "size": 75, "sizes": []}, "space_tablet": {"unit": "px", "size": "", "sizes": []}, "space_mobile": {"unit": "px", "size": "", "sizes": []}, "css_filters_css_filter": "custom", "css_filters_brightness": {"unit": "px", "size": 200, "sizes": []}, "css_filters_contrast": {"unit": "px", "size": 200, "sizes": []}, "css_filters_saturate": {"unit": "px", "size": 200, "sizes": []}, "css_filters_hue": {"unit": "px", "size": 255, "sizes": []}, "align": "center", "ae_dynamic_rules": [{"_id": "19767bb"}]}, "elements": [], "isInner": false, "widgetType": "image", "elType": "widget"}, {"id": "7123363e", "settings": {"title": "Lighting Control System", "align": "center", "title_color": "#000000", "typography_typography": "custom", "typography_font_family": "Nunito", "typography_font_size": {"unit": "px", "size": 20, "sizes": []}, "typography_font_weight": "600", "typography_line_height": {"unit": "em", "size": 1, "sizes": []}, "ae_dynamic_rules": [{"_id": "85a6a5a"}]}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}], "isInner": true, "elType": "column"}, {"id": "254b5d8b", "settings": {"_column_size": 33, "_inline_size": null, "content_position": "center", "background_background": "classic", "background_color": "#FFFFFF", "background_overlay_hover_background": "classic", "background_overlay_hover_color": "#F2F5F7", "border_radius": {"unit": "px", "top": "10", "right": "10", "bottom": "10", "left": "10", "isLinked": true}, "margin": {"unit": "px", "top": "10", "right": "10", "bottom": "10", "left": "10", "isLinked": true}, "padding": {"unit": "px", "top": "15", "right": "15", "bottom": "15", "left": "15", "isLinked": true}, "ae_dynamic_rules": [{"_id": "a405f88"}], "slider_delay": "5000"}, "elements": [{"id": "6b9b6eb0", "settings": {"image": {"url": "https://azoneus.com/wp-content/uploads/2022/02/121057373007-fire-alarm.png", "id": "", "source": "url", "alt": ""}, "space": {"unit": "px", "size": 75, "sizes": []}, "space_tablet": {"unit": "px", "size": "", "sizes": []}, "space_mobile": {"unit": "px", "size": "", "sizes": []}, "css_filters_css_filter": "custom", "css_filters_brightness": {"unit": "px", "size": 200, "sizes": []}, "css_filters_contrast": {"unit": "px", "size": 200, "sizes": []}, "css_filters_saturate": {"unit": "px", "size": 200, "sizes": []}, "css_filters_hue": {"unit": "px", "size": 255, "sizes": []}, "align": "center", "ae_dynamic_rules": [{"_id": "51b74c8"}]}, "elements": [], "isInner": false, "widgetType": "image", "elType": "widget"}, {"id": "11c73087", "settings": {"title": "Fire Alarm System", "align": "center", "title_color": "#000000", "typography_typography": "custom", "typography_font_family": "Nunito", "typography_font_size": {"unit": "px", "size": 20, "sizes": []}, "typography_font_weight": "600", "typography_line_height": {"unit": "em", "size": 1, "sizes": []}, "ae_dynamic_rules": [{"_id": "b198fae"}]}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}], "isInner": true, "elType": "column"}], "isInner": true, "elType": "section"}, {"id": "3088759", "settings": {"html": "<div class=\"elementor-container elementor-column-gap-default\">\r\n\t\t\t\r\n\t<div class=\"elementor-column elementor-col-33 elementor-inner-column elementor-element elementor-element-8a58d3b\" data-id=\"8a58d3b\" data-element_type=\"column\" data-settings=\"{&quot;background_background&quot;:&quot;classic&quot;}\">\t\r\n\t\t\t<div class=\"elementor-widget-wrap elementor-element-populated\" style=\"padding-left:11px\"><a href=\"http://localhost/azoneus/solutions#automation\">\r\n\t\t\t\t\t<div class=\"elementor-background-overlay\"></div>\r\n\t\t\t\t\t\t\t\t<div class=\"elementor-element elementor-element-1c28409 elementor-widget elementor-widget-image\" data-id=\"1c28409\" data-element_type=\"widget\" data-widget_type=\"image.default\">\r\n\t\t\t\t<div class=\"elementor-widget-container\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<img src=\"https://azoneus.com/wp-content/uploads/2022/02/091132876011-stamp.png\" title=\"\" alt=\"\">\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div class=\"elementor-element elementor-element-1a2a4b3 elementor-widget elementor-widget-heading\" data-id=\"1a2a4b3\" data-element_type=\"widget\" data-widget_type=\"heading.default\">\r\n\t\t\t\t<div class=\"elementor-widget-container\">\r\n\t\t\t<style>/*! elementor - v3.7.6 - 15-09-2022 */\r\n.elementor-heading-title{padding:0;margin:0;line-height:1}.elementor-widget-heading .elementor-heading-title[class*=elementor-size-]>a{color:inherit;font-size:inherit;line-height:inherit}.elementor-widget-heading .elementor-heading-title.elementor-size-small{font-size:15px}.elementor-widget-heading .elementor-heading-title.elementor-size-medium{font-size:19px}.elementor-widget-heading .elementor-heading-title.elementor-size-large{font-size:29px}.elementor-widget-heading .elementor-heading-title.elementor-size-xl{font-size:39px}.elementor-widget-heading .elementor-heading-title.elementor-size-xxl{font-size:59px}</style><h2 class=\"elementor-heading-title elementor-size-default\">Automation System Integrations</h2>\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t\t</a>\t</div>\r\n\t\t</div>\r\n\t\t\t\t<div class=\"elementor-column elementor-col-33 elementor-inner-column elementor-element elementor-element-5d86537\" data-id=\"5d86537\" data-element_type=\"column\" data-settings=\"{&quot;background_background&quot;:&quot;classic&quot;}\">\r\n\t\t\t<div class=\"elementor-widget-wrap elementor-element-populated\" style=\"padding-left:41px\"><a href=\"http://localhost/azoneus/solutions#building\">\r\n\t\t\t\t\t<div class=\"elementor-background-overlay\"></div>\r\n\t\t\t\t\t\t\t\t<div class=\"elementor-element elementor-element-ab93ed6 elementor-widget elementor-widget-image\" data-id=\"ab93ed6\" data-element_type=\"widget\" data-widget_type=\"image.default\">\r\n\t\t\t\t<div class=\"elementor-widget-container\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<img src=\"https://azoneus.com/wp-content/uploads/2022/02/121039795003-hotel.png\" title=\"\" alt=\"\">\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div class=\"elementor-element elementor-element-d0603a4 elementor-widget elementor-widget-heading\" data-id=\"d0603a4\" data-element_type=\"widget\" data-widget_type=\"heading.default\">\r\n\t\t\t\t<div class=\"elementor-widget-container\">\r\n\t\t\t<h2 class=\"elementor-heading-title elementor-size-default\">Building Management System</h2>\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t</a>\t\t\t</div>\r\n\t\t</div>\r\n\t\t\t\t<div class=\"elementor-column elementor-col-33 elementor-inner-column elementor-element elementor-element-2d7fefe\" data-id=\"2d7fefe\" data-element_type=\"column\" data-settings=\"{&quot;background_background&quot;:&quot;classic&quot;}\">\r\n\t\t\t<div class=\"elementor-widget-wrap elementor-element-populated\"><a href=\"http://localhost/azoneus/solutions#power\">\r\n\t\t\t\t\t<div class=\"elementor-background-overlay\"></div>\r\n\t\t\t\t\t\t\t\t<div class=\"elementor-element elementor-element-592460f elementor-widget elementor-widget-image\" data-id=\"592460f\" data-element_type=\"widget\" data-widget_type=\"image.default\">\r\n\t\t\t\t<div class=\"elementor-widget-container\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<img src=\"https://azoneus.com/wp-content/uploads/2022/02/121025723004-computer.png\" title=\"\" alt=\"\">\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div class=\"elementor-element elementor-element-d5d2174 elementor-widget elementor-widget-heading\" data-id=\"d5d2174\" data-element_type=\"widget\" data-widget_type=\"heading.default\">\r\n\t\t\t\t<div class=\"elementor-widget-container\">\r\n\t\t\t<h2 class=\"elementor-heading-title elementor-size-default\">Energy Manager and Power Monitoring System</h2>\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</a>\t\t</div>\r\n\t\t</div>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div class=\"elementor-container elementor-column-gap-default\">\r\n\t\t\t\t\t<div class=\"elementor-column elementor-col-33 elementor-inner-column elementor-element elementor-element-ca494d6\" data-id=\"ca494d6\" data-element_type=\"column\" data-settings=\"{&quot;background_background&quot;:&quot;classic&quot;}\">\r\n\t\t\t<div class=\"elementor-widget-wrap elementor-element-populated\"><a href=\"http://localhost/azoneus/solutions#lab\">\r\n\t\t\t\t\t<div class=\"elementor-background-overlay\"></div>\r\n\t\t\t\t\t\t\t\t<div class=\"elementor-element elementor-element-c625015 elementor-widget elementor-widget-image\" data-id=\"c625015\" data-element_type=\"widget\" data-widget_type=\"image.default\">\r\n\t\t\t\t<div class=\"elementor-widget-container\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<img src=\"https://azoneus.com/wp-content/uploads/2022/02/121035601006-microscope.png\" title=\"\" alt=\"\">\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div class=\"elementor-element elementor-element-ec1be7c elementor-widget elementor-widget-heading\" data-id=\"ec1be7c\" data-element_type=\"widget\" data-widget_type=\"heading.default\">\r\n\t\t\t\t<div class=\"elementor-widget-container\">\r\n\t\t\t<h2 class=\"elementor-heading-title elementor-size-default\">&emsp;&emsp;&ensp;Lab Control System&emsp;&ensp;&ensp;&ensp;</h2>\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</a>\t\t</div>\r\n\t\t</div>\r\n\t\t\t\t<div class=\"elementor-column elementor-col-33 elementor-inner-column elementor-element elementor-element-b1b1c73\" data-id=\"b1b1c73\" data-element_type=\"column\" data-settings=\"{&quot;background_background&quot;:&quot;classic&quot;}\">\r\n\t\t\t<div class=\"elementor-widget-wrap elementor-element-populated\" style=\"padding-left:35px\"><a href=\"http://localhost/azoneus/solutions#lighting\">\r\n\t\t\t\t\t<div class=\"elementor-background-overlay\"></div>\r\n\t\t\t\t\t\t\t\t<div class=\"elementor-element elementor-element-43daec3 elementor-widget elementor-widget-image\" data-id=\"43daec3\" data-element_type=\"widget\" data-widget_type=\"image.default\">\r\n\t\t\t\t<div class=\"elementor-widget-container\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<img src=\"https://azoneus.com/wp-content/uploads/2022/02/121019623005-control.png\" title=\"\" alt=\"\">\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div class=\"elementor-element elementor-element-aa1f92b elementor-widget elementor-widget-heading\" data-id=\"aa1f92b\" data-element_type=\"widget\" data-widget_type=\"heading.default\">\r\n\t\t\t\t<div class=\"elementor-widget-container\">\r\n\t\t\t<h2 class=\"elementor-heading-title elementor-size-default\">&emsp;&emsp;Lighting Control System&emsp;&emsp;</h2>\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</a>\t\t</div>\r\n\t\t</div>\r\n\t\t\t\t<div class=\"elementor-column elementor-col-33 elementor-inner-column elementor-element elementor-element-37878bb\" data-id=\"37878bb\" data-element_type=\"column\" data-settings=\"{&quot;background_background&quot;:&quot;classic&quot;}\">\r\n\t\t\t<div class=\"elementor-widget-wrap elementor-element-populated\" style=\"padding-left:35px\"><a href=\"http://localhost/azoneus/solutions#fire\">\r\n\t\t\t\t\t<div class=\"elementor-background-overlay\"></div>\r\n\t\t\t\t\t\t\t\t<div class=\"elementor-element elementor-element-c73094b elementor-widget elementor-widget-image\" data-id=\"c73094b\" data-element_type=\"widget\" data-widget_type=\"image.default\">\r\n\t\t\t\t<div class=\"elementor-widget-container\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<img src=\"https://azoneus.com/wp-content/uploads/2022/02/121057373007-fire-alarm.png\" title=\"\" alt=\"\">\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div class=\"elementor-element elementor-element-3f69af9 elementor-widget elementor-widget-heading\" data-id=\"3f69af9\" data-element_type=\"widget\" data-widget_type=\"heading.default\">\r\n\t\t\t\t<div class=\"elementor-widget-container\">\r\n\t\t\t<h2 class=\"elementor-heading-title elementor-size-default\">&emsp;&emsp;&emsp;Fire Alarm System&emsp;&emsp;&emsp;</h2>\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t\t</a>\t</div>\r\n\t\t</div>\r\n\t\t\t\t\t\t\t</div>", "_margin": {"unit": "px", "top": "90", "right": "0", "bottom": "0", "left": "0", "isLinked": false}, "_margin_mobile": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": false}, "ae_dynamic_rules": [{"_id": "335c7f1"}]}, "elements": [], "isInner": false, "widgetType": "html", "elType": "widget"}], "isInner": false, "elType": "column"}], "isInner": false, "elType": "section"}, {"id": "603e4007", "settings": {"structure": "20", "padding": {"unit": "px", "top": "50", "right": "0", "bottom": "50", "left": "0", "isLinked": false}, "ae_dynamic_rules": [{"_id": "43420d9"}], "slider_delay": "5000"}, "elements": [{"id": "2fd35497", "settings": {"_column_size": 50, "_inline_size": 52.53, "ae_dynamic_rules": [{"_id": "4de2030"}], "slider_delay": "5000"}, "elements": [{"id": "3dbd7861", "settings": {"ae_dynamic_rules": [{"_id": "8d7bd30"}], "slider_delay": "5000"}, "elements": [{"id": "1f4acd05", "settings": {"_column_size": 100, "_inline_size": null, "ae_dynamic_rules": [{"_id": "312c114"}], "slider_delay": "5000"}, "elements": [{"id": "55bb4f2d", "settings": {"title": "About Us", "typography_typography": "custom", "typography_font_family": "Nunito", "typography_font_size": {"unit": "px", "size": 23, "sizes": []}, "typography_font_weight": "700", "__globals__": {"title_color": "globals/colors?id=secondary"}, "ae_dynamic_rules": [{"_id": "9a49e2b"}]}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}, {"id": "7a253f0", "settings": {"title": "Taking care of our customers is our first principle and our number one priority", "title_color": "#000000", "typography_typography": "custom", "typography_font_family": "Nunito", "typography_font_size": {"unit": "px", "size": 40, "sizes": []}, "typography_font_weight": "300", "typography_line_height": {"unit": "em", "size": 1, "sizes": []}, "typography_font_size_tablet": {"unit": "px", "size": 22, "sizes": []}, "typography_font_size_mobile": {"unit": "px", "size": 22, "sizes": []}, "ae_dynamic_rules": [{"_id": "5c4f112"}]}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}, {"id": "3501a221", "settings": {"editor": "<p>Established in 2010 in the USA as an integration company only. Started the Saudi office in 2011. Expanded services in 2013 to cover Fire Alarm and Fire management solutions.</p>", "text_color": "#000000", "typography_typography": "custom", "typography_font_family": "Nunito", "typography_font_size": {"unit": "rem", "size": 1, "sizes": []}, "typography_font_size_tablet": {"unit": "rem", "size": "", "sizes": []}, "typography_font_size_mobile": {"unit": "rem", "size": "", "sizes": []}, "typography_font_weight": "400", "_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "-20", "left": "0", "isLinked": false}, "ae_dynamic_rules": [{"_id": "aff476d"}]}, "elements": [], "isInner": false, "widgetType": "text-editor", "elType": "widget"}, {"id": "1cbfd323", "settings": {"editor": "<p>Further expansion in 2015 for Building Management, Lighting Controls, Power Monitoring systems &amp; Lab control.<br />Covered many projects with major clients</p>", "text_color": "#000000", "typography_typography": "custom", "typography_font_family": "Nunito", "typography_font_size": {"unit": "rem", "size": 1, "sizes": []}, "typography_font_size_tablet": {"unit": "rem", "size": "", "sizes": []}, "typography_font_size_mobile": {"unit": "rem", "size": "", "sizes": []}, "typography_font_weight": "400", "_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "-10", "left": "0", "isLinked": false}, "ae_dynamic_rules": [{"_id": "38f9399"}]}, "elements": [], "isInner": false, "widgetType": "text-editor", "elType": "widget"}], "isInner": true, "elType": "column"}], "isInner": true, "elType": "section"}, {"id": "6cc35513", "settings": {"text": "Read More", "link": {"url": "http://localhost/azoneus/about/", "is_external": "", "nofollow": "", "custom_attributes": ""}, "typography_typography": "custom", "typography_font_family": "Nunito", "typography_font_size": {"unit": "px", "size": 15, "sizes": []}, "typography_font_weight": "500", "typography_line_height": {"unit": "em", "size": 1, "sizes": []}, "hover_animation": "grow", "border_radius": {"unit": "px", "top": "5", "right": "5", "bottom": "5", "left": "5", "isLinked": true}, "_padding": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "10", "isLinked": false}, "__globals__": {"background_color": "globals/colors?id=secondary"}, "ae_dynamic_rules": [{"_id": "41899ff"}]}, "elements": [], "isInner": false, "widgetType": "button", "elType": "widget"}], "isInner": false, "elType": "column"}, {"id": "68f47c5e", "settings": {"_column_size": 50, "_inline_size": 47.423, "margin": {"unit": "px", "top": "50", "right": "0", "bottom": "0", "left": "0", "isLinked": false}, "ae_dynamic_rules": [{"_id": "2b0e9b8"}], "slider_delay": "5000"}, "elements": [{"id": "2944f927", "settings": {"image": {"url": "https://azoneus.com/wp-content/uploads/2022/02/engineer-meeting-architectural-project-working-with-partner_1421-70-300x200.jpg", "id": "", "source": "url"}, "image_size": "full", "width": {"unit": "%", "size": 100, "sizes": []}, "space_tablet": {"unit": "px", "size": "", "sizes": []}, "space_mobile": {"unit": "px", "size": "", "sizes": []}, "image_border_radius": {"unit": "px", "top": "5", "right": "5", "bottom": "5", "left": "5", "isLinked": true}, "ae_dynamic_rules": [{"_id": "a128c75"}]}, "elements": [], "isInner": false, "widgetType": "image", "elType": "widget"}], "isInner": false, "elType": "column"}], "isInner": false, "elType": "section"}, {"id": "442649b0", "settings": {"structure": "40", "background_background": "gradient", "background_color": "#141843", "background_color_b": "#2F3E8A", "background_gradient_angle": {"unit": "deg", "size": 90, "sizes": []}, "padding": {"unit": "px", "top": "20", "right": "0", "bottom": "20", "left": "0", "isLinked": false}, "ae_dynamic_rules": [{"_id": "32bfa94"}], "slider_delay": "5000"}, "elements": [{"id": "32092686", "settings": {"_column_size": 25, "_inline_size": null, "padding": {"unit": "px", "top": "10", "right": "10", "bottom": "10", "left": "10", "isLinked": true}, "ae_dynamic_rules": [{"_id": "fa8b1f0"}], "slider_delay": "5000"}, "elements": [{"id": "66a2337d", "settings": {"ending_number": 1000, "suffix": "+", "thousand_separator": "", "title": "Systems Installed", "number_color": "#FFFFFF", "typography_number_typography": "custom", "typography_number_font_family": "Nunito", "typography_number_font_size": {"unit": "px", "size": 69, "sizes": []}, "typography_number_font_weight": "600", "typography_number_line_height": {"unit": "em", "size": 1, "sizes": []}, "title_color": "#F4A41C", "typography_title_typography": "custom", "typography_title_font_family": "Nunito", "typography_title_font_size": {"unit": "px", "size": 19, "sizes": []}, "typography_title_font_weight": "400", "typography_title_line_height": {"unit": "em", "size": 2.5, "sizes": []}, "typography_number_font_size_tablet": {"unit": "px", "size": 34, "sizes": []}, "typography_number_font_size_mobile": {"unit": "px", "size": 34, "sizes": []}, "ae_dynamic_rules": [{"_id": "3ea754a"}]}, "elements": [], "isInner": false, "widgetType": "counter", "elType": "widget"}], "isInner": false, "elType": "column"}, {"id": "12b4ffac", "settings": {"_column_size": 25, "_inline_size": null, "padding": {"unit": "px", "top": "10", "right": "10", "bottom": "10", "left": "10", "isLinked": true}, "ae_dynamic_rules": [{"_id": "68dc566"}], "slider_delay": "5000"}, "elements": [{"id": "673569de", "settings": {"ending_number": 5, "suffix": "+", "thousand_separator": "", "title": "Countries Served", "number_color": "#FFFFFF", "typography_number_typography": "custom", "typography_number_font_family": "Nunito", "typography_number_font_size": {"unit": "px", "size": 69, "sizes": []}, "typography_number_font_weight": "600", "typography_number_line_height": {"unit": "em", "size": 1, "sizes": []}, "title_color": "#F4A41C", "typography_title_typography": "custom", "typography_title_font_family": "Nunito", "typography_title_font_size": {"unit": "px", "size": 19, "sizes": []}, "typography_title_font_weight": "400", "typography_title_line_height": {"unit": "em", "size": 2.5, "sizes": []}, "typography_number_font_size_tablet": {"unit": "px", "size": 34, "sizes": []}, "typography_number_font_size_mobile": {"unit": "px", "size": 34, "sizes": []}, "ae_dynamic_rules": [{"_id": "a3db26c"}]}, "elements": [], "isInner": false, "widgetType": "counter", "elType": "widget"}], "isInner": false, "elType": "column"}, {"id": "7fb8870d", "settings": {"_column_size": 25, "_inline_size": null, "padding": {"unit": "px", "top": "10", "right": "10", "bottom": "10", "left": "10", "isLinked": true}, "ae_dynamic_rules": [{"_id": "08ac059"}], "slider_delay": "5000"}, "elements": [{"id": "2aeded2b", "settings": {"ending_number": 12, "suffix": "+", "thousand_separator": "", "title": "Years", "number_color": "#FFFFFF", "typography_number_typography": "custom", "typography_number_font_family": "Nunito", "typography_number_font_size": {"unit": "px", "size": 69, "sizes": []}, "typography_number_font_weight": "600", "typography_number_line_height": {"unit": "em", "size": 1, "sizes": []}, "title_color": "#F4A41C", "typography_title_typography": "custom", "typography_title_font_family": "Nunito", "typography_title_font_size": {"unit": "px", "size": 19, "sizes": []}, "typography_title_font_weight": "400", "typography_title_line_height": {"unit": "em", "size": 2.5, "sizes": []}, "typography_number_font_size_tablet": {"unit": "px", "size": 34, "sizes": []}, "typography_number_font_size_mobile": {"unit": "px", "size": 34, "sizes": []}, "ae_dynamic_rules": [{"_id": "adb35ec"}]}, "elements": [], "isInner": false, "widgetType": "counter", "elType": "widget"}], "isInner": false, "elType": "column"}, {"id": "7d537915", "settings": {"_column_size": 25, "_inline_size": null, "padding": {"unit": "px", "top": "10", "right": "10", "bottom": "10", "left": "10", "isLinked": true}, "ae_dynamic_rules": [{"_id": "2da11ba"}], "slider_delay": "5000"}, "elements": [{"id": "3d5f0d4a", "settings": {"suffix": "+", "thousand_separator": "", "title": "Clients", "number_color": "#FFFFFF", "typography_number_typography": "custom", "typography_number_font_family": "Nunito", "typography_number_font_size": {"unit": "px", "size": 69, "sizes": []}, "typography_number_font_weight": "600", "typography_number_line_height": {"unit": "em", "size": 1, "sizes": []}, "title_color": "#F4A41C", "typography_title_typography": "custom", "typography_title_font_family": "Nunito", "typography_title_font_size": {"unit": "px", "size": 19, "sizes": []}, "typography_title_font_weight": "400", "typography_title_line_height": {"unit": "em", "size": 2.5, "sizes": []}, "typography_number_font_size_tablet": {"unit": "px", "size": 34, "sizes": []}, "typography_number_font_size_mobile": {"unit": "px", "size": 34, "sizes": []}, "ae_dynamic_rules": [{"_id": "51b55be"}]}, "elements": [], "isInner": false, "widgetType": "counter", "elType": "widget"}], "isInner": false, "elType": "column"}], "isInner": false, "elType": "section"}, {"id": "4e28df7a", "settings": {"structure": "20", "padding": {"unit": "px", "top": "50", "right": "0", "bottom": "50", "left": "0", "isLinked": false}, "ae_dynamic_rules": [{"_id": "bb7757e"}], "slider_delay": "5000"}, "elements": [{"id": "53a79f88", "settings": {"_column_size": 50, "_inline_size": 28.286, "ae_dynamic_rules": [{"_id": "86ec7a5"}], "slider_delay": "5000"}, "elements": [{"id": "758b65a7", "settings": {"title": "Valuable Clients of Automation Zone", "align": "center", "typography_typography": "custom", "typography_font_family": "Nunito", "typography_font_size": {"unit": "px", "size": 30, "sizes": []}, "typography_font_weight": "700", "_padding": {"unit": "px", "top": "0", "right": "0", "bottom": "20", "left": "0", "isLinked": false}, "__globals__": {"title_color": "globals/colors?id=secondary"}, "ae_dynamic_rules": [{"_id": "1943140"}]}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}, {"id": "571832af", "settings": {"text": "View More", "link": {"url": "http://localhost/azoneus/clients/", "is_external": "", "nofollow": "", "custom_attributes": ""}, "align": "center", "typography_typography": "custom", "typography_font_family": "Nunito", "typography_font_weight": "500", "hover_animation": "grow", "border_radius": {"unit": "px", "top": "5", "right": "5", "bottom": "5", "left": "5", "isLinked": true}, "text_padding": {"unit": "px", "top": "12", "right": "24", "bottom": "12", "left": "24", "isLinked": false}, "_padding": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": false}, "__globals__": {"background_color": "globals/colors?id=secondary"}, "typography_font_size_mobile": {"unit": "px", "size": 15, "sizes": []}, "ae_dynamic_rules": [{"_id": "b052e73"}]}, "elements": [], "isInner": false, "widgetType": "button", "elType": "widget"}], "isInner": false, "elType": "column"}, {"id": "6a9c39e9", "settings": {"_column_size": 50, "_inline_size": 71.714, "ae_dynamic_rules": [{"_id": "d726878"}], "slider_delay": "5000"}, "elements": [{"id": "14ccc052", "settings": {"structure": "40", "ae_dynamic_rules": [{"_id": "9f3498a"}], "slider_delay": "5000"}, "elements": [{"id": "5b52ba6c", "settings": {"_column_size": 25, "_inline_size": null, "ae_dynamic_rules": [{"_id": "73ed5e8"}], "slider_delay": "5000"}, "elements": [], "isInner": true, "elType": "column"}, {"id": "5bc12bf5", "settings": {"_column_size": 25, "_inline_size": null, "margin_mobile": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": false}, "padding_mobile": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": false}, "ae_dynamic_rules": [{"_id": "b631e28"}], "slider_delay": "5000"}, "elements": [], "isInner": true, "elType": "column"}, {"id": "1038466e", "settings": {"_column_size": 25, "_inline_size": null, "content_position": "center", "padding": {"unit": "px", "top": "40", "right": "0", "bottom": "10", "left": "0", "isLinked": false}, "ae_dynamic_rules": [{"_id": "5d9b375"}], "slider_delay": "5000"}, "elements": [{"id": "66dffe04", "settings": {"image": {"url": "https://azoneus.com/wp-content/uploads/2022/02/17171693.548c103186dbe-removebg-preview-1.png", "id": "", "source": "url"}, "space": {"unit": "px", "size": 150, "sizes": []}, "space_tablet": {"unit": "px", "size": 100, "sizes": []}, "space_mobile": {"unit": "px", "size": 100, "sizes": []}, "hover_animation": "grow", "_padding_mobile": {"unit": "px", "top": "100", "right": "0", "bottom": "0", "left": "0", "isLinked": false}, "ae_dynamic_rules": [{"_id": "475a350"}]}, "elements": [], "isInner": false, "widgetType": "image", "elType": "widget"}], "isInner": true, "elType": "column"}, {"id": "fc892fd", "settings": {"_column_size": 25, "_inline_size": null, "content_position": "center", "padding": {"unit": "px", "top": "50", "right": "0", "bottom": "10", "left": "0", "isLinked": false}, "ae_dynamic_rules": [{"_id": "8c1c8f2"}], "slider_delay": "5000"}, "elements": [{"id": "2b55ad62", "settings": {"image": {"url": "https://azoneus.com/wp-content/uploads/2022/02/Screenshot_2022-02-21_195707-removebg-preview-e1645467046185.png", "id": "", "source": "url"}, "space": {"unit": "px", "size": 100, "sizes": []}, "space_tablet": {"unit": "px", "size": 75, "sizes": []}, "space_mobile": {"unit": "px", "size": 75, "sizes": []}, "hover_animation": "grow", "_padding_mobile": {"unit": "px", "top": "15", "right": "0", "bottom": "0", "left": "0", "isLinked": false}, "ae_dynamic_rules": [{"_id": "413c50b"}]}, "elements": [], "isInner": false, "widgetType": "image", "elType": "widget"}], "isInner": true, "elType": "column"}], "isInner": true, "elType": "section"}], "isInner": false, "elType": "column"}], "isInner": false, "elType": "section"}, {"id": "4ab893c1", "settings": {"structure": "20", "background_background": "gradient", "__globals__": {"background_color": "globals/colors?id=primary", "background_color_b": "globals/colors?id=secondary"}, "gap_columns_custom_mobile": {"unit": "px", "size": 5, "sizes": []}, "ae_dynamic_rules": [{"_id": "81627c2"}], "slider_delay": "5000"}, "elements": [{"id": "6a05ae02", "settings": {"_column_size": 50, "_inline_size": 75.189, "content_position": "center", "ae_dynamic_rules": [{"_id": "68eeea9"}], "slider_delay": "5000"}, "elements": [{"id": "636638c3", "settings": {"title": "To discuss how we can work together, send in a call back request or contact us directly!", "title_color": "#FFFFFF", "typography_typography": "custom", "typography_font_family": "Roboto", "typography_font_size": {"unit": "px", "size": 18, "sizes": []}, "typography_font_weight": "400", "typography_line_height": {"unit": "em", "size": 1, "sizes": []}, "typography_font_size_mobile": {"unit": "px", "size": 16, "sizes": []}, "ae_dynamic_rules": [{"_id": "9d3523e"}]}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}], "isInner": false, "elType": "column"}, {"id": "544ba98", "settings": {"_column_size": 50, "_inline_size": 24.715, "ae_dynamic_rules": [{"_id": "0f9ba5f"}], "slider_delay": "5000"}, "elements": [{"id": "3bf8dce2", "settings": {"text": "Contact us", "align": "center", "typography_typography": "custom", "typography_font_family": "Roboto", "typography_font_size": {"unit": "px", "size": 15, "sizes": []}, "typography_font_weight": "500", "typography_line_height": {"unit": "em", "size": 1, "sizes": []}, "background_color": "#FFFFFF", "hover_animation": "grow", "border_radius": {"unit": "px", "top": "5", "right": "5", "bottom": "5", "left": "5", "isLinked": true}, "text_padding": {"unit": "px", "top": "12", "right": "24", "bottom": "12", "left": "24", "isLinked": false}, "__globals__": {"button_text_color": "globals/colors?id=secondary"}, "link": {"url": "http://localhost/azoneus/contact/", "is_external": "", "nofollow": "", "custom_attributes": ""}, "align_mobile": "left", "ae_dynamic_rules": [{"_id": "9ce084d"}]}, "elements": [], "isInner": false, "widgetType": "button", "elType": "widget"}], "isInner": false, "elType": "column"}], "isInner": false, "elType": "section"}], "page_settings": [], "version": "0.4", "title": "Azoneus_home", "type": "page"}