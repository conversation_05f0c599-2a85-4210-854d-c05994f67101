{"version": 3, "file": "zxcvbn-ts.js", "sources": ["../src/helper.ts", "../src/data/dateSplits.ts", "../src/data/const.ts", "../src/matcher/date/matching.ts", "../../../../node_modules/fastest-levenshtein/index.js", "../src/levenshtein.ts", "../src/data/l33tTable.ts", "../src/data/translationKeys.ts", "../src/Options.ts", "../src/matcher/dictionary/variants/matching/reverse.ts", "../src/matcher/dictionary/variants/matching/l33t.ts", "../src/matcher/dictionary/matching.ts", "../src/matcher/regex/matching.ts", "../src/scoring/utils.ts", "../src/matcher/bruteforce/scoring.ts", "../src/matcher/date/scoring.ts", "../src/matcher/dictionary/variants/scoring/uppercase.ts", "../src/matcher/dictionary/variants/scoring/l33t.ts", "../src/matcher/dictionary/scoring.ts", "../src/matcher/regex/scoring.ts", "../src/matcher/repeat/scoring.ts", "../src/matcher/sequence/scoring.ts", "../src/matcher/spatial/scoring.ts", "../src/scoring/estimate.ts", "../src/scoring/index.ts", "../src/matcher/repeat/matching.ts", "../src/matcher/sequence/matching.ts", "../src/matcher/spatial/matching.ts", "../src/Matching.ts", "../src/TimeEstimates.ts", "../src/matcher/bruteforce/feedback.ts", "../src/matcher/date/feedback.ts", "../src/matcher/dictionary/feedback.ts", "../src/matcher/regex/feedback.ts", "../src/matcher/repeat/feedback.ts", "../src/matcher/sequence/feedback.ts", "../src/matcher/spatial/feedback.ts", "../src/Feedback.ts", "../src/debounce.ts", "../src/index.ts"], "sourcesContent": [null, null, null, null, "\"use strict\";\nconst peq = new Uint32Array(0x10000);\nconst myers_32 = (a, b) => {\n  const n = a.length;\n  const m = b.length;\n  const lst = 1 << (n - 1);\n  let pv = -1;\n  let mv = 0;\n  let sc = n;\n  let i = n;\n  while (i--) {\n    peq[a.charCodeAt(i)] |= 1 << i;\n  }\n  for (i = 0; i < m; i++) {\n    let eq = peq[b.charCodeAt(i)];\n    const xv = eq | mv;\n    eq |= ((eq & pv) + pv) ^ pv;\n    mv |= ~(eq | pv);\n    pv &= eq;\n    if (mv & lst) {\n      sc++;\n    }\n    if (pv & lst) {\n      sc--;\n    }\n    mv = (mv << 1) | 1;\n    pv = (pv << 1) | ~(xv | mv);\n    mv &= xv;\n  }\n  i = n;\n  while (i--) {\n    peq[a.charCodeAt(i)] = 0;\n  }\n  return sc;\n};\n\nconst myers_x = (a, b) => {\n  const n = a.length;\n  const m = b.length;\n  const mhc = [];\n  const phc = [];\n  const hsize = Math.ceil(n / 32);\n  const vsize = Math.ceil(m / 32);\n  let score = m;\n  for (let i = 0; i < hsize; i++) {\n    phc[i] = -1;\n    mhc[i] = 0;\n  }\n  let j = 0;\n  for (; j < vsize - 1; j++) {\n    let mv = 0;\n    let pv = -1;\n    const start = j * 32;\n    const end = Math.min(32, m) + start;\n    for (let k = start; k < end; k++) {\n      peq[b.charCodeAt(k)] |= 1 << k;\n    }\n    score = m;\n    for (let i = 0; i < n; i++) {\n      const eq = peq[a.charCodeAt(i)];\n      const pb = (phc[(i / 32) | 0] >>> i) & 1;\n      const mb = (mhc[(i / 32) | 0] >>> i) & 1;\n      const xv = eq | mv;\n      const xh = ((((eq | mb) & pv) + pv) ^ pv) | eq | mb;\n      let ph = mv | ~(xh | pv);\n      let mh = pv & xh;\n      if ((ph >>> 31) ^ pb) {\n        phc[(i / 32) | 0] ^= 1 << i;\n      }\n      if ((mh >>> 31) ^ mb) {\n        mhc[(i / 32) | 0] ^= 1 << i;\n      }\n      ph = (ph << 1) | pb;\n      mh = (mh << 1) | mb;\n      pv = mh | ~(xv | ph);\n      mv = ph & xv;\n    }\n    for (let k = start; k < end; k++) {\n      peq[b.charCodeAt(k)] = 0;\n    }\n  }\n  let mv = 0;\n  let pv = -1;\n  const start = j * 32;\n  const end = Math.min(32, m - start) + start;\n  for (let k = start; k < end; k++) {\n    peq[b.charCodeAt(k)] |= 1 << k;\n  }\n  score = m;\n  for (let i = 0; i < n; i++) {\n    const eq = peq[a.charCodeAt(i)];\n    const pb = (phc[(i / 32) | 0] >>> i) & 1;\n    const mb = (mhc[(i / 32) | 0] >>> i) & 1;\n    const xv = eq | mv;\n    const xh = ((((eq | mb) & pv) + pv) ^ pv) | eq | mb;\n    let ph = mv | ~(xh | pv);\n    let mh = pv & xh;\n    score += (ph >>> (m - 1)) & 1;\n    score -= (mh >>> (m - 1)) & 1;\n    if ((ph >>> 31) ^ pb) {\n      phc[(i / 32) | 0] ^= 1 << i;\n    }\n    if ((mh >>> 31) ^ mb) {\n      mhc[(i / 32) | 0] ^= 1 << i;\n    }\n    ph = (ph << 1) | pb;\n    mh = (mh << 1) | mb;\n    pv = mh | ~(xv | ph);\n    mv = ph & xv;\n  }\n  for (let k = start; k < end; k++) {\n    peq[b.charCodeAt(k)] = 0;\n  }\n  return score;\n};\n\nconst distance = (a, b) => {\n  if (a.length > b.length) {\n    const tmp = b;\n    b = a;\n    a = tmp;\n  }\n  if (a.length === 0) {\n    return b.length;\n  }\n  if (a.length <= 32) {\n    return myers_32(a, b);\n  }\n  return myers_x(a, b);\n};\n\nconst closest = (str, arr) => {\n  let min_distance = Infinity;\n  let min_index = 0;\n  for (let i = 0; i < arr.length; i++) {\n    const dist = distance(str, arr[i]);\n    if (dist < min_distance) {\n      min_distance = dist;\n      min_index = i;\n    }\n  }\n  return arr[min_index];\n};\n\nmodule.exports = {\n  closest, distance\n}\n", null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null], "names": ["empty", "obj", "Object", "keys", "length", "extend", "listToExtend", "list", "push", "apply", "translate", "string", "chrMap", "tempArray", "split", "map", "char", "join", "sorted", "matches", "sort", "m1", "m2", "i", "j", "buildRankedDictionary", "orderedList", "result", "counter", "for<PERSON>ach", "word", "DATE_MAX_YEAR", "DATE_MIN_YEAR", "DATE_SPLITS", "dateSplits", "BRUTEFORCE_CARDINALITY", "MIN_GUESSES_BEFORE_GROWING_SEQUENCE", "MIN_SUBMATCH_GUESSES_SINGLE_CHAR", "MIN_SUBMATCH_GUESSES_MULTI_CHAR", "MIN_YEAR_SPACE", "START_UPPER", "END_UPPER", "ALL_UPPER", "ALL_UPPER_INVERTED", "ALL_LOWER", "ALL_LOWER_INVERTED", "ONE_UPPER", "ONE_LOWER", "ALPHA_INVERTED", "ALL_DIGIT", "REFERENCE_YEAR", "Date", "getFullYear", "REGEXEN", "recentYear", "MatchDate", "match", "password", "getMatchesWithoutSeparator", "getMatchesWithSeparator", "filteredMatches", "filterNoise", "maybeDateWithSeparator", "Math", "abs", "token", "slice", "regexMatch", "exec", "dmy", "mapIntegersToDayMonthYear", "parseInt", "pattern", "separator", "year", "month", "day", "maybeDateNoSeparator", "metric", "candidate", "candidates", "index", "splittedDates", "k", "l", "bestCandidate", "minDistance", "distance", "filter", "isSubmatch", "matchesLength", "o", "otherMatch", "integers", "over12", "over31", "under1", "len1", "int", "getDayMonth", "possibleYearSplits", "possibleYearSplitsLength", "y", "rest", "dm", "mapIntegersToDayMonth", "twoToFourDigitYear", "temp", "reverse", "data", "getUsedT<PERSON><PERSON>old", "entry", "threshold", "isPasswordToShort", "isThresholdLongerThanPassword", "shouldUsePasswordLength", "ceil", "findLevenshteinDistance", "rankedDictionary", "foundDistance", "found", "find", "usedThreshold", "foundEntryDistance", "isInThreshold", "levenshteinDistance", "levenshteinDistanceEntry", "a", "b", "c", "e", "g", "s", "t", "x", "z", "warnings", "straightRow", "keyPattern", "simpleRepeat", "extendedRepeat", "sequences", "recentYears", "dates", "topTen", "topHundred", "common", "similarToCommon", "wordByItself", "namesByThemselves", "commonNames", "userInputs", "pwned", "suggestions", "l33t", "reverseWords", "allUppercase", "capitalization", "associatedYears", "repeated", "longerKeyboardPattern", "anotherWord", "useWords", "no<PERSON><PERSON>", "timeEstimation", "ltSecond", "second", "seconds", "minute", "minutes", "hour", "hours", "days", "months", "years", "centuries", "Options", "constructor", "l33tTable", "translationKeys", "setRankedDictionaries", "setOptions", "options", "dictionary", "translations", "setTranslations", "graphs", "useLevenshteinDistance", "undefined", "levenshteinThreshold", "checkCustomTranslations", "Error", "valid", "type", "translationType", "key", "rankedDictionaries", "name", "getRankedDictionary", "sanitizedInputs", "input", "inputType", "toString", "toLowerCase", "extendUserInputsDictionary", "addMatcher", "matcher", "matchers", "console", "info", "zxcvbnOptions", "MatchL33t", "defaultMatch", "passwordReversed", "reversed", "enumeratedSubs", "enumerateL33tSubs", "relevantL33tSubtable", "sub", "subbedPassword", "matchedDictionary", "matchedWord", "matchSub", "subbedChr", "chr", "indexOf", "subDisplay", "table", "passwordChars", "subTable", "letter", "subs", "relevantSubs", "tableKeys", "getSubs", "subDict", "l33tChr", "firstKey", "restKeys", "nextSubs", "dupL33tIndex", "subExtension", "concat", "subAlternative", "splice", "newSubs", "dedup", "deduped", "members", "assoc", "label", "v", "MatchDictionary", "L33t", "Reverse", "<PERSON><PERSON><PERSON><PERSON>", "passwordLower", "dictionaryName", "rankedDict", "usedPassword", "isInDictionary", "foundLevenshteinDistance", "isFullPassword", "isLevenshteinMatch", "usedRankPassword", "rank", "MatchRegex", "regexes", "regex", "lastIndex", "regexName", "nCk", "n", "count", "<PERSON><PERSON><PERSON>", "log10", "log", "log2", "factorial", "num", "rval", "guesses", "Number", "POSITIVE_INFINITY", "MAX_VALUE", "minGuesses", "max", "yearSpace", "getVariations", "cleaned<PERSON><PERSON>", "wordArray", "upperCaseCount", "lowerCaseCount", "variations", "<PERSON><PERSON><PERSON><PERSON>", "min", "utils", "replace", "commonCases", "commonCasesLength", "getCounts", "subbed", "unsubbed", "chrs", "subbedCount", "unsubbedCount", "p", "possibilities", "baseGuesses", "uppercaseVariations", "uppercaseVariant", "l33tVariations", "l33tVariant", "reversedVariations", "calculation", "charClassBases", "alphaLower", "alphaUpper", "alpha", "alphanumeric", "digits", "symbols", "repeatCount", "ascending", "firstChr", "char<PERSON>t", "startingPoints", "includes", "calcAverageDegree", "graph", "average", "neighbors", "entries", "estimatePossiblePatterns", "turns", "startingPosition", "averageDegree", "token<PERSON><PERSON>th", "possibleTurns", "shiftedCount", "unShiftedCount", "shiftedVariations", "round", "getMinGuesses", "bruteforce", "bruteforceMatcher", "date", "date<PERSON><PERSON><PERSON>", "dictionaryMatcher", "regexMatcher", "repeat", "repeatM<PERSON>er", "sequence", "sequenceMatcher", "spatial", "spatialMatcher", "getScoring", "scoring", "extraData", "estimationResult", "matchGuesses", "guessesLog10", "<PERSON><PERSON><PERSON><PERSON>", "optimal", "excludeAdditive", "fillA<PERSON>y", "size", "valueType", "value", "makeBruteforceMatch", "update", "sequenceLength", "estimatedMatch", "estimateGuesses", "pi", "shouldSkip", "competingPatternLength", "competingMetricMatch", "m", "bruteforceUpdate", "passwordCharIndex", "tmp", "lastMatch", "unwind", "optimalMatchSequence", "candidate<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "candidate<PERSON>etric<PERSON><PERSON>", "unshift", "mostGuessableMatchSequence", "matchesByCoordinateJ", "optimalSequenceLength", "getGuesses", "MatchRepeat", "omniMatch", "greedyMatch", "getGreedyMatch", "lazyMatch", "getLazyMatch", "baseToken", "setMatchToken", "getBaseGuesses", "normalizeMatch", "hasPromises", "some", "Promise", "all", "baseMatch", "then", "resolvedBaseGuesses", "greedy", "lazy", "lazyAnchored", "resolvedMatches", "baseAnalysis", "MatchSequence", "<PERSON><PERSON><PERSON><PERSON>", "delta", "charCodeAt", "absoluteDelta", "MAX_DELTA", "sequenceName", "sequenceSpace", "getSequence", "test", "MatchSpatial", "graphName", "helper", "checkIfShifted", "SHIFTED_RX", "lastDirection", "prevChar", "adjacents", "foundDirection", "curDirection", "cur<PERSON><PERSON>", "adjacentsLength", "adjacent", "adjacentIndex", "Matching", "promises", "Matcher", "usedMatcher", "response", "resolve", "SECOND", "MINUTE", "HOUR", "DAY", "MONTH", "YEAR", "CENTURY", "times", "century", "TimeEstimates", "displayStr", "estimateAttackTimes", "crackTimesSeconds", "onlineThrottling100PerHour", "onlineNoThrottling10PerSecond", "offlineSlowHashing1e4PerSecond", "offlineFastHashing1e10PerSecond", "crackTimesDisplay", "scenario", "displayTime", "score", "guessesToScore", "DELTA", "base", "timeKeys", "foundIndex", "findIndex", "time", "warning", "getDictionaryWarningPassword", "isSoleMatch", "getDictionaryWarningWikipedia", "getDictionaryWarningNames", "getDictionaryWarning", "dictName", "isAName", "defaultFeedback", "<PERSON><PERSON><PERSON>", "setDefaultSuggestions", "getFeedback", "extraFeedback", "longestMatch", "getLongestMatch", "feedback", "getMatchFeedback", "slicedSequence", "func", "wait", "isImmediate", "timeout", "debounce", "args", "context", "later", "shouldCallNow", "clearTimeout", "setTimeout", "getTime", "createReturnValue", "start", "timeEstimates", "matchSequence", "calcTime", "attackTimes", "main", "matching", "zxcvbn", "zxcvbnAsync"], "mappings": ";;;;IAEO,MAAMA,KAAK,GAAIC,GAAD,IAAsBC,MAAM,CAACC,IAAP,CAAYF,GAAZ,EAAiBG,MAAjB,KAA4B,CAAhE;IAEA,MAAMC,MAAM,GAAG,CAACC,YAAD,EAAsBC,IAAtB;IAEpBD,YAAY,CAACE,IAAb,CAAkBC,KAAlB,CAAwBH,YAAxB,EAAsCC,IAAtC,CAFK;IAIA,MAAMG,SAAS,GAAG,CAACC,MAAD,EAAiBC,MAAjB;IACvB,QAAMC,SAAS,GAAGF,MAAM,CAACG,KAAP,CAAa,EAAb,CAAlB;IACA,SAAOD,SAAS,CAACE,GAAV,CAAeC,IAAD,IAAUJ,MAAM,CAACI,IAAD,CAAN,IAAgBA,IAAxC,EAA8CC,IAA9C,CAAmD,EAAnD,CAAP;IACD,CAHM;;IASA,MAAMC,MAAM,GAAIC,OAAD,IACpBA,OAAO,CAACC,IAAR,CAAa,CAACC,EAAD,EAAKC,EAAL,KAAYD,EAAE,CAACE,CAAH,GAAOD,EAAE,CAACC,CAAV,IAAeF,EAAE,CAACG,CAAH,GAAOF,EAAE,CAACE,CAAlD,CADK;IAGA,MAAMC,qBAAqB,GAAIC,WAAD;IACnC,QAAMC,MAAM,GAAgB,EAA5B;IACA,MAAIC,OAAO,GAAG,CAAd;;IACAF,EAAAA,WAAW,CAACG,OAAZ,CAAqBC,IAAD;IAClBH,IAAAA,MAAM,CAACG,IAAD,CAAN,GAAeF,OAAf;IACAA,IAAAA,OAAO,IAAI,CAAX;IACD,GAHD;IAIA,SAAOD,MAAP;IACD,CARM;;ACpBP,qBAAe;IACb,KAAG,CACD,CAAC,CAAD,EAAI,CAAJ,CADC,EAED,CAAC,CAAD,EAAI,CAAJ,CAFC,CADU;IAKb,KAAG,CACD,CAAC,CAAD,EAAI,CAAJ,CADC,EAED,CAAC,CAAD,EAAI,CAAJ,CAFC,CALU;IASb,KAAG,CACD,CAAC,CAAD,EAAI,CAAJ,CADC,EAED,CAAC,CAAD,EAAI,CAAJ,CAFC,EAGD,CAAC,CAAD,EAAI,CAAJ,CAHC,CATU;IAcb,KAAG,CACD,CAAC,CAAD,EAAI,CAAJ,CADC,EAED,CAAC,CAAD,EAAI,CAAJ,CAFC,EAGD,CAAC,CAAD,EAAI,CAAJ,CAHC,EAID,CAAC,CAAD,EAAI,CAAJ,CAJC,CAdU;IAoBb,KAAG,CACD,CAAC,CAAD,EAAI,CAAJ,CADC,EAED,CAAC,CAAD,EAAI,CAAJ,CAFC;IApBU,CAAf;;ICEO,MAAMI,aAAa,GAAG,IAAtB;IACA,MAAMC,aAAa,GAAG,IAAtB;IACA,MAAMC,WAAW,GAAGC,UAApB;IACA,MAAMC,sBAAsB,GAAG,EAA/B;IACA,MAAMC,mCAAmC,GAAG,KAA5C;IACA,MAAMC,gCAAgC,GAAG,EAAzC;IACA,MAAMC,+BAA+B,GAAG,EAAxC;IACA,MAAMC,cAAc,GAAG,EAAvB;;IAEA,MAAMC,WAAW,GAAG,kCAApB;IACA,MAAMC,SAAS,GAAG,kCAAlB;;IAEA,MAAMC,SAAS,GAAG,mBAAlB;IACA,MAAMC,kBAAkB,GAAG,oBAA3B;IACA,MAAMC,SAAS,GAAG,mBAAlB;IACA,MAAMC,kBAAkB,GAAG,oBAA3B;IACA,MAAMC,SAAS,GAAG,gBAAlB;IACA,MAAMC,SAAS,GAAG,gBAAlB;IACA,MAAMC,cAAc,GAAG,sBAAvB;IACA,MAAMC,SAAS,GAAG,OAAlB;IACA,MAAMC,cAAc,GAAG,IAAIC,IAAJ,GAAWC,WAAX,EAAvB;IACA,MAAMC,OAAO,GAAG;IAAEC,EAAAA,UAAU,EAAE;IAAd,CAAhB;;ICVP;;;;;;IAKA,MAAMC,SAAN;IACE;;;;;;;;;;;;;;;;;;;;IAoBAC,EAAAA,KAAK,CAAC;IAAEC,IAAAA;IAAF,GAAD;IACH,UAAMtC,OAAO,GAAgB,CAC3B,GAAG,KAAKuC,0BAAL,CAAgCD,QAAhC,CADwB,EAE3B,GAAG,KAAKE,uBAAL,CAA6BF,QAA7B,CAFwB,CAA7B;IAKA,UAAMG,eAAe,GAAG,KAAKC,WAAL,CAAiB1C,OAAjB,CAAxB;IACA,WAAOD,MAAM,CAAC0C,eAAD,CAAb;IACD;;IAEDD,EAAAA,uBAAuB,CAACF,QAAD;IACrB,UAAMtC,OAAO,GAAgB,EAA7B;IACA,UAAM2C,sBAAsB,GAAG,6CAA/B;;IAEA,SAAK,IAAIvC,CAAC,GAAG,CAAb,EAAgBA,CAAC,IAAIwC,IAAI,CAACC,GAAL,CAASP,QAAQ,CAACrD,MAAT,GAAkB,CAA3B,CAArB,EAAoDmB,CAAC,IAAI,CAAzD,EAA4D;IAC1D,WAAK,IAAIC,CAAC,GAAGD,CAAC,GAAG,CAAjB,EAAoBC,CAAC,IAAID,CAAC,GAAG,CAA7B,EAAgCC,CAAC,IAAI,CAArC,EAAwC;IACtC,YAAIA,CAAC,IAAIiC,QAAQ,CAACrD,MAAlB,EAA0B;IACxB;IACD;;IACD,cAAM6D,KAAK,GAAGR,QAAQ,CAACS,KAAT,CAAe3C,CAAf,EAAkB,CAACC,CAAD,GAAK,CAAL,IAAU,GAA5B,CAAd;IACA,cAAM2C,UAAU,GAAGL,sBAAsB,CAACM,IAAvB,CAA4BH,KAA5B,CAAnB;;IACA,YAAIE,UAAU,IAAI,IAAlB,EAAwB;IACtB,gBAAME,GAAG,GAAG,KAAKC,yBAAL,CAA+B,CACzCC,QAAQ,CAACJ,UAAU,CAAC,CAAD,CAAX,EAAgB,EAAhB,CADiC,EAEzCI,QAAQ,CAACJ,UAAU,CAAC,CAAD,CAAX,EAAgB,EAAhB,CAFiC,EAGzCI,QAAQ,CAACJ,UAAU,CAAC,CAAD,CAAX,EAAgB,EAAhB,CAHiC,CAA/B,CAAZ;;IAKA,cAAIE,GAAG,IAAI,IAAX,EAAiB;IACflD,YAAAA,OAAO,CAACX,IAAR,CAAa;IACXgE,cAAAA,OAAO,EAAE,MADE;IAEXP,cAAAA,KAFW;IAGX1C,cAAAA,CAHW;IAIXC,cAAAA,CAJW;IAKXiD,cAAAA,SAAS,EAAEN,UAAU,CAAC,CAAD,CALV;IAMXO,cAAAA,IAAI,EAAEL,GAAG,CAACK,IANC;IAOXC,cAAAA,KAAK,EAAEN,GAAG,CAACM,KAPA;IAQXC,cAAAA,GAAG,EAAEP,GAAG,CAACO;IARE,aAAb;IAUD;IACF;IACF;IACF;;IACD,WAAOzD,OAAP;IACD;;;IAGDuC,EAAAA,0BAA0B,CAACD,QAAD;IACxB,UAAMtC,OAAO,GAAgB,EAA7B;IACA,UAAM0D,oBAAoB,GAAG,WAA7B;;IACA,UAAMC,MAAM,GAAIC,SAAD,IACbhB,IAAI,CAACC,GAAL,CAASe,SAAS,CAACL,IAAV,GAAiBxB,cAA1B,CADF;;;IAGA,SAAK,IAAI3B,CAAC,GAAG,CAAb,EAAgBA,CAAC,IAAIwC,IAAI,CAACC,GAAL,CAASP,QAAQ,CAACrD,MAAT,GAAkB,CAA3B,CAArB,EAAoDmB,CAAC,IAAI,CAAzD,EAA4D;IAC1D,WAAK,IAAIC,CAAC,GAAGD,CAAC,GAAG,CAAjB,EAAoBC,CAAC,IAAID,CAAC,GAAG,CAA7B,EAAgCC,CAAC,IAAI,CAArC,EAAwC;IACtC,YAAIA,CAAC,IAAIiC,QAAQ,CAACrD,MAAlB,EAA0B;IACxB;IACD;;IACD,cAAM6D,KAAK,GAAGR,QAAQ,CAACS,KAAT,CAAe3C,CAAf,EAAkB,CAACC,CAAD,GAAK,CAAL,IAAU,GAA5B,CAAd;;IACA,YAAIqD,oBAAoB,CAACT,IAArB,CAA0BH,KAA1B,CAAJ,EAAsC;IACpC,gBAAMe,UAAU,GAAU,EAA1B;IACA,gBAAMC,KAAK,GAAGhB,KAAK,CAAC7D,MAApB;IACA,gBAAM8E,aAAa,GAAGjD,WAAW,CAACgD,KAAD,CAAjC;IACAC,UAAAA,aAAa,CAACrD,OAAd,CAAsB,CAAC,CAACsD,CAAD,EAAIC,CAAJ,CAAD;IACpB,kBAAMf,GAAG,GAAG,KAAKC,yBAAL,CAA+B,CACzCC,QAAQ,CAACN,KAAK,CAACC,KAAN,CAAY,CAAZ,EAAeiB,CAAf,CAAD,EAAoB,EAApB,CADiC,EAEzCZ,QAAQ,CAACN,KAAK,CAACC,KAAN,CAAYiB,CAAZ,EAAeC,CAAf,CAAD,EAAoB,EAApB,CAFiC,EAGzCb,QAAQ,CAACN,KAAK,CAACC,KAAN,CAAYkB,CAAZ,CAAD,EAAiB,EAAjB,CAHiC,CAA/B,CAAZ;;IAKA,gBAAIf,GAAG,IAAI,IAAX,EAAiB;IACfW,cAAAA,UAAU,CAACxE,IAAX,CAAgB6D,GAAhB;IACD;IACF,WATD;;IAUA,cAAIW,UAAU,CAAC5E,MAAX,GAAoB,CAAxB,EAA2B;IACzB;;;;;;;;;IASA,gBAAIiF,aAAa,GAAGL,UAAU,CAAC,CAAD,CAA9B;IACA,gBAAIM,WAAW,GAAGR,MAAM,CAACE,UAAU,CAAC,CAAD,CAAX,CAAxB;IACAA,YAAAA,UAAU,CAACd,KAAX,CAAiB,CAAjB,EAAoBrC,OAApB,CAA6BkD,SAAD;IAC1B,oBAAMQ,QAAQ,GAAGT,MAAM,CAACC,SAAD,CAAvB;;IACA,kBAAIQ,QAAQ,GAAGD,WAAf,EAA4B;IAC1BD,gBAAAA,aAAa,GAAGN,SAAhB;IACAO,gBAAAA,WAAW,GAAGC,QAAd;IACD;IACF,aAND;IAOApE,YAAAA,OAAO,CAACX,IAAR,CAAa;IACXgE,cAAAA,OAAO,EAAE,MADE;IAEXP,cAAAA,KAFW;IAGX1C,cAAAA,CAHW;IAIXC,cAAAA,CAJW;IAKXiD,cAAAA,SAAS,EAAE,EALA;IAMXC,cAAAA,IAAI,EAAEW,aAAa,CAACX,IANT;IAOXC,cAAAA,KAAK,EAAEU,aAAa,CAACV,KAPV;IAQXC,cAAAA,GAAG,EAAES,aAAa,CAACT;IARR,aAAb;IAUD;IACF;IACF;IACF;;IACD,WAAOzD,OAAP;IACD;IAED;;;;;;;;;;;IASA0C,EAAAA,WAAW,CAAC1C,OAAD;IACT,WAAOA,OAAO,CAACqE,MAAR,CAAgBhC,KAAD;IACpB,UAAIiC,UAAU,GAAG,KAAjB;IACA,YAAMC,aAAa,GAAGvE,OAAO,CAACf,MAA9B;;IACA,WAAK,IAAIuF,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGD,aAApB,EAAmCC,CAAC,IAAI,CAAxC,EAA2C;IACzC,cAAMC,UAAU,GAAGzE,OAAO,CAACwE,CAAD,CAA1B;;IACA,YAAInC,KAAK,KAAKoC,UAAd,EAA0B;IACxB,cAAIA,UAAU,CAACrE,CAAX,IAAgBiC,KAAK,CAACjC,CAAtB,IAA2BqE,UAAU,CAACpE,CAAX,IAAgBgC,KAAK,CAAChC,CAArD,EAAwD;IACtDiE,YAAAA,UAAU,GAAG,IAAb;IACA;IACD;IACF;IACF;;IACD,aAAO,CAACA,UAAR;IACD,KAbM,CAAP;IAcD;IAED;;;;;;;;;;IAUA;;;IACAnB,EAAAA,yBAAyB,CAACuB,QAAD;IACvB,QAAIA,QAAQ,CAAC,CAAD,CAAR,GAAc,EAAd,IAAoBA,QAAQ,CAAC,CAAD,CAAR,IAAe,CAAvC,EAA0C;IACxC,aAAO,IAAP;IACD;;IACD,QAAIC,MAAM,GAAG,CAAb;IACA,QAAIC,MAAM,GAAG,CAAb;IACA,QAAIC,MAAM,GAAG,CAAb;;IACA,SAAK,IAAIL,CAAC,GAAG,CAAR,EAAWM,IAAI,GAAGJ,QAAQ,CAACzF,MAAhC,EAAwCuF,CAAC,GAAGM,IAA5C,EAAkDN,CAAC,IAAI,CAAvD,EAA0D;IACxD,YAAMO,GAAG,GAAGL,QAAQ,CAACF,CAAD,CAApB;;IACA,UAAKO,GAAG,GAAG,EAAN,IAAYA,GAAG,GAAGlE,aAAnB,IAAqCkE,GAAG,GAAGnE,aAA/C,EAA8D;IAC5D,eAAO,IAAP;IACD;;IACD,UAAImE,GAAG,GAAG,EAAV,EAAc;IACZH,QAAAA,MAAM,IAAI,CAAV;IACD;;IACD,UAAIG,GAAG,GAAG,EAAV,EAAc;IACZJ,QAAAA,MAAM,IAAI,CAAV;IACD;;IACD,UAAII,GAAG,IAAI,CAAX,EAAc;IACZF,QAAAA,MAAM,IAAI,CAAV;IACD;IACF;;IACD,QAAID,MAAM,IAAI,CAAV,IAAeD,MAAM,KAAK,CAA1B,IAA+BE,MAAM,IAAI,CAA7C,EAAgD;IAC9C,aAAO,IAAP;IACD;;IACD,WAAO,KAAKG,WAAL,CAAiBN,QAAjB,CAAP;IACD;;;IAGDM,EAAAA,WAAW,CAACN,QAAD;IACT;IACA,UAAMO,kBAAkB,GAAyB,CAC/C,CAACP,QAAQ,CAAC,CAAD,CAAT,EAAcA,QAAQ,CAAC3B,KAAT,CAAe,CAAf,EAAkB,CAAlB,CAAd,CAD+C,EAE/C,CAAC2B,QAAQ,CAAC,CAAD,CAAT,EAAcA,QAAQ,CAAC3B,KAAT,CAAe,CAAf,EAAkB,CAAlB,CAAd,CAF+C;IAAA,KAAjD;IAIA,UAAMmC,wBAAwB,GAAGD,kBAAkB,CAAChG,MAApD;;IACA,SAAK,IAAIoB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG6E,wBAApB,EAA8C7E,CAAC,IAAI,CAAnD,EAAsD;IACpD,YAAM,CAAC8E,CAAD,EAAIC,IAAJ,IAAYH,kBAAkB,CAAC5E,CAAD,CAApC;;IACA,UAAIQ,aAAa,IAAIsE,CAAjB,IAAsBA,CAAC,IAAIvE,aAA/B,EAA8C;IAC5C,cAAMyE,EAAE,GAAG,KAAKC,qBAAL,CAA2BF,IAA3B,CAAX;;IACA,YAAIC,EAAE,IAAI,IAAV,EAAgB;IACd,iBAAO;IACL9B,YAAAA,IAAI,EAAE4B,CADD;IAEL3B,YAAAA,KAAK,EAAE6B,EAAE,CAAC7B,KAFL;IAGLC,YAAAA,GAAG,EAAE4B,EAAE,CAAC5B;IAHH,WAAP;IAKD;IACD;;;;;;;IAKA,eAAO,IAAP;IACD;IACF;IAED;;;IACA,SAAK,IAAIO,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGkB,wBAApB,EAA8ClB,CAAC,IAAI,CAAnD,EAAsD;IACpD,YAAM,CAACmB,CAAD,EAAIC,IAAJ,IAAYH,kBAAkB,CAACjB,CAAD,CAApC;IACA,YAAMqB,EAAE,GAAG,KAAKC,qBAAL,CAA2BF,IAA3B,CAAX;;IACA,UAAIC,EAAE,IAAI,IAAV,EAAgB;IACd,eAAO;IACL9B,UAAAA,IAAI,EAAE,KAAKgC,kBAAL,CAAwBJ,CAAxB,CADD;IAEL3B,UAAAA,KAAK,EAAE6B,EAAE,CAAC7B,KAFL;IAGLC,UAAAA,GAAG,EAAE4B,EAAE,CAAC5B;IAHH,SAAP;IAKD;IACF;;IACD,WAAO,IAAP;IACD;;IAED6B,EAAAA,qBAAqB,CAACZ,QAAD;IACnB,UAAMc,IAAI,GAAG,CAACd,QAAD,EAAWA,QAAQ,CAAC3B,KAAT,GAAiB0C,OAAjB,EAAX,CAAb;;IACA,SAAK,IAAIrF,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGoF,IAAI,CAACvG,MAAzB,EAAiCmB,CAAC,IAAI,CAAtC,EAAyC;IACvC,YAAMsF,IAAI,GAAGF,IAAI,CAACpF,CAAD,CAAjB;IACA,YAAMqD,GAAG,GAAGiC,IAAI,CAAC,CAAD,CAAhB;IACA,YAAMlC,KAAK,GAAGkC,IAAI,CAAC,CAAD,CAAlB;;IACA,UAAIjC,GAAG,IAAI,CAAP,IAAYA,GAAG,IAAI,EAAnB,IAAyBD,KAAK,IAAI,CAAlC,IAAuCA,KAAK,IAAI,EAApD,EAAwD;IACtD,eAAO;IACLC,UAAAA,GADK;IAELD,UAAAA;IAFK,SAAP;IAID;IACF;;IACD,WAAO,IAAP;IACD;;IAED+B,EAAAA,kBAAkB,CAAChC,IAAD;IAChB,QAAIA,IAAI,GAAG,EAAX,EAAe;IACb,aAAOA,IAAP;IACD;;IACD,QAAIA,IAAI,GAAG,EAAX,EAAe;IACb;IACA,aAAOA,IAAI,GAAG,IAAd;IACD;;;IAED,WAAOA,IAAI,GAAG,IAAd;IACD;;;;ICxRH,MAAM,GAAG,GAAG,IAAI,WAAW,CAAC,OAAO,CAAC,CAAC;IACrC,MAAM,QAAQ,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK;IAC3B,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC;IACrB,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC;IACrB,EAAE,MAAM,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;IAC3B,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC;IACd,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC;IACb,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC;IACb,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;IACZ,EAAE,OAAO,CAAC,EAAE,EAAE;IACd,IAAI,GAAG,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACnC,GAAG;IACH,EAAE,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;IAC1B,IAAI,IAAI,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;IAClC,IAAI,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;IACvB,IAAI,EAAE,IAAI,CAAC,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;IAChC,IAAI,EAAE,IAAI,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC;IACrB,IAAI,EAAE,IAAI,EAAE,CAAC;IACb,IAAI,IAAI,EAAE,GAAG,GAAG,EAAE;IAClB,MAAM,EAAE,EAAE,CAAC;IACX,KAAK;IACL,IAAI,IAAI,EAAE,GAAG,GAAG,EAAE;IAClB,MAAM,EAAE,EAAE,CAAC;IACX,KAAK;IACL,IAAI,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;IACvB,IAAI,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC,IAAI,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC;IAChC,IAAI,EAAE,IAAI,EAAE,CAAC;IACb,GAAG;IACH,EAAE,CAAC,GAAG,CAAC,CAAC;IACR,EAAE,OAAO,CAAC,EAAE,EAAE;IACd,IAAI,GAAG,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAC7B,GAAG;IACH,EAAE,OAAO,EAAE,CAAC;IACZ,CAAC,CAAC;AACF;IACA,MAAM,OAAO,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK;IAC1B,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC;IACrB,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC;IACrB,EAAE,MAAM,GAAG,GAAG,EAAE,CAAC;IACjB,EAAE,MAAM,GAAG,GAAG,EAAE,CAAC;IACjB,EAAE,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;IAClC,EAAE,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;IAClC,EAAE,IAAI,KAAK,GAAG,CAAC,CAAC;IAChB,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE;IAClC,IAAI,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IAChB,IAAI,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACf,GAAG;IACH,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;IACZ,EAAE,OAAO,CAAC,GAAG,KAAK,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;IAC7B,IAAI,IAAI,EAAE,GAAG,CAAC,CAAC;IACf,IAAI,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC;IAChB,IAAI,MAAM,KAAK,GAAG,CAAC,GAAG,EAAE,CAAC;IACzB,IAAI,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,KAAK,CAAC;IACxC,IAAI,KAAK,IAAI,CAAC,GAAG,KAAK,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;IACtC,MAAM,GAAG,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACrC,KAAK;IACL,IAAI,KAAK,GAAG,CAAC,CAAC;IACd,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;IAChC,MAAM,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;IACtC,MAAM,MAAM,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAC/C,MAAM,MAAM,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAC/C,MAAM,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;IACzB,MAAM,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;IAC1D,MAAM,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC;IAC/B,MAAM,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;IACvB,MAAM,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,EAAE;IAC5B,QAAQ,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACpC,OAAO;IACP,MAAM,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,EAAE;IAC5B,QAAQ,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACpC,OAAO;IACP,MAAM,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC;IAC1B,MAAM,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC;IAC1B,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC;IAC3B,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;IACnB,KAAK;IACL,IAAI,KAAK,IAAI,CAAC,GAAG,KAAK,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;IACtC,MAAM,GAAG,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAC/B,KAAK;IACL,GAAG;IACH,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC;IACb,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC;IACd,EAAE,MAAM,KAAK,GAAG,CAAC,GAAG,EAAE,CAAC;IACvB,EAAE,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,CAAC,GAAG,KAAK,CAAC;IAC9C,EAAE,KAAK,IAAI,CAAC,GAAG,KAAK,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;IACpC,IAAI,GAAG,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACnC,GAAG;IACH,EAAE,KAAK,GAAG,CAAC,CAAC;IACZ,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;IAC9B,IAAI,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;IACpC,IAAI,MAAM,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAC7C,IAAI,MAAM,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAC7C,IAAI,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;IACvB,IAAI,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;IACxD,IAAI,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC;IAC7B,IAAI,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;IACrB,IAAI,KAAK,IAAI,CAAC,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;IAClC,IAAI,KAAK,IAAI,CAAC,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;IAClC,IAAI,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,EAAE;IAC1B,MAAM,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAClC,KAAK;IACL,IAAI,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,EAAE;IAC1B,MAAM,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAClC,KAAK;IACL,IAAI,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC;IACxB,IAAI,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC;IACxB,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC;IACzB,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;IACjB,GAAG;IACH,EAAE,KAAK,IAAI,CAAC,GAAG,KAAK,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;IACpC,IAAI,GAAG,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAC7B,GAAG;IACH,EAAE,OAAO,KAAK,CAAC;IACf,CAAC,CAAC;AACF;IACA,MAAM,QAAQ,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK;IAC3B,EAAE,IAAI,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,EAAE;IAC3B,IAAI,MAAM,GAAG,GAAG,CAAC,CAAC;IAClB,IAAI,CAAC,GAAG,CAAC,CAAC;IACV,IAAI,CAAC,GAAG,GAAG,CAAC;IACZ,GAAG;IACH,EAAE,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE;IACtB,IAAI,OAAO,CAAC,CAAC,MAAM,CAAC;IACpB,GAAG;IACH,EAAE,IAAI,CAAC,CAAC,MAAM,IAAI,EAAE,EAAE;IACtB,IAAI,OAAO,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC1B,GAAG;IACH,EAAE,OAAO,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACvB,CAAC,CAAC;AACF;IACA,MAAM,OAAO,GAAG,CAAC,GAAG,EAAE,GAAG,KAAK;IAC9B,EAAE,IAAI,YAAY,GAAG,QAAQ,CAAC;IAC9B,EAAE,IAAI,SAAS,GAAG,CAAC,CAAC;IACpB,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;IACvC,IAAI,MAAM,IAAI,GAAG,QAAQ,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IACvC,IAAI,IAAI,IAAI,GAAG,YAAY,EAAE;IAC7B,MAAM,YAAY,GAAG,IAAI,CAAC;IAC1B,MAAM,SAAS,GAAG,CAAC,CAAC;IACpB,KAAK;IACL,GAAG;IACH,EAAE,OAAO,GAAG,CAAC,SAAS,CAAC,CAAC;IACxB,CAAC,CAAC;AACF;QACA,kBAAc,GAAG;IACjB,EAAE,OAAO,EAAE,QAAQ;IACnB;;IC/IA,MAAMoC,gBAAgB,GAAG,CACvBrD,QADuB,EAEvBsD,KAFuB,EAGvBC,SAHuB;IAKvB,QAAMC,iBAAiB,GAAGxD,QAAQ,CAACrD,MAAT,IAAmB2G,KAAK,CAAC3G,MAAnD;IACA,QAAM8G,6BAA6B,GAAGzD,QAAQ,CAACrD,MAAT,IAAmB4G,SAAzD;IACA,QAAMG,uBAAuB,GAC3BF,iBAAiB,IAAIC,6BADvB;;IAIA,SAAOC,uBAAuB,GAAGpD,IAAI,CAACqD,IAAL,CAAU3D,QAAQ,CAACrD,MAAT,GAAkB,CAA5B,CAAH,GAAoC4G,SAAlE;IACD,CAZD;;IAmBA,MAAMK,uBAAuB,GAAG,CAC9B5D,QAD8B,EAE9B6D,gBAF8B,EAG9BN,SAH8B;IAK9B,MAAIO,aAAa,GAAG,CAApB;IACA,QAAMC,KAAK,GAAGtH,MAAM,CAACC,IAAP,CAAYmH,gBAAZ,EAA8BG,IAA9B,CAAoCV,KAAD;IAC/C,UAAMW,aAAa,GAAGZ,gBAAgB,CAACrD,QAAD,EAAWsD,KAAX,EAAkBC,SAAlB,CAAtC;IACA,UAAMW,kBAAkB,GAAGpC,2BAAQ,CAAC9B,QAAD,EAAWsD,KAAX,CAAnC;IACA,UAAMa,aAAa,GAAGD,kBAAkB,IAAID,aAA5C;;IAEA,QAAIE,aAAJ,EAAmB;IACjBL,MAAAA,aAAa,GAAGI,kBAAhB;IACD;;IACD,WAAOC,aAAP;IACD,GATa,CAAd;;IAUA,MAAIJ,KAAJ,EAAW;IACT,WAAO;IACLK,MAAAA,mBAAmB,EAAEN,aADhB;IAELO,MAAAA,wBAAwB,EAAEN;IAFrB,KAAP;IAID;;IACD,SAAO,EAAP;IACD,CAvBD;;ACtBA,oBAAe;IACbO,EAAAA,CAAC,EAAE,CAAC,GAAD,EAAM,GAAN,CADU;IAEbC,EAAAA,CAAC,EAAE,CAAC,GAAD,CAFU;IAGbC,EAAAA,CAAC,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,EAAgB,GAAhB,CAHU;IAIbC,EAAAA,CAAC,EAAE,CAAC,GAAD,CAJU;IAKbC,EAAAA,CAAC,EAAE,CAAC,GAAD,EAAM,GAAN,CALU;IAMb5G,EAAAA,CAAC,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CANU;IAOb6D,EAAAA,CAAC,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAPU;IAQbO,EAAAA,CAAC,EAAE,CAAC,GAAD,CARU;IASbyC,EAAAA,CAAC,EAAE,CAAC,GAAD,EAAM,GAAN,CATU;IAUbC,EAAAA,CAAC,EAAE,CAAC,GAAD,EAAM,GAAN,CAVU;IAWbC,EAAAA,CAAC,EAAE,CAAC,GAAD,CAXU;IAYbC,EAAAA,CAAC,EAAE,CAAC,GAAD;IAZU,CAAf;;ACAA,0BAAe;IACbC,EAAAA,QAAQ,EAAE;IACRC,IAAAA,WAAW,EAAE,aADL;IAERC,IAAAA,UAAU,EAAE,YAFJ;IAGRC,IAAAA,YAAY,EAAE,cAHN;IAIRC,IAAAA,cAAc,EAAE,gBAJR;IAKRC,IAAAA,SAAS,EAAE,WALH;IAMRC,IAAAA,WAAW,EAAE,aANL;IAORC,IAAAA,KAAK,EAAE,OAPC;IAQRC,IAAAA,MAAM,EAAE,QARA;IASRC,IAAAA,UAAU,EAAE,YATJ;IAURC,IAAAA,MAAM,EAAE,QAVA;IAWRC,IAAAA,eAAe,EAAE,iBAXT;IAYRC,IAAAA,YAAY,EAAE,cAZN;IAaRC,IAAAA,iBAAiB,EAAE,mBAbX;IAcRC,IAAAA,WAAW,EAAE,aAdL;IAeRC,IAAAA,UAAU,EAAE,YAfJ;IAgBRC,IAAAA,KAAK,EAAE;IAhBC,GADG;IAmBbC,EAAAA,WAAW,EAAE;IACXC,IAAAA,IAAI,EAAE,MADK;IAEXC,IAAAA,YAAY,EAAE,cAFH;IAGXC,IAAAA,YAAY,EAAE,cAHH;IAIXC,IAAAA,cAAc,EAAE,gBAJL;IAKXd,IAAAA,KAAK,EAAE,OALI;IAMXD,IAAAA,WAAW,EAAE,aANF;IAOXgB,IAAAA,eAAe,EAAE,iBAPN;IAQXjB,IAAAA,SAAS,EAAE,WARA;IASXkB,IAAAA,QAAQ,EAAE,UATC;IAUXC,IAAAA,qBAAqB,EAAE,uBAVZ;IAWXC,IAAAA,WAAW,EAAE,aAXF;IAYXC,IAAAA,QAAQ,EAAE,UAZC;IAaXC,IAAAA,MAAM,EAAE,QAbG;IAcXX,IAAAA,KAAK,EAAE;IAdI,GAnBA;IAmCbY,EAAAA,cAAc,EAAE;IACdC,IAAAA,QAAQ,EAAE,UADI;IAEdC,IAAAA,MAAM,EAAE,QAFM;IAGdC,IAAAA,OAAO,EAAE,SAHK;IAIdC,IAAAA,MAAM,EAAE,QAJM;IAKdC,IAAAA,OAAO,EAAE,SALK;IAMdC,IAAAA,IAAI,EAAE,MANQ;IAOdC,IAAAA,KAAK,EAAE,OAPO;IAQd/F,IAAAA,GAAG,EAAE,KARS;IASdgG,IAAAA,IAAI,EAAE,MATQ;IAUdjG,IAAAA,KAAK,EAAE,OAVO;IAWdkG,IAAAA,MAAM,EAAE,QAXM;IAYdnG,IAAAA,IAAI,EAAE,MAZQ;IAadoG,IAAAA,KAAK,EAAE,OAbO;IAcdC,IAAAA,SAAS,EAAE;IAdG;IAnCH,CAAf;;UCcaC;IAqBXC,EAAAA;IApBA,iBAAA,GAAqB,EAArB;IAEA,kBAAA,GAA8BC,SAA9B;IAEA,mBAAA,GAAgC;IAC9B3B,MAAAA,UAAU,EAAE;IADkB,KAAhC;IAIA,2BAAA,GAAyC,EAAzC;IAEA,qBAAA,GAAgC4B,eAAhC;IAEA,eAAA,GAAuB,EAAvB;IAEA,wBAAA,GAA4B,EAA5B;IAEA,+BAAA,GAAkC,KAAlC;IAEA,6BAAA,GAA+B,CAA/B;IAGE,SAAKC,qBAAL;IACD;;IAEDC,EAAAA,UAAU,CAACC,UAAuB,EAAxB;IACR,QAAIA,OAAO,CAACJ,SAAZ,EAAuB;IACrB,WAAKA,SAAL,GAAiBI,OAAO,CAACJ,SAAzB;IACD;;IAED,QAAII,OAAO,CAACC,UAAZ,EAAwB;IACtB,WAAKA,UAAL,GAAkBD,OAAO,CAACC,UAA1B;IAEA,WAAKH,qBAAL;IACD;;IAED,QAAIE,OAAO,CAACE,YAAZ,EAA0B;IACxB,WAAKC,eAAL,CAAqBH,OAAO,CAACE,YAA7B;IACD;;IAED,QAAIF,OAAO,CAACI,MAAZ,EAAoB;IAClB,WAAKA,MAAL,GAAcJ,OAAO,CAACI,MAAtB;IACD;;IAED,QAAIJ,OAAO,CAACK,sBAAR,KAAmCC,SAAvC,EAAkD;IAChD,WAAKD,sBAAL,GAA8BL,OAAO,CAACK,sBAAtC;IACD;;IAED,QAAIL,OAAO,CAACO,oBAAR,KAAiCD,SAArC,EAAgD;IAC9C,WAAKC,oBAAL,GAA4BP,OAAO,CAACO,oBAApC;IACD;IACF;;IAEDJ,EAAAA,eAAe,CAACD,YAAD;IACb,QAAI,KAAKM,uBAAL,CAA6BN,YAA7B,CAAJ,EAAgD;IAC9C,WAAKA,YAAL,GAAoBA,YAApB;IACD,KAFD,MAEO;IACL,YAAM,IAAIO,KAAJ,CAAU,8CAAV,CAAN;IACD;IACF;;IAEDD,EAAAA,uBAAuB,CAACN,YAAD;IACrB,QAAIQ,KAAK,GAAG,IAAZ;IACA9L,IAAAA,MAAM,CAACC,IAAP,CAAYgL,eAAZ,EAA6BtJ,OAA7B,CAAsCoK,IAAD;IACnC,UAAIA,IAAI,IAAIT,YAAZ,EAA0B;IACxB,cAAMU,eAAe,GAAGD,IAAxB;IACA/L,QAAAA,MAAM,CAACC,IAAP,CAAYgL,eAAe,CAACe,eAAD,CAA3B,EAA8CrK,OAA9C,CAAuDsK,GAAD;IACpD,cAAI,EAAEA,GAAG,IAAIX,YAAY,CAACU,eAAD,CAArB,CAAJ,EAA6C;IAC3CF,YAAAA,KAAK,GAAG,KAAR;IACD;IACF,SAJD;IAKD,OAPD,MAOO;IACLA,QAAAA,KAAK,GAAG,KAAR;IACD;IACF,KAXD;IAYA,WAAOA,KAAP;IACD;;IAEDZ,EAAAA,qBAAqB;IACnB,UAAMgB,kBAAkB,GAAuB,EAA/C;IACAlM,IAAAA,MAAM,CAACC,IAAP,CAAY,KAAKoL,UAAjB,EAA6B1J,OAA7B,CAAsCwK,IAAD;IACnCD,MAAAA,kBAAkB,CAACC,IAAD,CAAlB,GAA2B,KAAKC,mBAAL,CAAyBD,IAAzB,CAA3B;IACD,KAFD;IAGA,SAAKD,kBAAL,GAA0BA,kBAA1B;IACD;;IAEDE,EAAAA,mBAAmB,CAACD,IAAD;IACjB,UAAM9L,IAAI,GAAG,KAAKgL,UAAL,CAAgBc,IAAhB,CAAb;;IACA,QAAIA,IAAI,KAAK,YAAb,EAA2B;IACzB,YAAME,eAAe,GAAa,EAAlC;IAEAhM,MAAAA,IAAI,CAACsB,OAAL,CAAc2K,KAAD;IACX,cAAMC,SAAS,GAAG,OAAOD,KAAzB;;IACA,YACEC,SAAS,KAAK,QAAd,IACAA,SAAS,KAAK,QADd,IAEAA,SAAS,KAAK,SAHhB,EAIE;IACAF,UAAAA,eAAe,CAAC/L,IAAhB,CAAqBgM,KAAK,CAACE,QAAN,GAAiBC,WAAjB,EAArB;IACD;IACF,OATD;IAWA,aAAOlL,qBAAqB,CAAC8K,eAAD,CAA5B;IACD;;IACD,WAAO9K,qBAAqB,CAAClB,IAAD,CAA5B;IACD;;IAEDqM,EAAAA,0BAA0B,CAACrB,UAAD;IACxB,QAAI,KAAKA,UAAL,CAAgBhC,UAApB,EAAgC;IAC9B,WAAKgC,UAAL,CAAgBhC,UAAhB,GAA6B,CAC3B,GAAG,KAAKgC,UAAL,CAAgBhC,UADQ,EAE3B,GAAGgC,UAFwB,CAA7B;IAID,KALD,MAKO;IACL,WAAKA,UAAL,CAAgBhC,UAAhB,GAA6BgC,UAA7B;IACD;;IAED,SAAKa,kBAAL,CAAwB7C,UAAxB,GAAqC,KAAK+C,mBAAL,CAAyB,YAAzB,CAArC;IACD;;IAEMO,EAAAA,UAAU,CAACR,IAAD,EAAeS,OAAf;IACf,QAAI,KAAKC,QAAL,CAAcV,IAAd,CAAJ,EAAyB;IACvBW,MAAAA,OAAO,CAACC,IAAR,CAAa,wBAAb;IACD,KAFD,MAEO;IACL,WAAKF,QAAL,CAAcV,IAAd,IAAsBS,OAAtB;IACD;IACF;;;UAGGI,aAAa,GAAG,IAAIlC,OAAJ;;IC7ItB;;;;;IAKA,MAAMmC,WAAN;IAGElC,EAAAA,YAAYmC;IACV,SAAKA,YAAL,GAAoBA,YAApB;IACD;;IAED5J,EAAAA,KAAK,CAAC;IAAEC,IAAAA;IAAF,GAAD;IACH,UAAM4J,gBAAgB,GAAG5J,QAAQ,CAAC3C,KAAT,CAAe,EAAf,EAAmB8F,OAAnB,GAA6B3F,IAA7B,CAAkC,EAAlC,CAAzB;IACA,WAAO,KAAKmM,YAAL,CAAkB;IACvB3J,MAAAA,QAAQ,EAAE4J;IADa,KAAlB,EAEJtM,GAFI,CAECyC,KAAD,KAA6B,EAClC,GAAGA,KAD+B;IAElCS,MAAAA,KAAK,EAAET,KAAK,CAACS,KAAN,CAAYnD,KAAZ,CAAkB,EAAlB,EAAsB8F,OAAtB,GAAgC3F,IAAhC,CAAqC,EAArC,CAF2B;IAGlCqM,MAAAA,QAAQ,EAAE,IAHwB;IAIlC;IACA/L,MAAAA,CAAC,EAAEkC,QAAQ,CAACrD,MAAT,GAAkB,CAAlB,GAAsBoD,KAAK,CAAChC,CALG;IAMlCA,MAAAA,CAAC,EAAEiC,QAAQ,CAACrD,MAAT,GAAkB,CAAlB,GAAsBoD,KAAK,CAACjC;IANG,KAA7B,CAFA,CAAP;IAUD;;;;ICfH;;;;;;IAKA,MAAM4L,SAAN;IAGElC,EAAAA,YAAYmC;IACV,SAAKA,YAAL,GAAoBA,YAApB;IACD;;IAED5J,EAAAA,KAAK,CAAC;IAAEC,IAAAA;IAAF,GAAD;IACH,UAAMtC,OAAO,GAAgB,EAA7B;IACA,UAAMoM,cAAc,GAAG,KAAKC,iBAAL,CACrB,KAAKC,oBAAL,CAA0BhK,QAA1B,EAAoCyJ,aAAa,CAAChC,SAAlD,CADqB,CAAvB;;IAGA,SAAK,IAAI3J,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGgM,cAAc,CAACnN,MAAnC,EAA2CmB,CAAC,IAAI,CAAhD,EAAmD;IACjD,YAAMmM,GAAG,GAAGH,cAAc,CAAChM,CAAD,CAA1B,CADiD;;IAGjD,UAAIvB,KAAK,CAAC0N,GAAD,CAAT,EAAgB;IACd;IACD;;IACD,YAAMC,cAAc,GAAGjN,SAAS,CAAC+C,QAAD,EAAWiK,GAAX,CAAhC;IACA,YAAME,iBAAiB,GAAG,KAAKR,YAAL,CAAkB;IAC1C3J,QAAAA,QAAQ,EAAEkK;IADgC,OAAlB,CAA1B;IAGAC,MAAAA,iBAAiB,CAAC/L,OAAlB,CAA2B2B,KAAD;IACxB,cAAMS,KAAK,GAAGR,QAAQ,CAACS,KAAT,CAAeV,KAAK,CAACjC,CAArB,EAAwB,CAACiC,KAAK,CAAChC,CAAP,GAAW,CAAX,IAAgB,GAAxC,CAAd;;IAEA,YAAIyC,KAAK,CAAC0I,WAAN,OAAwBnJ,KAAK,CAACqK,WAAlC,EAA+C;IAC7C;IACA,gBAAMC,QAAQ,GAAgB,EAA9B;IACA5N,UAAAA,MAAM,CAACC,IAAP,CAAYuN,GAAZ,EAAiB7L,OAAjB,CAA0BkM,SAAD;IACvB,kBAAMC,GAAG,GAAGN,GAAG,CAACK,SAAD,CAAf;;IACA,gBAAI9J,KAAK,CAACgK,OAAN,CAAcF,SAAd,MAA6B,CAAC,CAAlC,EAAqC;IACnCD,cAAAA,QAAQ,CAACC,SAAD,CAAR,GAAsBC,GAAtB;IACD;IACF,WALD;IAMA,gBAAME,UAAU,GAAGhO,MAAM,CAACC,IAAP,CAAY2N,QAAZ,EAChB/M,GADgB,CACXoE,CAAD,OAAUA,QAAQ2I,QAAQ,CAAC3I,CAAD,GADd,EAEhBlE,IAFgB,CAEX,IAFW,CAAnB;IAGAE,UAAAA,OAAO,CAACX,IAAR,CAAa,EACX,GAAGgD,KADQ;IAEXkG,YAAAA,IAAI,EAAE,IAFK;IAGXzF,YAAAA,KAHW;IAIXyJ,YAAAA,GAAG,EAAEI,QAJM;IAKXI,YAAAA;IALW,WAAb;IAOD;IACF,OAvBD;IAwBD;IAED;IACA;;;IACA,WAAO/M,OAAO,CAACqE,MAAR,CAAgBhC,KAAD,IAAWA,KAAK,CAACS,KAAN,CAAY7D,MAAZ,GAAqB,CAA/C,CAAP;IACD;;;IAGDqN,EAAAA,oBAAoB,CAAChK,QAAD,EAAmB0K,KAAnB;IAClB,UAAMC,aAAa,GAAgB,EAAnC;IACA,UAAMC,QAAQ,GAAgB,EAA9B;IACA5K,IAAAA,QAAQ,CAAC3C,KAAT,CAAe,EAAf,EAAmBe,OAAnB,CAA4Bb,IAAD;IACzBoN,MAAAA,aAAa,CAACpN,IAAD,CAAb,GAAsB,IAAtB;IACD,KAFD;IAIAd,IAAAA,MAAM,CAACC,IAAP,CAAYgO,KAAZ,EAAmBtM,OAAnB,CAA4ByM,MAAD;IACzB,YAAMC,IAAI,GAAGJ,KAAK,CAACG,MAAD,CAAlB;IACA,YAAME,YAAY,GAAGD,IAAI,CAAC/I,MAAL,CAAakI,GAAD,IAAiBA,GAAG,IAAIU,aAApC,CAArB;;IACA,UAAII,YAAY,CAACpO,MAAb,GAAsB,CAA1B,EAA6B;IAC3BiO,QAAAA,QAAQ,CAACC,MAAD,CAAR,GAAmBE,YAAnB;IACD;IACF,KAND;IAOA,WAAOH,QAAP;IACD;;;IAGDb,EAAAA,iBAAiB,CAACW,KAAD;IACf,UAAMM,SAAS,GAAGvO,MAAM,CAACC,IAAP,CAAYgO,KAAZ,CAAlB;IACA,UAAMI,IAAI,GAAG,KAAKG,OAAL,CAAaD,SAAb,EAAwB,CAAC,EAAD,CAAxB,EAA8BN,KAA9B,CAAb;;IAEA,WAAOI,IAAI,CAACxN,GAAL,CAAU2M,GAAD;IACd,YAAMiB,OAAO,GAAgB,EAA7B;IACAjB,MAAAA,GAAG,CAAC7L,OAAJ,CAAY,CAAC,CAAC+M,OAAD,EAAUZ,GAAV,CAAD;IACVW,QAAAA,OAAO,CAACC,OAAD,CAAP,GAAmBZ,GAAnB;IACD,OAFD;IAGA,aAAOW,OAAP;IACD,KANM,CAAP;IAOD;;IAEDD,EAAAA,OAAO,CAACvO,IAAD,EAAiBoO,IAAjB,EAA6BJ,KAA7B;IACL,QAAI,CAAChO,IAAI,CAACC,MAAV,EAAkB;IAChB,aAAOmO,IAAP;IACD;;IACD,UAAMM,QAAQ,GAAG1O,IAAI,CAAC,CAAD,CAArB;IACA,UAAM2O,QAAQ,GAAG3O,IAAI,CAAC+D,KAAL,CAAW,CAAX,CAAjB;IACA,UAAM6K,QAAQ,GAAS,EAAvB;IACAZ,IAAAA,KAAK,CAACU,QAAD,CAAL,CAAsChN,OAAtC,CAA+C+M,OAAD;IAC5CL,MAAAA,IAAI,CAAC1M,OAAL,CAAc6L,GAAD;IACX,YAAIsB,YAAY,GAAG,CAAC,CAApB;;IACA,aAAK,IAAIzN,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGmM,GAAG,CAACtN,MAAxB,EAAgCmB,CAAC,IAAI,CAArC,EAAwC;IACtC,cAAImM,GAAG,CAACnM,CAAD,CAAH,CAAO,CAAP,MAAcqN,OAAlB,EAA2B;IACzBI,YAAAA,YAAY,GAAGzN,CAAf;IACA;IACD;IACF;;IACD,YAAIyN,YAAY,KAAK,CAAC,CAAtB,EAAyB;IACvB,gBAAMC,YAAY,GAAGvB,GAAG,CAACwB,MAAJ,CAAW,CAAC,CAACN,OAAD,EAAUC,QAAV,CAAD,CAAX,CAArB;IACAE,UAAAA,QAAQ,CAACvO,IAAT,CAAcyO,YAAd;IACD,SAHD,MAGO;IACL,gBAAME,cAAc,GAAGzB,GAAG,CAACxJ,KAAJ,CAAU,CAAV,CAAvB;IACAiL,UAAAA,cAAc,CAACC,MAAf,CAAsBJ,YAAtB,EAAoC,CAApC;IACAG,UAAAA,cAAc,CAAC3O,IAAf,CAAoB,CAACoO,OAAD,EAAUC,QAAV,CAApB;IACAE,UAAAA,QAAQ,CAACvO,IAAT,CAAckN,GAAd;IACAqB,UAAAA,QAAQ,CAACvO,IAAT,CAAc2O,cAAd;IACD;IACF,OAlBD;IAmBD,KApBD;IAqBA,UAAME,OAAO,GAAG,KAAKC,KAAL,CAAWP,QAAX,CAAhB;;IACA,QAAID,QAAQ,CAAC1O,MAAb,EAAqB;IACnB,aAAO,KAAKsO,OAAL,CAAaI,QAAb,EAAuBO,OAAvB,EAAgClB,KAAhC,CAAP;IACD;;IACD,WAAOkB,OAAP;IACD;;IAEDC,EAAAA,KAAK,CAACf,IAAD;IACH,UAAMgB,OAAO,GAAS,EAAtB;IACA,UAAMC,OAAO,GAAgB,EAA7B;IACAjB,IAAAA,IAAI,CAAC1M,OAAL,CAAc6L,GAAD;IACX,YAAM+B,KAAK,GAAG/B,GAAG,CAAC3M,GAAJ,CAAQ,CAACoE,CAAD,EAAIF,KAAJ,KAAc,CAACE,CAAD,EAAIF,KAAJ,CAAtB,CAAd;IACAwK,MAAAA,KAAK,CAACrO,IAAN;IACA,YAAMsO,KAAK,GAAGD,KAAK,CAAC1O,GAAN,CAAU,CAAC,CAACoE,CAAD,EAAIwK,CAAJ,CAAD,QAAexK,KAAKwK,GAA9B,EAAmC1O,IAAnC,CAAwC,GAAxC,CAAd;;IACA,UAAI,EAAEyO,KAAK,IAAIF,OAAX,CAAJ,EAAyB;IACvBA,QAAAA,OAAO,CAACE,KAAD,CAAP,GAAiB,IAAjB;IACAH,QAAAA,OAAO,CAAC/O,IAAR,CAAakN,GAAb;IACD;IACF,KARD;IASA,WAAO6B,OAAP;IACD;;;;ICxIH,MAAMK,eAAN;IAKE3E,EAAAA;IACE,SAAKvB,IAAL,GAAY,IAAImG,SAAJ,CAAS,KAAKzC,YAAd,CAAZ;IACA,SAAKxG,OAAL,GAAe,IAAIkJ,WAAJ,CAAY,KAAK1C,YAAjB,CAAf;IACD;;IAED5J,EAAAA,KAAK,CAAC;IAAEC,IAAAA;IAAF,GAAD;IACH,UAAMtC,OAAO,GAAG,CACd,GAAI,KAAKiM,YAAL,CAAkB;IAAE3J,MAAAA;IAAF,KAAlB,CADU,EAEd,GAAI,KAAKmD,OAAL,CAAapD,KAAb,CAAmB;IAAEC,MAAAA;IAAF,KAAnB,CAFU,EAGd,GAAI,KAAKiG,IAAL,CAAUlG,KAAV,CAAgB;IAAEC,MAAAA;IAAF,KAAhB,CAHU,CAAhB;IAKA,WAAOvC,MAAM,CAACC,OAAD,CAAb;IACD;;IAEDiM,EAAAA,YAAY,CAAC;IAAE3J,IAAAA;IAAF,GAAD;IACV,UAAMtC,OAAO,GAAsB,EAAnC;IACA,UAAM4O,cAAc,GAAGtM,QAAQ,CAACrD,MAAhC;IACA,UAAM4P,aAAa,GAAGvM,QAAQ,CAACkJ,WAAT,EAAtB;;IAGAzM,IAAAA,MAAM,CAACC,IAAP,CAAY+M,aAAa,CAACd,kBAA1B,EAA8CvK,OAA9C,CAAuDoO,cAAD;IACpD,YAAMC,UAAU,GACdhD,aAAa,CAACd,kBAAd,CAAiC6D,cAAjC,CADF;;IAEA,WAAK,IAAI1O,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGwO,cAApB,EAAoCxO,CAAC,IAAI,CAAzC,EAA4C;IAC1C,aAAK,IAAIC,CAAC,GAAGD,CAAb,EAAgBC,CAAC,GAAGuO,cAApB,EAAoCvO,CAAC,IAAI,CAAzC,EAA4C;IAC1C,gBAAM2O,YAAY,GAAGH,aAAa,CAAC9L,KAAd,CAAoB3C,CAApB,EAAuB,CAACC,CAAD,GAAK,CAAL,IAAU,GAAjC,CAArB;IACA,gBAAM4O,cAAc,IAAGD,YAAY,IAAID,UAAnB,CAApB;IACA,cAAIG,wBAAwB,GAC1B,EADF,CAH0C;IAM1C;;IACA,gBAAMC,cAAc,GAAG/O,CAAC,KAAK,CAAN,IAAWC,CAAC,KAAKuO,cAAc,GAAG,CAAzD;;IACA,cACE7C,aAAa,CAACvB,sBAAd,IACA2E,cADA,IAEA,CAACF,cAHH,EAIE;IACAC,YAAAA,wBAAwB,GAAGhJ,uBAAuB,CAChD8I,YADgD,EAEhDD,UAFgD,EAGhDhD,aAAa,CAACrB,oBAHkC,CAAlD;IAKD;;IACD,gBAAM0E,kBAAkB,GACtBrQ,MAAM,CAACC,IAAP,CAAYkQ,wBAAZ,EAAsCjQ,MAAtC,KAAiD,CADnD;;IAGA,cAAIgQ,cAAc,IAAIG,kBAAtB,EAA0C;IACxC,kBAAMC,gBAAgB,GAAGD,kBAAkB,GACtCF,wBAAwB,CAACvI,wBADa,GAEvCqI,YAFJ;IAIA,kBAAMM,IAAI,GAAGP,UAAU,CAACM,gBAAD,CAAvB;IACArP,YAAAA,OAAO,CAACX,IAAR,CAAa;IACXgE,cAAAA,OAAO,EAAE,YADE;IAEXjD,cAAAA,CAFW;IAGXC,cAAAA,CAHW;IAIXyC,cAAAA,KAAK,EAAER,QAAQ,CAACS,KAAT,CAAe3C,CAAf,EAAkB,CAACC,CAAD,GAAK,CAAL,IAAU,GAA5B,CAJI;IAKXqM,cAAAA,WAAW,EAAEsC,YALF;IAMXM,cAAAA,IANW;IAOXR,cAAAA,cAAc,EAAEA,cAPL;IAQX3C,cAAAA,QAAQ,EAAE,KARC;IASX5D,cAAAA,IAAI,EAAE,KATK;IAUX,iBAAG2G;IAVQ,aAAb;IAYD;IACF;IACF;IACF,KA/CD;IAgDA,WAAOlP,OAAP;IACD;;;;IC7EH;;;;;;IAKA,MAAMuP,UAAN;IACElN,EAAAA,KAAK,CAAC;IAAEC,IAAAA,QAAF;IAAYkN,IAAAA,OAAO,GAAGtN;IAAtB,GAAD;IACH,UAAMlC,OAAO,GAAiB,EAA9B;IACAjB,IAAAA,MAAM,CAACC,IAAP,CAAYwQ,OAAZ,EAAqB9O,OAArB,CAA8BwK,IAAD;IAC3B,YAAMuE,KAAK,GAAGD,OAAO,CAACtE,IAAD,CAArB;IACAuE,MAAAA,KAAK,CAACC,SAAN,GAAkB,CAAlB;;IACA,YAAM1M,UAAU,GAAGyM,KAAK,CAACxM,IAAN,CAAWX,QAAX,CAAnB;;IACA,UAAIU,UAAJ,EAAgB;IACd,cAAMF,KAAK,GAAGE,UAAU,CAAC,CAAD,CAAxB;IACAhD,QAAAA,OAAO,CAACX,IAAR,CAAa;IACXgE,UAAAA,OAAO,EAAE,OADE;IAEXP,UAAAA,KAFW;IAGX1C,UAAAA,CAAC,EAAE4C,UAAU,CAACc,KAHH;IAIXzD,UAAAA,CAAC,EAAE2C,UAAU,CAACc,KAAX,GAAmBd,UAAU,CAAC,CAAD,CAAV,CAAc/D,MAAjC,GAA0C,CAJlC;IAKX0Q,UAAAA,SAAS,EAAEzE,IALA;IAMXlI,UAAAA;IANW,SAAb;IAQD;IACF,KAfD;IAgBA,WAAOjD,MAAM,CAACC,OAAD,CAAb;IACD;;;;ACnCH,gBAAe;IACb;IACA;IACA4P,EAAAA,GAAG,CAACC,CAAD,EAAY7L,CAAZ;IACD,QAAI8L,KAAK,GAAGD,CAAZ;;IACA,QAAI7L,CAAC,GAAG8L,KAAR,EAAe;IACb,aAAO,CAAP;IACD;;IACD,QAAI9L,CAAC,KAAK,CAAV,EAAa;IACX,aAAO,CAAP;IACD;;IACD,QAAI+L,KAAK,GAAG,CAAZ;;IACA,SAAK,IAAI3P,CAAC,GAAG,CAAb,EAAgBA,CAAC,IAAI4D,CAArB,EAAwB5D,CAAC,IAAI,CAA7B,EAAgC;IAC9B2P,MAAAA,KAAK,IAAID,KAAT;IACAC,MAAAA,KAAK,IAAI3P,CAAT;IACA0P,MAAAA,KAAK,IAAI,CAAT;IACD;;IACD,WAAOC,KAAP;IACD,GAlBY;;IAmBbC,EAAAA,KAAK,CAACH,CAAD;IACH,WAAOjN,IAAI,CAACqN,GAAL,CAASJ,CAAT,IAAcjN,IAAI,CAACqN,GAAL,CAAS,EAAT,CAArB;IACD,GArBY;;IAsBbC,EAAAA,IAAI,CAACL,CAAD;IACF,WAAOjN,IAAI,CAACqN,GAAL,CAASJ,CAAT,IAAcjN,IAAI,CAACqN,GAAL,CAAS,CAAT,CAArB;IACD,GAxBY;;IAyBbE,EAAAA,SAAS,CAACC,GAAD;IACP,QAAIC,IAAI,GAAG,CAAX;;IACA,SAAK,IAAIjQ,CAAC,GAAG,CAAb,EAAgBA,CAAC,IAAIgQ,GAArB,EAA0BhQ,CAAC,IAAI,CAA/B,EAAkCiQ,IAAI,IAAIjQ,CAAR;;IAClC,WAAOiQ,IAAP;IACD;;IA7BY,CAAf;;ACOA,+BAAe,CAAC;IAAEvN,EAAAA;IAAF,CAAD;IACb,MAAIwN,OAAO,GAAGtP,sBAAsB,IAAI8B,KAAK,CAAC7D,MAA9C;;IACA,MAAIqR,OAAO,KAAKC,MAAM,CAACC,iBAAvB,EAA0C;IACxCF,IAAAA,OAAO,GAAGC,MAAM,CAACE,SAAjB;IACD;;IACD,MAAIC,UAAJ;IAEA;;IACA,MAAI5N,KAAK,CAAC7D,MAAN,KAAiB,CAArB,EAAwB;IACtByR,IAAAA,UAAU,GAAGxP,gCAAgC,GAAG,CAAhD;IACD,GAFD,MAEO;IACLwP,IAAAA,UAAU,GAAGvP,+BAA+B,GAAG,CAA/C;IACD;;IAED,SAAOyB,IAAI,CAAC+N,GAAL,CAASL,OAAT,EAAkBI,UAAlB,CAAP;IACD,CAfD;;ACJA,yBAAe,CAAC;IAAEnN,EAAAA,IAAF;IAAQD,EAAAA;IAAR,CAAD;IACb;IACA,QAAMsN,SAAS,GAAGhO,IAAI,CAAC+N,GAAL,CAAS/N,IAAI,CAACC,GAAL,CAASU,IAAI,GAAGxB,cAAhB,CAAT,EAA0CX,cAA1C,CAAlB;IAEA,MAAIkP,OAAO,GAAGM,SAAS,GAAG,GAA1B;;IAEA,MAAItN,SAAJ,EAAe;IACbgN,IAAAA,OAAO,IAAI,CAAX;IACD;;IACD,SAAOA,OAAP;IACD,CAVD;;ICQA,MAAMO,aAAa,GAAIC,WAAD;IACpB,QAAMC,SAAS,GAAGD,WAAW,CAACnR,KAAZ,CAAkB,EAAlB,CAAlB;IACA,QAAMqR,cAAc,GAAGD,SAAS,CAAC1M,MAAV,CAAkBxE,IAAD,IACtCA,IAAI,CAACwC,KAAL,CAAWV,SAAX,CADqB,EAErB1C,MAFF;IAGA,QAAMgS,cAAc,GAAGF,SAAS,CAAC1M,MAAV,CAAkBxE,IAAD,IACtCA,IAAI,CAACwC,KAAL,CAAWT,SAAX,CADqB,EAErB3C,MAFF;IAIA,MAAIiS,UAAU,GAAG,CAAjB;IACA,QAAMC,eAAe,GAAGvO,IAAI,CAACwO,GAAL,CAASJ,cAAT,EAAyBC,cAAzB,CAAxB;;IACA,OAAK,IAAI7Q,CAAC,GAAG,CAAb,EAAgBA,CAAC,IAAI+Q,eAArB,EAAsC/Q,CAAC,IAAI,CAA3C,EAA8C;IAC5C8Q,IAAAA,UAAU,IAAIG,KAAK,CAACzB,GAAN,CAAUoB,cAAc,GAAGC,cAA3B,EAA2C7Q,CAA3C,CAAd;IACD;;IACD,SAAO8Q,UAAP;IACD,CAfD;;AAiBA,4BAAgBvQ,IAAD;IACb;IACA,QAAMmQ,WAAW,GAAGnQ,IAAI,CAAC2Q,OAAL,CAAazP,cAAb,EAA6B,EAA7B,CAApB;;IACA,MACEiP,WAAW,CAACzO,KAAZ,CAAkBX,kBAAlB,KACAoP,WAAW,CAACtF,WAAZ,OAA8BsF,WAFhC,EAGE;IACA,WAAO,CAAP;IACD;IAED;IACA;;;IACA,QAAMS,WAAW,GAAG,CAAClQ,WAAD,EAAcC,SAAd,EAAyBE,kBAAzB,CAApB;IACA,QAAMgQ,iBAAiB,GAAGD,WAAW,CAACtS,MAAtC;;IACA,OAAK,IAAImB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGoR,iBAApB,EAAuCpR,CAAC,IAAI,CAA5C,EAA+C;IAC7C,UAAMqP,KAAK,GAAG8B,WAAW,CAACnR,CAAD,CAAzB;;IACA,QAAI0Q,WAAW,CAACzO,KAAZ,CAAkBoN,KAAlB,CAAJ,EAA8B;IAC5B,aAAO,CAAP;IACD;IACF;IAGD;IACA;;;IACA,SAAOoB,aAAa,CAACC,WAAD,CAApB;IACD,CAzBD;;ICbA,MAAMW,SAAS,GAAG,CAAC;IAAErE,EAAAA,IAAF;IAAQsE,EAAAA,MAAR;IAAgB5O,EAAAA;IAAhB,CAAD;IAChB,QAAM6O,QAAQ,GAAGvE,IAAI,CAACsE,MAAD,CAArB;;IAEA,QAAME,IAAI,GAAG9O,KAAK,CAAC0I,WAAN,GAAoB7L,KAApB,CAA0B,EAA1B,CAAb;;IAEA,QAAMkS,WAAW,GAAGD,IAAI,CAACvN,MAAL,CAAaxE,IAAD,IAAUA,IAAI,KAAK6R,MAA/B,EAAuCzS,MAA3D;;IAEA,QAAM6S,aAAa,GAAGF,IAAI,CAACvN,MAAL,CAAaxE,IAAD,IAAUA,IAAI,KAAK8R,QAA/B,EAAyC1S,MAA/D;IACA,SAAO;IACL4S,IAAAA,WADK;IAELC,IAAAA;IAFK,GAAP;IAID,CAZD;;AAcA,uBAAe,CAAC;IAAEvJ,EAAAA,IAAF;IAAQgE,EAAAA,GAAR;IAAazJ,EAAAA;IAAb,CAAD;IACb,MAAI,CAACyF,IAAL,EAAW;IACT,WAAO,CAAP;IACD;;IACD,MAAI2I,UAAU,GAAG,CAAjB;IACA,QAAM9D,IAAI,GAAGb,GAAb;IACAxN,EAAAA,MAAM,CAACC,IAAP,CAAYoO,IAAZ,EAAkB1M,OAAlB,CAA2BgR,MAAD;IACxB,UAAM;IAAEG,MAAAA,WAAF;IAAeC,MAAAA;IAAf,QAAiCL,SAAS,CAAC;IAAErE,MAAAA,IAAF;IAAQsE,MAAAA,MAAR;IAAgB5O,MAAAA;IAAhB,KAAD,CAAhD;;IAEA,QAAI+O,WAAW,KAAK,CAAhB,IAAqBC,aAAa,KAAK,CAA3C,EAA8C;IAC5C;IACA;IACA;IACAZ,MAAAA,UAAU,IAAI,CAAd;IACD,KALD,MAKO;IACL;IACA;IACA,YAAMa,CAAC,GAAGnP,IAAI,CAACwO,GAAL,CAASU,aAAT,EAAwBD,WAAxB,CAAV;IACA,UAAIG,aAAa,GAAG,CAApB;;IACA,WAAK,IAAI5R,CAAC,GAAG,CAAb,EAAgBA,CAAC,IAAI2R,CAArB,EAAwB3R,CAAC,IAAI,CAA7B,EAAgC;IAC9B4R,QAAAA,aAAa,IAAIX,KAAK,CAACzB,GAAN,CAAUkC,aAAa,GAAGD,WAA1B,EAAuCzR,CAAvC,CAAjB;IACD;;IACD8Q,MAAAA,UAAU,IAAIc,aAAd;IACD;IACF,GAlBD;IAmBA,SAAOd,UAAP;IACD,CA1BD;;AClBA,+BAAe,CAAC;IACd5B,EAAAA,IADc;IAEdnD,EAAAA,QAFc;IAGd5D,EAAAA,IAHc;IAIdgE,EAAAA,GAJc;IAKdzJ,EAAAA;IALc,CAAD;IAOb,QAAMmP,WAAW,GAAG3C,IAApB;;IACA,QAAM4C,mBAAmB,GAAGC,gBAAgB,CAACrP,KAAD,CAA5C;IACA,QAAMsP,cAAc,GAAGC,WAAW,CAAC;IAAE9J,IAAAA,IAAF;IAAQgE,IAAAA,GAAR;IAAazJ,IAAAA;IAAb,GAAD,CAAlC;IACA,QAAMwP,kBAAkB,GAAInG,QAAQ,IAAI,CAAb,IAAmB,CAA9C;IACA,QAAMoG,WAAW,GACfN,WAAW,GAAGC,mBAAd,GAAoCE,cAApC,GAAqDE,kBADvD;IAEA,SAAO;IACLL,IAAAA,WADK;IAELC,IAAAA,mBAFK;IAGLE,IAAAA,cAHK;IAILG,IAAAA;IAJK,GAAP;IAMD,CAnBD;;ACRA,0BAAe,CAAC;IACd5C,EAAAA,SADc;IAEd3M,EAAAA,UAFc;IAGdF,EAAAA;IAHc,CAAD;IAKb,QAAM0P,cAAc,GAAG;IACrBC,IAAAA,UAAU,EAAE,EADS;IAErBC,IAAAA,UAAU,EAAE,EAFS;IAGrBC,IAAAA,KAAK,EAAE,EAHc;IAIrBC,IAAAA,YAAY,EAAE,EAJO;IAKrBC,IAAAA,MAAM,EAAE,EALa;IAMrBC,IAAAA,OAAO,EAAE;IANY,GAAvB;;IAQA,MAAInD,SAAS,IAAI6C,cAAjB,EAAiC;IAC/B,WACEA,cAAc,CAAC7C,SAAD,CAAd,IAA4D7M,KAAK,CAAC7D,MADpE;IAGD;IAED;;;IACA,UAAQ0Q,SAAR;IACE,SAAK,YAAL;IACE;IACA;IACA,aAAO/M,IAAI,CAAC+N,GAAL,CACL/N,IAAI,CAACC,GAAL,CAASO,QAAQ,CAACJ,UAAU,CAAC,CAAD,CAAX,EAAgB,EAAhB,CAAR,GAA8BjB,cAAvC,CADK,EAELX,cAFK,CAAP;IAJJ;;IASA,SAAO,CAAP;IACD,CA9BD;;ACDA,2BAAe,CAAC;IAAE6Q,EAAAA,WAAF;IAAec,EAAAA;IAAf,CAAD,KACbd,WAAW,GAAGc,WADhB;;ACAA,6BAAe,CAAC;IAAEjQ,EAAAA,KAAF;IAASkQ,EAAAA;IAAT,CAAD;IACb,QAAMC,QAAQ,GAAGnQ,KAAK,CAACoQ,MAAN,CAAa,CAAb,CAAjB;IACA,MAAIjB,WAAW,GAAG,CAAlB;IACA,QAAMkB,cAAc,GAAG,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,EAAgB,GAAhB,EAAqB,GAArB,EAA0B,GAA1B,EAA+B,GAA/B,CAAvB;;IAEA,MAAIA,cAAc,CAACC,QAAf,CAAwBH,QAAxB,CAAJ,EAAuC;IACrChB,IAAAA,WAAW,GAAG,CAAd;IACD,GAFD,MAEO,IAAIgB,QAAQ,CAAC5Q,KAAT,CAAe,IAAf,CAAJ,EAA0B;IAC/B4P,IAAAA,WAAW,GAAG,EAAd,CAD+B;IAEhC,GAFM,MAEA;IACL;IACA;IACAA,IAAAA,WAAW,GAAG,EAAd;IACD;IAED;;;IACA,MAAI,CAACe,SAAL,EAAgB;IACdf,IAAAA,WAAW,IAAI,CAAf;IACD;;IACD,SAAOA,WAAW,GAAGnP,KAAK,CAAC7D,MAA3B;IACD,CApBD;;ICQA,MAAMoU,iBAAiB,GAAIC,KAAD;IACxB,MAAIC,OAAO,GAAG,CAAd;IACAxU,EAAAA,MAAM,CAACC,IAAP,CAAYsU,KAAZ,EAAmB5S,OAAnB,CAA4BsK,GAAD;IACzB,UAAMwI,SAAS,GAAGF,KAAK,CAACtI,GAAD,CAAvB;IACAuI,IAAAA,OAAO,IAAIC,SAAS,CAACnP,MAAV,CAAkBuB,KAAD,IAAmB,CAAC,CAACA,KAAtC,EAA6C3G,MAAxD;IACD,GAHD;IAIAsU,EAAAA,OAAO,IAAIxU,MAAM,CAAC0U,OAAP,CAAeH,KAAf,EAAsBrU,MAAjC;IACA,SAAOsU,OAAP;IACD,CARD;;IAUA,MAAMG,wBAAwB,GAAG,CAAC;IAChC5Q,EAAAA,KADgC;IAEhCwQ,EAAAA,KAFgC;IAGhCK,EAAAA;IAHgC,CAAD;IAK/B,QAAMC,gBAAgB,GAAG7U,MAAM,CAACC,IAAP,CAAY+M,aAAa,CAACxB,MAAd,CAAqB+I,KAArB,CAAZ,EAAyCrU,MAAlE;IACA,QAAM4U,aAAa,GAAGR,iBAAiB,CAACtH,aAAa,CAACxB,MAAd,CAAqB+I,KAArB,CAAD,CAAvC;IAEA,MAAIhD,OAAO,GAAG,CAAd;IACA,QAAMwD,WAAW,GAAGhR,KAAK,CAAC7D,MAA1B;;IAEA,OAAK,IAAImB,CAAC,GAAG,CAAb,EAAgBA,CAAC,IAAI0T,WAArB,EAAkC1T,CAAC,IAAI,CAAvC,EAA0C;IACxC,UAAM2T,aAAa,GAAGnR,IAAI,CAACwO,GAAL,CAASuC,KAAT,EAAgBvT,CAAC,GAAG,CAApB,CAAtB;;IACA,SAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,IAAI0T,aAArB,EAAoC1T,CAAC,IAAI,CAAzC,EAA4C;IAC1CiQ,MAAAA,OAAO,IAAIe,KAAK,CAACzB,GAAN,CAAUxP,CAAC,GAAG,CAAd,EAAiBC,CAAC,GAAG,CAArB,IAA0BuT,gBAA1B,GAA6CC,aAAa,IAAIxT,CAAzE;IACD;IACF;;IACD,SAAOiQ,OAAP;IACD,CAlBD;;AAoBA,4BAAe,CAAC;IACdgD,EAAAA,KADc;IAEdxQ,EAAAA,KAFc;IAGdkR,EAAAA,YAHc;IAIdL,EAAAA;IAJc,CAAD;IAMb,MAAIrD,OAAO,GAAGoD,wBAAwB,CAAC;IAAE5Q,IAAAA,KAAF;IAASwQ,IAAAA,KAAT;IAAgBK,IAAAA;IAAhB,GAAD,CAAtC;IAGA;;IACA,MAAIK,YAAJ,EAAkB;IAChB,UAAMC,cAAc,GAAGnR,KAAK,CAAC7D,MAAN,GAAe+U,YAAtC;;IACA,QAAIA,YAAY,KAAK,CAAjB,IAAsBC,cAAc,KAAK,CAA7C,EAAgD;IAC9C3D,MAAAA,OAAO,IAAI,CAAX;IACD,KAFD,MAEO;IACL,UAAI4D,iBAAiB,GAAG,CAAxB;;IACA,WAAK,IAAI9T,CAAC,GAAG,CAAb,EAAgBA,CAAC,IAAIwC,IAAI,CAACwO,GAAL,CAAS4C,YAAT,EAAuBC,cAAvB,CAArB,EAA6D7T,CAAC,IAAI,CAAlE,EAAqE;IACnE8T,QAAAA,iBAAiB,IAAI7C,KAAK,CAACzB,GAAN,CAAUoE,YAAY,GAAGC,cAAzB,EAAyC7T,CAAzC,CAArB;IACD;;IACDkQ,MAAAA,OAAO,IAAI4D,iBAAX;IACD;IACF;;IACD,SAAOtR,IAAI,CAACuR,KAAL,CAAW7D,OAAX,CAAP;IACD,CAvBD;;ICpBA,MAAM8D,aAAa,GAAG,CACpB/R,KADoB,EAEpBC,QAFoB;IAIpB,MAAIoO,UAAU,GAAG,CAAjB;;IACA,MAAIrO,KAAK,CAACS,KAAN,CAAY7D,MAAZ,GAAqBqD,QAAQ,CAACrD,MAAlC,EAA0C;IACxC,QAAIoD,KAAK,CAACS,KAAN,CAAY7D,MAAZ,KAAuB,CAA3B,EAA8B;IAC5ByR,MAAAA,UAAU,GAAGxP,gCAAb;IACD,KAFD,MAEO;IACLwP,MAAAA,UAAU,GAAGvP,+BAAb;IACD;IACF;;IACD,SAAOuP,UAAP;IACD,CAbD;;IAmBA,MAAM9E,QAAQ,GAAa;IACzByI,EAAAA,UAAU,EAAEC,mBADa;IAEzBC,EAAAA,IAAI,EAAEC,aAFmB;IAGzBpK,EAAAA,UAAU,EAAEqK,mBAHa;IAIzBhF,EAAAA,KAAK,EAAEiF,cAJkB;IAKzBC,EAAAA,MAAM,EAAEC,eALiB;IAMzBC,EAAAA,QAAQ,EAAEC,iBANe;IAOzBC,EAAAA,OAAO,EAAEC;IAPgB,CAA3B;;IAUA,MAAMC,UAAU,GAAG,CAAC/J,IAAD,EAAe7I,KAAf;IACjB,MAAIuJ,QAAQ,CAACV,IAAD,CAAZ,EAAoB;IAClB,WAAOU,QAAQ,CAACV,IAAD,CAAR,CAAe7I,KAAf,CAAP;IACD;;IACD,MACE0J,aAAa,CAACH,QAAd,CAAuBV,IAAvB,KACA,aAAaa,aAAa,CAACH,QAAd,CAAuBV,IAAvB,CAFf,EAGE;IACA,WAAOa,aAAa,CAACH,QAAd,CAAuBV,IAAvB,EAA6BgK,OAA7B,CAAqC7S,KAArC,CAAP;IACD;;IACD,SAAO,CAAP;IACD,CAXD;IAcA;IACA;;;AACA,2BAAe,CAACA,KAAD,EAAwCC,QAAxC;IACb,QAAM6S,SAAS,GAAgB,EAA/B;;IAEA,MAAI,aAAa9S,KAAb,IAAsBA,KAAK,CAACiO,OAAN,IAAiB,IAA3C,EAAiD;IAC/C,WAAOjO,KAAP;IACD;;IAED,QAAMqO,UAAU,GAAG0D,aAAa,CAAC/R,KAAD,EAAQC,QAAR,CAAhC;IAEA,QAAM8S,gBAAgB,GAAGH,UAAU,CAAC5S,KAAK,CAACgB,OAAP,EAAgBhB,KAAhB,CAAnC;IACA,MAAIiO,OAAO,GAAG,CAAd;;IACA,MAAI,OAAO8E,gBAAP,KAA4B,QAAhC,EAA0C;IACxC9E,IAAAA,OAAO,GAAG8E,gBAAV;IACD,GAFD,MAEO,IAAI/S,KAAK,CAACgB,OAAN,KAAkB,YAAtB,EAAoC;IACzCiN,IAAAA,OAAO,GAAG8E,gBAAgB,CAAC7C,WAA3B;IACA4C,IAAAA,SAAS,CAAClD,WAAV,GAAwBmD,gBAAgB,CAACnD,WAAzC;IACAkD,IAAAA,SAAS,CAACjD,mBAAV,GAAgCkD,gBAAgB,CAAClD,mBAAjD;IACAiD,IAAAA,SAAS,CAAC/C,cAAV,GAA2BgD,gBAAgB,CAAChD,cAA5C;IACD;;IAED,QAAMiD,YAAY,GAAGzS,IAAI,CAAC+N,GAAL,CAASL,OAAT,EAAkBI,UAAlB,CAArB;IACA,SAAO,EACL,GAAGrO,KADE;IAEL,OAAG8S,SAFE;IAGL7E,IAAAA,OAAO,EAAE+E,YAHJ;IAILC,IAAAA,YAAY,EAAEjE,KAAK,CAACrB,KAAN,CAAYqF,YAAZ;IAJT,GAAP;IAMD,CA3BD;;IC5DA,MAAME,aAAa,GAAG;IACpBjT,EAAAA,QAAQ,EAAE,EADU;IAEpBkT,EAAAA,OAAO,EAAE,EAFW;IAGpBC,EAAAA,eAAe,EAAE,KAHG;;IAIpBC,EAAAA,SAAS,CAACC,IAAD,EAAeC,SAAf;IACP,UAAMpV,MAAM,GAAuD,EAAnE;;IACA,SAAK,IAAIJ,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGuV,IAApB,EAA0BvV,CAAC,IAAI,CAA/B,EAAkC;IAChC,UAAIyV,KAAK,GAAY,EAArB;;IACA,UAAID,SAAS,KAAK,QAAlB,EAA4B;IAC1BC,QAAAA,KAAK,GAAG,EAAR;IACD;;IACDrV,MAAAA,MAAM,CAACnB,IAAP,CAAYwW,KAAZ;IACD;;IACD,WAAOrV,MAAP;IACD,GAdmB;;IAepB;IACAsV,EAAAA,mBAAmB,CAAC1V,CAAD,EAAYC,CAAZ;IACjB,WAAO;IACLgD,MAAAA,OAAO,EAAE,YADJ;IAELP,MAAAA,KAAK,EAAE,KAAKR,QAAL,CAAcS,KAAd,CAAoB3C,CAApB,EAAuB,CAACC,CAAD,GAAK,CAAL,IAAU,GAAjC,CAFF;IAGLD,MAAAA,CAHK;IAILC,MAAAA;IAJK,KAAP;IAMD,GAvBmB;;IAwBpB;IACA;IACA;IACA0V,EAAAA,MAAM,CAAC1T,KAAD,EAAuB2T,cAAvB;IACJ,UAAMhS,CAAC,GAAG3B,KAAK,CAAChC,CAAhB;IACA,UAAM4V,cAAc,GAAGC,eAAe,CAAC7T,KAAD,EAAQ,KAAKC,QAAb,CAAtC;IACA,QAAI6T,EAAE,GAAGF,cAAc,CAAC3F,OAAxB;;IACA,QAAI0F,cAAc,GAAG,CAArB,EAAwB;IACtB;IACA;IACA;IACA;IACAG,MAAAA,EAAE,IAAI,KAAKX,OAAL,CAAaW,EAAb,CAAgBF,cAAc,CAAC7V,CAAf,GAAmB,CAAnC,EAAsC4V,cAAc,GAAG,CAAvD,CAAN;IACD;;;IAED,QAAIhP,CAAC,GAAGqK,KAAK,CAAClB,SAAN,CAAgB6F,cAAhB,IAAkCG,EAA1C;;IACA,QAAI,CAAC,KAAKV,eAAV,EAA2B;IACzBzO,MAAAA,CAAC,IAAI/F,mCAAmC,KAAK+U,cAAc,GAAG,CAAtB,CAAxC;IACD;IAED;IACA;IACA;;;IACA,QAAII,UAAU,GAAG,KAAjB;IACArX,IAAAA,MAAM,CAACC,IAAP,CAAY,KAAKwW,OAAL,CAAaxO,CAAb,CAAehD,CAAf,CAAZ,EAA+BtD,OAA/B,CAAwC2V,sBAAD;IACrC,YAAMC,oBAAoB,GAAG,KAAKd,OAAL,CAAaxO,CAAb,CAAehD,CAAf,EAAkBqS,sBAAlB,CAA7B;;IACA,UAAIjT,QAAQ,CAACiT,sBAAD,EAAyB,EAAzB,CAAR,IAAwCL,cAA5C,EAA4D;IAC1D,YAAIM,oBAAoB,IAAItP,CAA5B,EAA+B;IAC7BoP,UAAAA,UAAU,GAAG,IAAb;IACD;IACF;IACF,KAPD;;IAQA,QAAI,CAACA,UAAL,EAAiB;IACf;IACA,WAAKZ,OAAL,CAAaxO,CAAb,CAAehD,CAAf,EAAkBgS,cAAlB,IAAoChP,CAApC;IACA,WAAKwO,OAAL,CAAae,CAAb,CAAevS,CAAf,EAAkBgS,cAAlB,IAAoCC,cAApC;IACA,WAAKT,OAAL,CAAaW,EAAb,CAAgBnS,CAAhB,EAAmBgS,cAAnB,IAAqCG,EAArC;IACD;IACF,GA9DmB;;IAgEpB;IACAK,EAAAA,gBAAgB,CAACC,iBAAD;IACd;IACA,QAAIpU,KAAK,GAAG,KAAKyT,mBAAL,CAAyB,CAAzB,EAA4BW,iBAA5B,CAAZ;IACA,SAAKV,MAAL,CAAY1T,KAAZ,EAAmB,CAAnB;;IACA,SAAK,IAAIjC,CAAC,GAAG,CAAb,EAAgBA,CAAC,IAAIqW,iBAArB,EAAwCrW,CAAC,IAAI,CAA7C,EAAgD;IAC9C;IACA;IACA;IACAiC,MAAAA,KAAK,GAAG,KAAKyT,mBAAL,CAAyB1V,CAAzB,EAA4BqW,iBAA5B,CAAR;IACA,YAAMC,GAAG,GAAG,KAAKlB,OAAL,CAAae,CAAb,CAAenW,CAAC,GAAG,CAAnB,CAAZ,CAL8C;;IAO9CrB,MAAAA,MAAM,CAACC,IAAP,CAAY0X,GAAZ,EAAiBhW,OAAjB,CAA0BsV,cAAD;IACvB,cAAMW,SAAS,GAAGD,GAAG,CAACV,cAAD,CAArB;IAEA;IACA;IACA;;IACA,YAAIW,SAAS,CAACtT,OAAV,KAAsB,YAA1B,EAAwC;IACtC;IACA,eAAK0S,MAAL,CAAY1T,KAAZ,EAAmBe,QAAQ,CAAC4S,cAAD,EAAiB,EAAjB,CAAR,GAA+B,CAAlD;IACD;IACF,OAVD;IAWD;IACF,GAxFmB;;IA0FpB;IACA;IACAY,EAAAA,MAAM,CAAChI,cAAD;IACJ,UAAMiI,oBAAoB,GAAoB,EAA9C;IACA,QAAI7S,CAAC,GAAG4K,cAAc,GAAG,CAAzB;;IAEA,QAAIoH,cAAc,GAAG,CAArB;;IAEA,QAAIhP,CAAC,GAAG,KAAR;IACA,UAAMxB,IAAI,GAAG,KAAKgQ,OAAL,CAAaxO,CAAb,CAAehD,CAAf,CAAb;;IAEA,QAAIwB,IAAJ,EAAU;IACRzG,MAAAA,MAAM,CAACC,IAAP,CAAYwG,IAAZ,EAAkB9E,OAAlB,CAA2BoW,uBAAD;IACxB,cAAMC,oBAAoB,GAAGvR,IAAI,CAACsR,uBAAD,CAAjC;;IACA,YAAIC,oBAAoB,GAAG/P,CAA3B,EAA8B;IAC5BgP,UAAAA,cAAc,GAAG5S,QAAQ,CAAC0T,uBAAD,EAA0B,EAA1B,CAAzB;IACA9P,UAAAA,CAAC,GAAG+P,oBAAJ;IACD;IACF,OAND;IAOD;;IACD,WAAO/S,CAAC,IAAI,CAAZ,EAAe;IACb,YAAM3B,KAAK,GAAkB,KAAKmT,OAAL,CAAae,CAAb,CAAevS,CAAf,EAAkBgS,cAAlB,CAA7B;IACAa,MAAAA,oBAAoB,CAACG,OAArB,CAA6B3U,KAA7B;IACA2B,MAAAA,CAAC,GAAG3B,KAAK,CAACjC,CAAN,GAAU,CAAd;IACA4V,MAAAA,cAAc,IAAI,CAAlB;IACD;;IACD,WAAOa,oBAAP;IACD;;IArHmB,CAAtB;AAwHA,kBAAe;IACb;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAI,EAAAA,0BAA0B,CACxB3U,QADwB,EAExBtC,OAFwB,EAGxByV,eAAe,GAAG,KAHM;IAKxBF,IAAAA,aAAa,CAACjT,QAAd,GAAyBA,QAAzB;IACAiT,IAAAA,aAAa,CAACE,eAAd,GAAgCA,eAAhC;IACA,UAAM7G,cAAc,GAAGtM,QAAQ,CAACrD,MAAhC;;IAEA,QAAIiY,oBAAoB,GAAG3B,aAAa,CAACG,SAAd,CACzB9G,cADyB,EAEzB,OAFyB,CAA3B;IAKA5O,IAAAA,OAAO,CAACU,OAAR,CAAiB2B,KAAD;IACd6U,MAAAA,oBAAoB,CAAC7U,KAAK,CAAChC,CAAP,CAApB,CAA8BhB,IAA9B,CAAmCgD,KAAnC;IACD,KAFD;;IAIA6U,IAAAA,oBAAoB,GAAGA,oBAAoB,CAACtX,GAArB,CAA0ByC,KAAD,IAC9CA,KAAK,CAACpC,IAAN,CAAW,CAACC,EAAD,EAAoBC,EAApB,KAA0CD,EAAE,CAACE,CAAH,GAAOD,EAAE,CAACC,CAA/D,CADqB,CAAvB;IAIAmV,IAAAA,aAAa,CAACC,OAAd,GAAwB;IACtB;IACA;IACA;IACA;IACA;IACA;IACAe,MAAAA,CAAC,EAAEhB,aAAa,CAACG,SAAd,CAAwB9G,cAAxB,EAAwC,QAAxC,CAPmB;IAQtB;IACA;IACAuH,MAAAA,EAAE,EAAEZ,aAAa,CAACG,SAAd,CAAwB9G,cAAxB,EAAwC,QAAxC,CAVkB;IAWtB;IACA5H,MAAAA,CAAC,EAAEuO,aAAa,CAACG,SAAd,CAAwB9G,cAAxB,EAAwC,QAAxC;IAZmB,KAAxB;;IAeA,SAAK,IAAI5K,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG4K,cAApB,EAAoC5K,CAAC,IAAI,CAAzC,EAA4C;IAC1CkT,MAAAA,oBAAoB,CAAClT,CAAD,CAApB,CAAwBtD,OAAxB,CAAiC2B,KAAD;IAC9B,YAAIA,KAAK,CAACjC,CAAN,GAAU,CAAd,EAAiB;IACfrB,UAAAA,MAAM,CAACC,IAAP,CAAYuW,aAAa,CAACC,OAAd,CAAsBe,CAAtB,CAAwBlU,KAAK,CAACjC,CAAN,GAAU,CAAlC,CAAZ,EAAkDM,OAAlD,CACGsV,cAAD;IACET,YAAAA,aAAa,CAACQ,MAAd,CAAqB1T,KAArB,EAA4Be,QAAQ,CAAC4S,cAAD,EAAiB,EAAjB,CAAR,GAA+B,CAA3D;IACD,WAHH;IAKD,SAND,MAMO;IACLT,UAAAA,aAAa,CAACQ,MAAd,CAAqB1T,KAArB,EAA4B,CAA5B;IACD;IACF,OAVD;IAWAkT,MAAAA,aAAa,CAACiB,gBAAd,CAA+BxS,CAA/B;IACD;;IACD,UAAM6S,oBAAoB,GAAGtB,aAAa,CAACqB,MAAd,CAAqBhI,cAArB,CAA7B;IACA,UAAMuI,qBAAqB,GAAGN,oBAAoB,CAAC5X,MAAnD;IACA,UAAMqR,OAAO,GAAG,KAAK8G,UAAL,CAAgB9U,QAAhB,EAA0B6U,qBAA1B,CAAhB;IACA,WAAO;IACL7U,MAAAA,QADK;IAELgO,MAAAA,OAFK;IAGLgF,MAAAA,YAAY,EAAEjE,KAAK,CAACrB,KAAN,CAAYM,OAAZ,CAHT;IAILuE,MAAAA,QAAQ,EAAEgC;IAJL,KAAP;IAMD,GA7FY;;IA+FbO,EAAAA,UAAU,CAAC9U,QAAD,EAAmB6U,qBAAnB;IACR,UAAMvI,cAAc,GAAGtM,QAAQ,CAACrD,MAAhC;IACA,QAAIqR,OAAO,GAAG,CAAd;;IACA,QAAIhO,QAAQ,CAACrD,MAAT,KAAoB,CAAxB,EAA2B;IACzBqR,MAAAA,OAAO,GAAG,CAAV;IACD,KAFD,MAEO;IACLA,MAAAA,OAAO,GACLiF,aAAa,CAACC,OAAd,CAAsBxO,CAAtB,CAAwB4H,cAAc,GAAG,CAAzC,EAA4CuI,qBAA5C,CADF;IAED;;IACD,WAAO7G,OAAP;IACD;;IAzGY,CAAf;;ICrHA;;;;;;IAKA,MAAM+G,WAAN;IACE;IACAhV,EAAAA,KAAK,CAAC;IAAEC,IAAAA,QAAF;IAAYgV,IAAAA;IAAZ,GAAD;IACH,UAAMtX,OAAO,GAA2C,EAAxD;IACA,QAAI0P,SAAS,GAAG,CAAhB;;IACA,WAAOA,SAAS,GAAGpN,QAAQ,CAACrD,MAA5B,EAAoC;IAClC,YAAMsY,WAAW,GAAG,KAAKC,cAAL,CAAoBlV,QAApB,EAA8BoN,SAA9B,CAApB;IACA,YAAM+H,SAAS,GAAG,KAAKC,YAAL,CAAkBpV,QAAlB,EAA4BoN,SAA5B,CAAlB;;IACA,UAAI6H,WAAW,IAAI,IAAnB,EAAyB;IACvB;IACD;;IACD,YAAM;IAAElV,QAAAA,KAAF;IAASsV,QAAAA;IAAT,UAAuB,KAAKC,aAAL,CAAmBL,WAAnB,EAAgCE,SAAhC,CAA7B;;IAEA,UAAIpV,KAAJ,EAAW;IACT,cAAMhC,CAAC,GAAGgC,KAAK,CAACyB,KAAN,GAAczB,KAAK,CAAC,CAAD,CAAL,CAASpD,MAAvB,GAAgC,CAA1C;IACA,cAAMgT,WAAW,GAAG,KAAK4F,cAAL,CAAoBF,SAApB,EAA+BL,SAA/B,CAApB;IACAtX,QAAAA,OAAO,CAACX,IAAR,CAAa,KAAKyY,cAAL,CAAoBH,SAApB,EAA+BtX,CAA/B,EAAkCgC,KAAlC,EAAyC4P,WAAzC,CAAb;IAEAvC,QAAAA,SAAS,GAAGrP,CAAC,GAAG,CAAhB;IACD;IACF;;IAED,UAAM0X,WAAW,GAAG/X,OAAO,CAACgY,IAAR,CAAc3V,KAAD;IAC/B,aAAOA,KAAK,YAAY4V,OAAxB;IACD,KAFmB,CAApB;;IAGA,QAAIF,WAAJ,EAAiB;IACf,aAAOE,OAAO,CAACC,GAAR,CAAYlY,OAAZ,CAAP;IACD;;IACD,WAAOA,OAAP;IACD;;;IAGD8X,EAAAA,cAAc,CACZH,SADY,EAEZtX,CAFY,EAGZgC,KAHY,EAIZ4P,WAJY;IAMZ,UAAMkG,SAAS,GAAgB;IAC7B9U,MAAAA,OAAO,EAAE,QADoB;IAE7BjD,MAAAA,CAAC,EAAEiC,KAAK,CAACyB,KAFoB;IAG7BzD,MAAAA,CAH6B;IAI7ByC,MAAAA,KAAK,EAAET,KAAK,CAAC,CAAD,CAJiB;IAK7BsV,MAAAA,SAL6B;IAM7B1F,MAAAA,WAAW,EAAE,CANgB;IAO7Bc,MAAAA,WAAW,EAAE1Q,KAAK,CAAC,CAAD,CAAL,CAASpD,MAAT,GAAkB0Y,SAAS,CAAC1Y;IAPZ,KAA/B;;IASA,QAAIgT,WAAW,YAAYgG,OAA3B,EAAoC;IAClC,aAAOhG,WAAW,CAACmG,IAAZ,CAAkBC,mBAAD;IACtB,eAAO,EACL,GAAGF,SADE;IAELlG,UAAAA,WAAW,EAAEoG;IAFR,SAAP;IAID,OALM,CAAP;IAMD;;IACD,WAAO,EACL,GAAGF,SADE;IAELlG,MAAAA;IAFK,KAAP;IAID;;IAEDuF,EAAAA,cAAc,CAAClV,QAAD,EAAmBoN,SAAnB;IACZ,UAAM4I,MAAM,GAAG,UAAf;IACAA,IAAAA,MAAM,CAAC5I,SAAP,GAAmBA,SAAnB;IACA,WAAO4I,MAAM,CAACrV,IAAP,CAAYX,QAAZ,CAAP;IACD;;IAEDoV,EAAAA,YAAY,CAACpV,QAAD,EAAmBoN,SAAnB;IACV,UAAM6I,IAAI,GAAG,WAAb;IACAA,IAAAA,IAAI,CAAC7I,SAAL,GAAiBA,SAAjB;IACA,WAAO6I,IAAI,CAACtV,IAAL,CAAUX,QAAV,CAAP;IACD;;IAEDsV,EAAAA,aAAa,CACXL,WADW,EAEXE,SAFW;IAIX,UAAMe,YAAY,GAAG,YAArB;IACA,QAAInW,KAAJ;IACA,QAAIsV,SAAS,GAAG,EAAhB;;IACA,QAAIF,SAAS,IAAIF,WAAW,CAAC,CAAD,CAAX,CAAetY,MAAf,GAAwBwY,SAAS,CAAC,CAAD,CAAT,CAAaxY,MAAtD,EAA8D;IAC5D;IACA;IACA;IACAoD,MAAAA,KAAK,GAAGkV,WAAR,CAJ4D;IAM5D;IACA;IACA;;IACA,YAAM/R,IAAI,GAAGgT,YAAY,CAACvV,IAAb,CAAkBZ,KAAK,CAAC,CAAD,CAAvB,CAAb;;IACA,UAAImD,IAAJ,EAAU;IACRmS,QAAAA,SAAS,GAAGnS,IAAI,CAAC,CAAD,CAAhB;IACD;IACF,KAbD,MAaO;IACL;IACA;IACA;IACAnD,MAAAA,KAAK,GAAGoV,SAAR;;IACA,UAAIpV,KAAJ,EAAW;IACTsV,QAAAA,SAAS,GAAGtV,KAAK,CAAC,CAAD,CAAjB;IACD;IACF;;IACD,WAAO;IACLA,MAAAA,KADK;IAELsV,MAAAA;IAFK,KAAP;IAID;;IAEDE,EAAAA,cAAc,CAACF,SAAD,EAAoBL,SAApB;IACZ,UAAMtX,OAAO,GAAGsX,SAAS,CAACjV,KAAV,CAAgBsV,SAAhB,CAAhB;;IACA,QAAI3X,OAAO,YAAYiY,OAAvB,EAAgC;IAC9B,aAAOjY,OAAO,CAACoY,IAAR,CAAcK,eAAD;IAClB,cAAMC,YAAY,GAAGxD,OAAO,CAAC+B,0BAAR,CACnBU,SADmB,EAEnBc,eAFmB,CAArB;IAIA,eAAOC,YAAY,CAACpI,OAApB;IACD,OANM,CAAP;IAOD;;IACD,UAAMoI,YAAY,GAAGxD,OAAO,CAAC+B,0BAAR,CAAmCU,SAAnC,EAA8C3X,OAA9C,CAArB;IACA,WAAO0Y,YAAY,CAACpI,OAApB;IACD;;;;ICxHH;;;;;;IAKA,MAAMqI,aAAN;IAAA7O,EAAAA;IACE,kBAAA,GAAY,CAAZ;IA8FD;;;IA3FCzH,EAAAA,KAAK,CAAC;IAAEC,IAAAA;IAAF,GAAD;IACH;;;;;;;;;;;;;;IAcA,UAAM9B,MAAM,GAAoB,EAAhC;;IACA,QAAI8B,QAAQ,CAACrD,MAAT,KAAoB,CAAxB,EAA2B;IACzB,aAAO,EAAP;IACD;;IACD,QAAImB,CAAC,GAAG,CAAR;IACA,QAAIwY,SAAS,GAAkB,IAA/B;IACA,UAAMhK,cAAc,GAAGtM,QAAQ,CAACrD,MAAhC;;IACA,SAAK,IAAI+E,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG4K,cAApB,EAAoC5K,CAAC,IAAI,CAAzC,EAA4C;IAC1C,YAAM6U,KAAK,GAAGvW,QAAQ,CAACwW,UAAT,CAAoB9U,CAApB,IAAyB1B,QAAQ,CAACwW,UAAT,CAAoB9U,CAAC,GAAG,CAAxB,CAAvC;;IACA,UAAI4U,SAAS,IAAI,IAAjB,EAAuB;IACrBA,QAAAA,SAAS,GAAGC,KAAZ;IACD;;IACD,UAAIA,KAAK,KAAKD,SAAd,EAAyB;IACvB,cAAMvY,CAAC,GAAG2D,CAAC,GAAG,CAAd;IACA,aAAK+R,MAAL,CAAY;IACV3V,UAAAA,CADU;IAEVC,UAAAA,CAFU;IAGVwY,UAAAA,KAAK,EAAED,SAHG;IAIVtW,UAAAA,QAJU;IAKV9B,UAAAA;IALU,SAAZ;IAOAJ,QAAAA,CAAC,GAAGC,CAAJ;IACAuY,QAAAA,SAAS,GAAGC,KAAZ;IACD;IACF;;IACD,SAAK9C,MAAL,CAAY;IACV3V,MAAAA,CADU;IAEVC,MAAAA,CAAC,EAAEuO,cAAc,GAAG,CAFV;IAGViK,MAAAA,KAAK,EAAED,SAHG;IAIVtW,MAAAA,QAJU;IAKV9B,MAAAA;IALU,KAAZ;IAOA,WAAOA,MAAP;IACD;;IAEDuV,EAAAA,MAAM,CAAC;IAAE3V,IAAAA,CAAF;IAAKC,IAAAA,CAAL;IAAQwY,IAAAA,KAAR;IAAevW,IAAAA,QAAf;IAAyB9B,IAAAA;IAAzB,GAAD;IACJ,QAAIH,CAAC,GAAGD,CAAJ,GAAQ,CAAR,IAAawC,IAAI,CAACC,GAAL,CAASgW,KAAT,MAAoB,CAArC,EAAwC;IACtC,YAAME,aAAa,GAAGnW,IAAI,CAACC,GAAL,CAASgW,KAAT,CAAtB;;IACA,UAAIE,aAAa,GAAG,CAAhB,IAAqBA,aAAa,IAAI,KAAKC,SAA/C,EAA0D;IACxD,cAAMlW,KAAK,GAAGR,QAAQ,CAACS,KAAT,CAAe3C,CAAf,EAAkB,CAACC,CAAD,GAAK,CAAL,IAAU,GAA5B,CAAd;IACA,cAAM;IAAE4Y,UAAAA,YAAF;IAAgBC,UAAAA;IAAhB,YAAkC,KAAKC,WAAL,CAAiBrW,KAAjB,CAAxC;IACA,eAAOtC,MAAM,CAACnB,IAAP,CAAY;IACjBgE,UAAAA,OAAO,EAAE,UADQ;IAEjBjD,UAAAA,CAFiB;IAGjBC,UAAAA,CAHiB;IAIjByC,UAAAA,KAAK,EAAER,QAAQ,CAACS,KAAT,CAAe3C,CAAf,EAAkB,CAACC,CAAD,GAAK,CAAL,IAAU,GAA5B,CAJU;IAKjB4Y,UAAAA,YALiB;IAMjBC,UAAAA,aANiB;IAOjBlG,UAAAA,SAAS,EAAE6F,KAAK,GAAG;IAPF,SAAZ,CAAP;IASD;IACF;;IACD,WAAO,IAAP;IACD;;IAEDM,EAAAA,WAAW,CAACrW,KAAD;IACT;IACA;IACA,QAAImW,YAAY,GAAG,SAAnB;IACA,QAAIC,aAAa,GAAG,EAApB;;IAEA,QAAIzX,SAAS,CAAC2X,IAAV,CAAetW,KAAf,CAAJ,EAA2B;IACzBmW,MAAAA,YAAY,GAAG,OAAf;IACAC,MAAAA,aAAa,GAAG,EAAhB;IACD,KAHD,MAGO,IAAI3X,SAAS,CAAC6X,IAAV,CAAetW,KAAf,CAAJ,EAA2B;IAChCmW,MAAAA,YAAY,GAAG,OAAf;IACAC,MAAAA,aAAa,GAAG,EAAhB;IACD,KAHM,MAGA,IAAIpX,SAAS,CAACsX,IAAV,CAAetW,KAAf,CAAJ,EAA2B;IAChCmW,MAAAA,YAAY,GAAG,QAAf;IACAC,MAAAA,aAAa,GAAG,EAAhB;IACD;;IACD,WAAO;IACLD,MAAAA,YADK;IAELC,MAAAA;IAFK,KAAP;IAID;;;;IC1GH;;;;;;IAKA,MAAMG,YAAN;IAAAvP,EAAAA;IACE,mBAAA,GAAa,mDAAb;IAoGD;;IAlGCzH,EAAAA,KAAK,CAAC;IAAEC,IAAAA;IAAF,GAAD;IACH,UAAMtC,OAAO,GAAmB,EAAhC;IACAjB,IAAAA,MAAM,CAACC,IAAP,CAAY+M,aAAa,CAACxB,MAA1B,EAAkC7J,OAAlC,CAA2C4Y,SAAD;IACxC,YAAMhG,KAAK,GAAGvH,aAAa,CAACxB,MAAd,CAAqB+O,SAArB,CAAd;IACApa,MAAAA,MAAM,CAACc,OAAD,EAAU,KAAKuZ,MAAL,CAAYjX,QAAZ,EAAsBgR,KAAtB,EAA6BgG,SAA7B,CAAV,CAAN;IACD,KAHD;IAIA,WAAOvZ,MAAM,CAACC,OAAD,CAAb;IACD;;IAEDwZ,EAAAA,cAAc,CAACF,SAAD,EAAoBhX,QAApB,EAAsCwB,KAAtC;IACZ,QACE,CAACwV,SAAS,CAAClG,QAAV,CAAmB,QAAnB,CAAD;IAEA,SAAKqG,UAAL,CAAgBL,IAAhB,CAAqB9W,QAAQ,CAAC4Q,MAAT,CAAgBpP,KAAhB,CAArB,CAHF,EAIE;IACA,aAAO,CAAP;IACD;;IACD,WAAO,CAAP;IACD;;;IAGDyV,EAAAA,MAAM,CAACjX,QAAD,EAAmBgR,KAAnB,EAAuCgG,SAAvC;IACJ,QAAItF,YAAJ;IACA,UAAMhU,OAAO,GAAmB,EAAhC;IACA,QAAII,CAAC,GAAG,CAAR;IACA,UAAMwO,cAAc,GAAGtM,QAAQ,CAACrD,MAAhC;;IACA,WAAOmB,CAAC,GAAGwO,cAAc,GAAG,CAA5B,EAA+B;IAC7B,UAAIvO,CAAC,GAAGD,CAAC,GAAG,CAAZ;IACA,UAAIsZ,aAAa,GAAG,CAApB;IACA,UAAI/F,KAAK,GAAG,CAAZ;IACAK,MAAAA,YAAY,GAAG,KAAKwF,cAAL,CAAoBF,SAApB,EAA+BhX,QAA/B,EAAyClC,CAAzC,CAAf,CAJ6B;;IAM7B,aAAO,IAAP,EAAa;IACX,cAAMuZ,QAAQ,GAAGrX,QAAQ,CAAC4Q,MAAT,CAAgB7S,CAAC,GAAG,CAApB,CAAjB;IACA,cAAMuZ,SAAS,GAAGtG,KAAK,CAACqG,QAAD,CAAL,IAAyC,EAA3D;IACA,YAAItT,KAAK,GAAG,KAAZ;IACA,YAAIwT,cAAc,GAAG,CAAC,CAAtB;IACA,YAAIC,YAAY,GAAG,CAAC,CAApB,CALW;;IAOX,YAAIzZ,CAAC,GAAGuO,cAAR,EAAwB;IACtB,gBAAMmL,OAAO,GAAGzX,QAAQ,CAAC4Q,MAAT,CAAgB7S,CAAhB,CAAhB;IACA,gBAAM2Z,eAAe,GAAGJ,SAAS,CAAC3a,MAAlC;;IACA,eAAK,IAAI+E,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGgW,eAApB,EAAqChW,CAAC,IAAI,CAA1C,EAA6C;IAC3C,kBAAMiW,QAAQ,GAAGL,SAAS,CAAC5V,CAAD,CAA1B;IACA8V,YAAAA,YAAY,IAAI,CAAhB,CAF2C;;IAI3C,gBAAIG,QAAJ,EAAc;IACZ,oBAAMC,aAAa,GAAGD,QAAQ,CAACnN,OAAT,CAAiBiN,OAAjB,CAAtB,CADY;;IAGZ,kBAAIG,aAAa,KAAK,CAAC,CAAvB,EAA0B;IACxB7T,gBAAAA,KAAK,GAAG,IAAR;IACAwT,gBAAAA,cAAc,GAAGC,YAAjB,CAFwB;;IAIxB,oBAAII,aAAa,KAAK,CAAtB,EAAyB;IACvB;IACA;IACA;IACA;IACAlG,kBAAAA,YAAY,IAAI,CAAhB;IACD,iBAVuB;;;IAYxB,oBAAI0F,aAAa,KAAKG,cAAtB,EAAsC;IACpC;IACA;IACA;IACAlG,kBAAAA,KAAK,IAAI,CAAT;IACA+F,kBAAAA,aAAa,GAAGG,cAAhB;IACD;;IACD;IACD;IACF;IACF;IACF,SAxCU;;;IA0CX,YAAIxT,KAAJ,EAAW;IACThG,UAAAA,CAAC,IAAI,CAAL,CADS;IAGV,SAHD,MAGO;IACL;IACA,cAAIA,CAAC,GAAGD,CAAJ,GAAQ,CAAZ,EAAe;IACbJ,YAAAA,OAAO,CAACX,IAAR,CAAa;IACXgE,cAAAA,OAAO,EAAE,SADE;IAEXjD,cAAAA,CAFW;IAGXC,cAAAA,CAAC,EAAEA,CAAC,GAAG,CAHI;IAIXyC,cAAAA,KAAK,EAAER,QAAQ,CAACS,KAAT,CAAe3C,CAAf,EAAkBC,CAAlB,CAJI;IAKXiT,cAAAA,KAAK,EAAEgG,SALI;IAMX3F,cAAAA,KANW;IAOXK,cAAAA;IAPW,aAAb;IASD,WAZI;;;IAcL5T,UAAAA,CAAC,GAAGC,CAAJ;IACA;IACD;IACF;IACF;;IACD,WAAOL,OAAP;IACD;;;;IC5FH,MAAMma,QAAN;IAAArQ,EAAAA;IACW,iBAAA,GAAqB;IAC5ByK,MAAAA,IAAI,EAAEC,SADsB;IAE5BpK,MAAAA,UAAU,EAAEqK,eAFgB;IAG5BhF,MAAAA,KAAK,EAAEiF,UAHqB;IAI5B;IACAC,MAAAA,MAAM,EAAEC,WALoB;IAM5BC,MAAAA,QAAQ,EAAEC,aANkB;IAO5BC,MAAAA,OAAO,EAAEC;IAPmB,KAArB;IAiDV;;IAvCC3S,EAAAA,KAAK,CAACC,QAAD;IACH,UAAMtC,OAAO,GAAoB,EAAjC;IAEA,UAAMoa,QAAQ,GAA+B,EAA7C;IACA,UAAMxO,QAAQ,GAAG,CACf,GAAG7M,MAAM,CAACC,IAAP,CAAY,KAAK4M,QAAjB,CADY,EAEf,GAAG7M,MAAM,CAACC,IAAP,CAAY+M,aAAa,CAACH,QAA1B,CAFY,CAAjB;IAIAA,IAAAA,QAAQ,CAAClL,OAAT,CAAkBsK,GAAD;IACf,UAAI,CAAC,KAAKY,QAAL,CAAcZ,GAAd,CAAD,IAAuB,CAACe,aAAa,CAACH,QAAd,CAAuBZ,GAAvB,CAA5B,EAAyD;IACvD;IACD;;IACD,YAAMqP,OAAO,GAAG,KAAKzO,QAAL,CAAcZ,GAAd,IACZ,KAAKY,QAAL,CAAcZ,GAAd,CADY,GAEZe,aAAa,CAACH,QAAd,CAAuBZ,GAAvB,EAA4BmP,QAFhC;IAGA,YAAMG,WAAW,GAAG,IAAID,OAAJ,EAApB;IACA,YAAM7Z,MAAM,GAAG8Z,WAAW,CAACjY,KAAZ,CAAkB;IAC/BC,QAAAA,QAD+B;IAE/BgV,QAAAA,SAAS,EAAE;IAFoB,OAAlB,CAAf;;IAKA,UAAI9W,MAAM,YAAYyX,OAAtB,EAA+B;IAC7BzX,QAAAA,MAAM,CAAC4X,IAAP,CAAamC,QAAD;IACVrb,UAAAA,MAAM,CAACc,OAAD,EAAUua,QAAV,CAAN;IACD,SAFD;IAGAH,QAAAA,QAAQ,CAAC/a,IAAT,CAAcmB,MAAd;IACD,OALD,MAKO;IACLtB,QAAAA,MAAM,CAACc,OAAD,EAAUQ,MAAV,CAAN;IACD;IACF,KArBD;;IAsBA,QAAI4Z,QAAQ,CAACnb,MAAT,GAAkB,CAAtB,EAAyB;IACvB,aAAO,IAAIgZ,OAAJ,CAAauC,OAAD;IACjBvC,QAAAA,OAAO,CAACC,GAAR,CAAYkC,QAAZ,EAAsBhC,IAAtB,CAA2B;IACzBoC,UAAAA,OAAO,CAACza,MAAM,CAACC,OAAD,CAAP,CAAP;IACD,SAFD;IAGD,OAJM,CAAP;IAKD;;IACD,WAAOD,MAAM,CAACC,OAAD,CAAb;IACD;;;;IClEH,MAAMya,MAAM,GAAG,CAAf;IACA,MAAMC,MAAM,GAAGD,MAAM,GAAG,EAAxB;IACA,MAAME,IAAI,GAAGD,MAAM,GAAG,EAAtB;IACA,MAAME,GAAG,GAAGD,IAAI,GAAG,EAAnB;IACA,MAAME,KAAK,GAAGD,GAAG,GAAG,EAApB;IACA,MAAME,IAAI,GAAGD,KAAK,GAAG,EAArB;IACA,MAAME,OAAO,GAAGD,IAAI,GAAG,GAAvB;IAEA,MAAME,KAAK,GAAG;IACZ7R,EAAAA,MAAM,EAAEsR,MADI;IAEZpR,EAAAA,MAAM,EAAEqR,MAFI;IAGZnR,EAAAA,IAAI,EAAEoR,IAHM;IAIZlX,EAAAA,GAAG,EAAEmX,GAJO;IAKZpX,EAAAA,KAAK,EAAEqX,KALK;IAMZtX,EAAAA,IAAI,EAAEuX,IANM;IAOZG,EAAAA,OAAO,EAAEF;IAPG,CAAd;IAUA;;;;;;IAKA,MAAMG,aAAN;IACE3b,EAAAA,SAAS,CAAC4b,UAAD,EAAqBtF,KAArB;IACP,QAAI7K,GAAG,GAAGmQ,UAAV;;IACA,QAAItF,KAAK,KAAKpL,SAAV,IAAuBoL,KAAK,KAAK,CAArC,EAAwC;IACtC7K,MAAAA,GAAG,IAAI,GAAP;IACD;;IACD,UAAM;IAAE/B,MAAAA;IAAF,QAAqB8C,aAAa,CAAC1B,YAAzC;IACA,WAAOpB,cAAc,CAAC+B,GAAD,CAAd,CAAmDsG,OAAnD,CACL,QADK,KAEFuE,OAFE,CAAP;IAID;;IAEDuF,EAAAA,mBAAmB,CAAC9K,OAAD;IACjB,UAAM+K,iBAAiB,GAAsB;IAC3CC,MAAAA,0BAA0B,EAAEhL,OAAO,IAAI,MAAM,IAAV,CADQ;IAE3CiL,MAAAA,6BAA6B,EAAEjL,OAAO,GAAG,EAFE;IAG3CkL,MAAAA,8BAA8B,EAAElL,OAAO,GAAG,GAHC;IAI3CmL,MAAAA,+BAA+B,EAAEnL,OAAO,GAAG;IAJA,KAA7C;IAMA,UAAMoL,iBAAiB,GAAsB;IAC3CJ,MAAAA,0BAA0B,EAAE,EADe;IAE3CC,MAAAA,6BAA6B,EAAE,EAFY;IAG3CC,MAAAA,8BAA8B,EAAE,EAHW;IAI3CC,MAAAA,+BAA+B,EAAE;IAJU,KAA7C;IAMA1c,IAAAA,MAAM,CAACC,IAAP,CAAYqc,iBAAZ,EAA+B3a,OAA/B,CAAwCib,QAAD;IACrC,YAAMvS,OAAO,GAAGiS,iBAAiB,CAACM,QAAD,CAAjC;IACAD,MAAAA,iBAAiB,CAACC,QAAD,CAAjB,GACE,KAAKC,WAAL,CAAiBxS,OAAjB,CADF;IAED,KAJD;IAKA,WAAO;IACLiS,MAAAA,iBADK;IAELK,MAAAA,iBAFK;IAGLG,MAAAA,KAAK,EAAE,KAAKC,cAAL,CAAoBxL,OAApB;IAHF,KAAP;IAKD;;IAEDwL,EAAAA,cAAc,CAACxL,OAAD;IACZ,UAAMyL,KAAK,GAAG,CAAd;;IACA,QAAIzL,OAAO,GAAG,MAAMyL,KAApB,EAA2B;IACzB;IACA,aAAO,CAAP;IACD;;IACD,QAAIzL,OAAO,GAAG,MAAMyL,KAApB,EAA2B;IACzB;IACA,aAAO,CAAP;IACD;;IACD,QAAIzL,OAAO,GAAG,MAAMyL,KAApB,EAA2B;IACzB;IACA,aAAO,CAAP;IACD;;IACD,QAAIzL,OAAO,GAAG,OAAOyL,KAArB,EAA4B;IAC1B;IACA;IACA,aAAO,CAAP;IACD;;;IAED,WAAO,CAAP;IACD;;IAEDH,EAAAA,WAAW,CAACxS,OAAD;IACT,QAAI+R,UAAU,GAAG,WAAjB;IACA,QAAIa,IAAJ;IACA,UAAMC,QAAQ,GAAGld,MAAM,CAACC,IAAP,CAAYgc,KAAZ,CAAjB;IACA,UAAMkB,UAAU,GAAGD,QAAQ,CAACE,SAAT,CAChBC,IAAD,IAAUhT,OAAO,GAAG4R,KAAK,CAACoB,IAAD,CADR,CAAnB;;IAGA,QAAIF,UAAU,GAAG,CAAC,CAAlB,EAAqB;IACnBf,MAAAA,UAAU,GAAGc,QAAQ,CAACC,UAAU,GAAG,CAAd,CAArB;;IACA,UAAIA,UAAU,KAAK,CAAnB,EAAsB;IACpBF,QAAAA,IAAI,GAAGpZ,IAAI,CAACuR,KAAL,CAAW/K,OAAO,GAAG4R,KAAK,CAACG,UAAD,CAA1B,CAAP;IACD,OAFD,MAEO;IACLA,QAAAA,UAAU,GAAG,UAAb;IACD;IACF;;IACD,WAAO,KAAK5b,SAAL,CAAe4b,UAAf,EAA2Ba,IAA3B,CAAP;IACD;;;;ACvGH,6BAAe;IACb,SAAO,IAAP;IACD,CAFD;;ACEA,uBAAe;IACb,SAAO;IACLK,IAAAA,OAAO,EAAEtQ,aAAa,CAAC1B,YAAd,CAA2BhD,QAA3B,CAAoCO,KADxC;IAELU,IAAAA,WAAW,EAAE,CAACyD,aAAa,CAAC1B,YAAd,CAA2B/B,WAA3B,CAAuCV,KAAxC;IAFR,GAAP;IAID,CALD;;ICEA,MAAM0U,4BAA4B,GAAG,CACnCja,KADmC,EAEnCka,WAFmC;IAInC,MAAIF,OAAO,GAAG,EAAd;;IACA,MAAIE,WAAW,IAAI,CAACla,KAAK,CAACkG,IAAtB,IAA8B,CAAClG,KAAK,CAAC8J,QAAzC,EAAmD;IACjD,QAAI9J,KAAK,CAACiN,IAAN,IAAc,EAAlB,EAAsB;IACpB+M,MAAAA,OAAO,GAAGtQ,aAAa,CAAC1B,YAAd,CAA2BhD,QAA3B,CAAoCQ,MAA9C;IACD,KAFD,MAEO,IAAIxF,KAAK,CAACiN,IAAN,IAAc,GAAlB,EAAuB;IAC5B+M,MAAAA,OAAO,GAAGtQ,aAAa,CAAC1B,YAAd,CAA2BhD,QAA3B,CAAoCS,UAA9C;IACD,KAFM,MAEA;IACLuU,MAAAA,OAAO,GAAGtQ,aAAa,CAAC1B,YAAd,CAA2BhD,QAA3B,CAAoCU,MAA9C;IACD;IACF,GARD,MAQO,IAAI1F,KAAK,CAACiT,YAAN,IAAsB,CAA1B,EAA6B;IAClC+G,IAAAA,OAAO,GAAGtQ,aAAa,CAAC1B,YAAd,CAA2BhD,QAA3B,CAAoCW,eAA9C;IACD;;IACD,SAAOqU,OAAP;IACD,CAjBD;;IAmBA,MAAMG,6BAA6B,GAAG,CACpCna,KADoC,EAEpCka,WAFoC;IAIpC,MAAIF,OAAO,GAAG,EAAd;;IACA,MAAIE,WAAJ,EAAiB;IACfF,IAAAA,OAAO,GAAGtQ,aAAa,CAAC1B,YAAd,CAA2BhD,QAA3B,CAAoCY,YAA9C;IACD;;IACD,SAAOoU,OAAP;IACD,CATD;;IAWA,MAAMI,yBAAyB,GAAG,CAChCpa,KADgC,EAEhCka,WAFgC;IAIhC,MAAIA,WAAJ,EAAiB;IACf,WAAOxQ,aAAa,CAAC1B,YAAd,CAA2BhD,QAA3B,CAAoCa,iBAA3C;IACD;;IACD,SAAO6D,aAAa,CAAC1B,YAAd,CAA2BhD,QAA3B,CAAoCc,WAA3C;IACD,CARD;;IAUA,MAAMuU,oBAAoB,GAAG,CAACra,KAAD,EAAwBka,WAAxB;IAC3B,MAAIF,OAAO,GAAG,EAAd;IACA,QAAMM,QAAQ,GAAGta,KAAK,CAACyM,cAAvB;IACA,QAAM8N,OAAO,GACXD,QAAQ,KAAK,WAAb,IAA4BA,QAAQ,CAACnR,WAAT,GAAuB4H,QAAvB,CAAgC,YAAhC,CAD9B;;IAEA,MAAIuJ,QAAQ,KAAK,WAAjB,EAA8B;IAC5BN,IAAAA,OAAO,GAAGC,4BAA4B,CAACja,KAAD,EAAQka,WAAR,CAAtC;IACD,GAFD,MAEO,IAAII,QAAQ,CAACvJ,QAAT,CAAkB,WAAlB,CAAJ,EAAoC;IACzCiJ,IAAAA,OAAO,GAAGG,6BAA6B,CAACna,KAAD,EAAQka,WAAR,CAAvC;IACD,GAFM,MAEA,IAAIK,OAAJ,EAAa;IAClBP,IAAAA,OAAO,GAAGI,yBAAyB,CAACpa,KAAD,EAAQka,WAAR,CAAnC;IACD,GAFM,MAEA,IAAII,QAAQ,KAAK,YAAjB,EAA+B;IACpCN,IAAAA,OAAO,GAAGtQ,aAAa,CAAC1B,YAAd,CAA2BhD,QAA3B,CAAoCe,UAA9C;IACD;;IACD,SAAOiU,OAAP;IACD,CAfD;;AAiBA,6BAAe,CAACha,KAAD,EAAwBka,WAAxB;IACb,QAAMF,OAAO,GAAGK,oBAAoB,CAACra,KAAD,EAAQka,WAAR,CAApC;IACA,QAAMjU,WAAW,GAAa,EAA9B;IACA,QAAM3H,IAAI,GAAG0B,KAAK,CAACS,KAAnB;;IAEA,MAAInC,IAAI,CAAC0B,KAAL,CAAWhB,WAAX,CAAJ,EAA6B;IAC3BiH,IAAAA,WAAW,CAACjJ,IAAZ,CAAiB0M,aAAa,CAAC1B,YAAd,CAA2B/B,WAA3B,CAAuCI,cAAxD;IACD,GAFD,MAEO,IAAI/H,IAAI,CAAC0B,KAAL,CAAWb,kBAAX,KAAkCb,IAAI,CAAC6K,WAAL,OAAuB7K,IAA7D,EAAmE;IACxE2H,IAAAA,WAAW,CAACjJ,IAAZ,CAAiB0M,aAAa,CAAC1B,YAAd,CAA2B/B,WAA3B,CAAuCG,YAAxD;IACD;;IACD,MAAIpG,KAAK,CAAC8J,QAAN,IAAkB9J,KAAK,CAACS,KAAN,CAAY7D,MAAZ,IAAsB,CAA5C,EAA+C;IAC7CqJ,IAAAA,WAAW,CAACjJ,IAAZ,CAAiB0M,aAAa,CAAC1B,YAAd,CAA2B/B,WAA3B,CAAuCE,YAAxD;IACD;;IACD,MAAInG,KAAK,CAACkG,IAAV,EAAgB;IACdD,IAAAA,WAAW,CAACjJ,IAAZ,CAAiB0M,aAAa,CAAC1B,YAAd,CAA2B/B,WAA3B,CAAuCC,IAAxD;IACD;;IACD,SAAO;IACL8T,IAAAA,OADK;IAEL/T,IAAAA;IAFK,GAAP;IAID,CApBD;;AC1DA,wBAAgBjG,KAAD;IACb,MAAIA,KAAK,CAACsN,SAAN,KAAoB,YAAxB,EAAsC;IACpC,WAAO;IACL0M,MAAAA,OAAO,EAAEtQ,aAAa,CAAC1B,YAAd,CAA2BhD,QAA3B,CAAoCM,WADxC;IAELW,MAAAA,WAAW,EAAE,CACXyD,aAAa,CAAC1B,YAAd,CAA2B/B,WAA3B,CAAuCX,WAD5B,EAEXoE,aAAa,CAAC1B,YAAd,CAA2B/B,WAA3B,CAAuCK,eAF5B;IAFR,KAAP;IAOD;;IACD,SAAO;IACL0T,IAAAA,OAAO,EAAE,EADJ;IAEL/T,IAAAA,WAAW,EAAE;IAFR,GAAP;IAID,CAdD;;ACAA,yBAAgBjG,KAAD;IACb,MAAIga,OAAO,GAAGtQ,aAAa,CAAC1B,YAAd,CAA2BhD,QAA3B,CAAoCI,cAAlD;;IACA,MAAIpF,KAAK,CAACsV,SAAN,CAAgB1Y,MAAhB,KAA2B,CAA/B,EAAkC;IAChCod,IAAAA,OAAO,GAAGtQ,aAAa,CAAC1B,YAAd,CAA2BhD,QAA3B,CAAoCG,YAA9C;IACD;;IAED,SAAO;IACL6U,IAAAA,OADK;IAEL/T,IAAAA,WAAW,EAAE,CAACyD,aAAa,CAAC1B,YAAd,CAA2B/B,WAA3B,CAAuCM,QAAxC;IAFR,GAAP;IAID,CAVD;;ACDA,2BAAe;IACb,SAAO;IACLyT,IAAAA,OAAO,EAAEtQ,aAAa,CAAC1B,YAAd,CAA2BhD,QAA3B,CAAoCK,SADxC;IAELY,IAAAA,WAAW,EAAE,CAACyD,aAAa,CAAC1B,YAAd,CAA2B/B,WAA3B,CAAuCZ,SAAxC;IAFR,GAAP;IAID,CALD;;ACCA,0BAAgBrF,KAAD;IACb,MAAIga,OAAO,GAAGtQ,aAAa,CAAC1B,YAAd,CAA2BhD,QAA3B,CAAoCE,UAAlD;;IACA,MAAIlF,KAAK,CAACsR,KAAN,KAAgB,CAApB,EAAuB;IACrB0I,IAAAA,OAAO,GAAGtQ,aAAa,CAAC1B,YAAd,CAA2BhD,QAA3B,CAAoCC,WAA9C;IACD;;IACD,SAAO;IACL+U,IAAAA,OADK;IAEL/T,IAAAA,WAAW,EAAE,CAACyD,aAAa,CAAC1B,YAAd,CAA2B/B,WAA3B,CAAuCO,qBAAxC;IAFR,GAAP;IAID,CATD;;ICOA,MAAMgU,eAAe,GAAG;IACtBR,EAAAA,OAAO,EAAE,EADa;IAEtB/T,EAAAA,WAAW,EAAE;IAFS,CAAxB;IAQA;;;;;;IAKA,MAAMwU,QAAN;IAgBEhT,EAAAA;IAfS,iBAAA,GAAqB;IAC5BuK,MAAAA,UAAU,EAAEC,iBADgB;IAE5BC,MAAAA,IAAI,EAAEC,WAFsB;IAG5BpK,MAAAA,UAAU,EAAEqK,iBAHgB;IAI5BhF,MAAAA,KAAK,EAAEiF,YAJqB;IAK5BC,MAAAA,MAAM,EAAEC,aALoB;IAM5BC,MAAAA,QAAQ,EAAEC,eANkB;IAO5BC,MAAAA,OAAO,EAAEC;IAPmB,KAArB;IAUT,wBAAA,GAAgC;IAC9BqH,MAAAA,OAAO,EAAE,EADqB;IAE9B/T,MAAAA,WAAW,EAAE;IAFiB,KAAhC;IAME,SAAKyU,qBAAL;IACD;;IAEDA,EAAAA,qBAAqB;IACnB,SAAKF,eAAL,CAAqBvU,WAArB,CAAiCjJ,IAAjC,CACE0M,aAAa,CAAC1B,YAAd,CAA2B/B,WAA3B,CAAuCS,QADzC,EAEEgD,aAAa,CAAC1B,YAAd,CAA2B/B,WAA3B,CAAuCU,MAFzC;IAID;;IAEDgU,EAAAA,WAAW,CAACnB,KAAD,EAAgBhH,QAAhB;IACT,QAAIA,QAAQ,CAAC5V,MAAT,KAAoB,CAAxB,EAA2B;IACzB,aAAO,KAAK4d,eAAZ;IACD;;IACD,QAAIhB,KAAK,GAAG,CAAZ,EAAe;IACb,aAAOgB,eAAP;IACD;;IACD,UAAMI,aAAa,GAAGlR,aAAa,CAAC1B,YAAd,CAA2B/B,WAA3B,CAAuCQ,WAA7D;IACA,UAAMoU,YAAY,GAAG,KAAKC,eAAL,CAAqBtI,QAArB,CAArB;IACA,QAAIuI,QAAQ,GAAG,KAAKC,gBAAL,CAAsBH,YAAtB,EAAoCrI,QAAQ,CAAC5V,MAAT,KAAoB,CAAxD,CAAf;;IACA,QAAIme,QAAQ,KAAK,IAAb,IAAqBA,QAAQ,KAAK3S,SAAtC,EAAiD;IAC/C2S,MAAAA,QAAQ,CAAC9U,WAAT,CAAqB0O,OAArB,CAA6BiG,aAA7B;;IACA,UAAIG,QAAQ,CAACf,OAAT,IAAoB,IAAxB,EAA8B;IAC5Be,QAAAA,QAAQ,CAACf,OAAT,GAAmB,EAAnB;IACD;IACF,KALD,MAKO;IACLe,MAAAA,QAAQ,GAAG;IACTf,QAAAA,OAAO,EAAE,EADA;IAET/T,QAAAA,WAAW,EAAE,CAAC2U,aAAD;IAFJ,OAAX;IAID;;IACD,WAAOG,QAAP;IACD;;IAEDD,EAAAA,eAAe,CAACtI,QAAD;IACb,QAAIqI,YAAY,GAAGrI,QAAQ,CAAC,CAAD,CAA3B;IACA,UAAMyI,cAAc,GAAGzI,QAAQ,CAAC9R,KAAT,CAAe,CAAf,CAAvB;IACAua,IAAAA,cAAc,CAAC5c,OAAf,CAAwB2B,KAAD;IACrB,UAAIA,KAAK,CAACS,KAAN,CAAY7D,MAAZ,GAAqBie,YAAY,CAACpa,KAAb,CAAmB7D,MAA5C,EAAoD;IAClDie,QAAAA,YAAY,GAAG7a,KAAf;IACD;IACF,KAJD;IAKA,WAAO6a,YAAP;IACD;;IAEDG,EAAAA,gBAAgB,CAAChb,KAAD,EAAwBka,WAAxB;IACd,QAAI,KAAK3Q,QAAL,CAAcvJ,KAAK,CAACgB,OAApB,CAAJ,EAAkC;IAChC,aAAO,KAAKuI,QAAL,CAAcvJ,KAAK,CAACgB,OAApB,EAA6BhB,KAA7B,EAAoCka,WAApC,CAAP;IACD;;IACD,QACExQ,aAAa,CAACH,QAAd,CAAuBvJ,KAAK,CAACgB,OAA7B,KACA,cAAc0I,aAAa,CAACH,QAAd,CAAuBvJ,KAAK,CAACgB,OAA7B,CAFhB,EAGE;IACA,aAAO0I,aAAa,CAACH,QAAd,CAAuBvJ,KAAK,CAACgB,OAA7B,EAAsC+Z,QAAtC,CAA+C/a,KAA/C,EAAsDka,WAAtD,CAAP;IACD;;IACD,WAAOM,eAAP;IACD;;;;IC9FH;;;AAGA,oBAAe,CACbU,IADa,EAEbC,IAFa,EAGbC,WAHa;IAKb,MAAIC,OAAJ;IACA,SAAO,SAASC,QAAT,CAA8C,GAAGC,IAAjD;IACL,UAAMC,OAAO,GAAG,IAAhB;;IACA,UAAMC,KAAK,GAAG;IACZJ,MAAAA,OAAO,GAAGjT,SAAV;;IACA,UAAI,CAACgT,WAAL,EAAkB;IAChBF,QAAAA,IAAI,CAACje,KAAL,CAAWue,OAAX,EAAoBD,IAApB;IACD;IACF,KALD;;IAMA,UAAMG,aAAa,GAAGN,WAAW,IAAI,CAACC,OAAtC;;IACA,QAAIA,OAAO,KAAKjT,SAAhB,EAA2B;IACzBuT,MAAAA,YAAY,CAACN,OAAD,CAAZ;IACD;;IACDA,IAAAA,OAAO,GAAGO,UAAU,CAACH,KAAD,EAAQN,IAAR,CAApB;;IACA,QAAIO,aAAJ,EAAmB;IACjB,aAAOR,IAAI,CAACje,KAAL,CAAWue,OAAX,EAAoBD,IAApB,CAAP;IACD;;IACD,WAAOnT,SAAP;IACD,GAjBD;IAkBD,CAxBD;;ICGA,MAAM2R,IAAI,GAAG,MAAM,IAAIpa,IAAJ,GAAWkc,OAAX,EAAnB;;IAEA,MAAMC,iBAAiB,GAAG,CACxB1F,eADwB,EAExBnW,QAFwB,EAGxB8b,KAHwB;IAKxB,QAAMhB,QAAQ,GAAG,IAAIN,QAAJ,EAAjB;IACA,QAAMuB,aAAa,GAAG,IAAInD,aAAJ,EAAtB;IACA,QAAMoD,aAAa,GAAGpJ,OAAO,CAAC+B,0BAAR,CACpB3U,QADoB,EAEpBmW,eAFoB,CAAtB;IAIA,QAAM8F,QAAQ,GAAGnC,IAAI,KAAKgC,KAA1B;IACA,QAAMI,WAAW,GAAGH,aAAa,CAACjD,mBAAd,CAAkCkD,aAAa,CAAChO,OAAhD,CAApB;IAEA,SAAO;IACLiO,IAAAA,QADK;IAEL,OAAGD,aAFE;IAGL,OAAGE,WAHE;IAILpB,IAAAA,QAAQ,EAAEA,QAAQ,CAACJ,WAAT,CACRwB,WAAW,CAAC3C,KADJ,EAERyC,aAAa,CAACzJ,QAFN;IAJL,GAAP;IASD,CAvBD;;IAyBA,MAAM4J,IAAI,GAAG,CAACnc,QAAD,EAAmB8F,UAAnB;IACX,MAAIA,UAAJ,EAAgB;IACd2D,IAAAA,aAAa,CAACN,0BAAd,CAAyCrD,UAAzC;IACD;;IAED,QAAMsW,QAAQ,GAAG,IAAIvE,QAAJ,EAAjB;IAEA,SAAOuE,QAAQ,CAACrc,KAAT,CAAeC,QAAf,CAAP;IACD,CARD;;UAUaqc,MAAM,GAAG,CAACrc,QAAD,EAAmB8F,UAAnB;IACpB,QAAMgW,KAAK,GAAGhC,IAAI,EAAlB;IACA,QAAMpc,OAAO,GAAGye,IAAI,CAACnc,QAAD,EAAW8F,UAAX,CAApB;;IAEA,MAAIpI,OAAO,YAAYiY,OAAvB,EAAgC;IAC9B,UAAM,IAAIrN,KAAJ,CACJ,oEADI,CAAN;IAGD;;IACD,SAAOuT,iBAAiB,CAACne,OAAD,EAAUsC,QAAV,EAAoB8b,KAApB,CAAxB;IACD;UAEYQ,WAAW,GAAG,OACzBtc,QADyB,EAEzB8F,UAFyB;IAIzB,QAAMgW,KAAK,GAAGhC,IAAI,EAAlB;IACA,QAAMpc,OAAO,GAAG,MAAMye,IAAI,CAACnc,QAAD,EAAW8F,UAAX,CAA1B;IAEA,SAAO+V,iBAAiB,CAACne,OAAD,EAAUsC,QAAV,EAAoB8b,KAApB,CAAxB;IACD;;;;;;;;;;;;;;;"}