{"content": [{"id": "6f1f2520", "settings": {"layout": "full_width", "gap": "no", "background_background": "classic", "background_color": "#EAEAEA", "padding": {"unit": "px", "top": "30", "right": "0", "bottom": "0", "left": "0", "isLinked": false}, "ae_dynamic_rules": [{"_id": "425678e"}], "slider_delay": "5000"}, "elements": [{"id": "35356b9f", "settings": {"_column_size": 100, "_inline_size": null, "ae_dynamic_rules": [{"_id": "822619b"}], "slider_delay": "5000"}, "elements": [{"id": "527b4ed5", "settings": {"ae_dynamic_rules": [{"_id": "ed78773"}], "slider_delay": "5000"}, "elements": [{"id": "16ef0fde", "settings": {"_column_size": 100, "_inline_size": null, "ae_dynamic_rules": [{"_id": "dc27bf4"}], "slider_delay": "5000"}, "elements": [{"id": "252d1847", "settings": {"title": "About Us", "typography_typography": "custom", "typography_font_family": "Nunito", "typography_font_size": {"unit": "px", "size": 40, "sizes": []}, "typography_font_size_mobile": {"unit": "px", "size": 26, "sizes": []}, "typography_font_weight": "600", "_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "-35", "left": "0", "isLinked": false}, "typography_text_transform": "uppercase", "ae_dynamic_rules": [{"_id": "d056c9a"}]}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}, {"id": "13bd1119", "settings": {"width": {"unit": "px", "size": 25, "sizes": []}, "width_tablet": {"unit": "px", "size": "", "sizes": []}, "width_mobile": {"unit": "px", "size": 20, "sizes": []}, "text": "Divider", "color": "#F4A41C", "weight": {"unit": "px", "size": 4, "sizes": []}, "gap": {"unit": "px", "size": 3, "sizes": []}, "_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": false}, "_margin_mobile": {"unit": "px", "top": "0", "right": "0", "bottom": "-7", "left": "0", "isLinked": false}, "_padding": {"unit": "px", "top": "15", "right": "0", "bottom": "15", "left": "0", "isLinked": false}, "ae_dynamic_rules": [{"_id": "a995540"}]}, "elements": [], "isInner": false, "widgetType": "divider", "elType": "widget"}], "isInner": true, "elType": "column"}], "isInner": true, "elType": "section"}, {"id": "5eac3242", "settings": {"html": "<div class=\"elementor-shape elementor-shape-bottom\" data-negative=\"false\">\r\n\t\t\t<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 1000 100\" preserveAspectRatio=\"none\">\r\n\t<path class=\"elementor-shape-fill\" opacity=\"0.33\" d=\"M473,67.3c-203.9,88.3-263.1-34-320.3,0C66,119.1,0,59.7,0,59.7V0h1000v59.7 c0,0-62.1,26.1-94.9,29.3c-32.8,3.3-62.8-12.3-75.8-22.1C806,49.6,745.3,8.7,694.9,4.7S492.4,59,473,67.3z\"></path>\r\n\t<path class=\"elementor-shape-fill\" opacity=\"0.66\" d=\"M734,67.3c-45.5,0-77.2-23.2-129.1-39.1c-28.6-8.7-150.3-10.1-254,39.1 s-91.7-34.4-149.2,0C115.7,118.3,0,39.8,0,39.8V0h1000v36.5c0,0-28.2-18.5-92.1-18.5C810.2,18.1,775.7,67.3,734,67.3z\"></path>\r\n\t<path class=\"elementor-shape-fill\" d=\"M766.1,28.9c-200-57.5-266,65.5-395.1,19.5C242,1.8,242,5.4,184.8,20.6C128,35.8,132.3,44.9,89.9,52.5C28.6,63.7,0,0,0,0 h1000c0,0-9.9,40.9-83.6,48.1S829.6,47,766.1,28.9z\"></path>\r\n</svg>\t\t</div>", "_margin": {"unit": "px", "top": "83", "right": "0", "bottom": "0", "left": "0", "isLinked": false}, "_margin_tablet": {"unit": "px", "top": "25", "right": "0", "bottom": "0", "left": "0", "isLinked": false}, "_margin_mobile": {"unit": "px", "top": "-50", "right": "0", "bottom": "0", "left": "0", "isLinked": false}, "_padding": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": true}, "ae_dynamic_rules": [{"_id": "b4aa5d0"}]}, "elements": [], "isInner": false, "widgetType": "html", "elType": "widget"}], "isInner": false, "elType": "column"}], "isInner": false, "elType": "section"}, {"id": "2dde8e1c", "settings": {"ae_dynamic_rules": [{"_id": "71def55"}], "slider_delay": "5000"}, "elements": [{"id": "2e7bfcf9", "settings": {"_column_size": 100, "_inline_size": null, "ae_dynamic_rules": [{"_id": "cab9153"}], "slider_delay": "5000"}, "elements": [{"id": "65036c78", "settings": {"html": "<div class=\"elementor-container elementor-column-gap-default\">\r\n\t\t\t\t\t<div class=\"has_ae_slider elementor-column elementor-col-100 elementor-top-column elementor-element elementor-element-03ec0b8 ae-bg-gallery-type-default\" data-id=\"03ec0b8\" data-element_type=\"column\">\r\n\t\t\t<div class=\"elementor-widget-wrap elementor-element-populated\">\r\n\t\t\t\t\t\t\t\t<div class=\"elementor-element elementor-element-b6a4b9f elementor-widget elementor-widget-heading\" data-id=\"b6a4b9f\" data-element_type=\"widget\" data-widget_type=\"heading.default\">\r\n\t\t\t\t<div class=\"elementor-widget-container\">\r\n\t\t\t<h2 id=\"h2-header\" class=\"elementor-heading-title elementor-size-default\">❝</h2>\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div class=\"elementor-element elementor-element-2169264 elementor-widget elementor-widget-text-editor\" data-id=\"2169264\" data-element_type=\"widget\" data-widget_type=\"text-editor.default\">\r\n\t\t\t\t<div class=\"elementor-widget-container\">\r\n\t\t\t<style>/*! elementor - v3.5.6 - 28-02-2022 */\r\n.elementor-widget-text-editor.elementor-drop-cap-view-stacked .elementor-drop-cap{background-color:#818a91;color:#fff}.elementor-widget-text-editor.elementor-drop-cap-view-framed .elementor-drop-cap{color:#818a91;border:3px solid;background-color:transparent}.elementor-widget-text-editor:not(.elementor-drop-cap-view-default) .elementor-drop-cap{margin-top:8px}.elementor-widget-text-editor:not(.elementor-drop-cap-view-default) .elementor-drop-cap-letter{width:1em;height:1em}.elementor-widget-text-editor .elementor-drop-cap{float:left;text-align:center;line-height:1;font-size:50px}.elementor-widget-text-editor .elementor-drop-cap-letter{display:inline-block}</style>\t\t\t\t<p id=\"quote\">Founded in 2010, AUTOMATION ZONE is one of the largest engineering companies in the USA, Saudi Arabia, and Turkey.</p>\t\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div class=\"elementor-element elementor-element-51c5fce elementor-widget elementor-widget-heading\" data-id=\"51c5fce\" data-element_type=\"widget\" data-widget_type=\"heading.default\">\r\n\t\t\t\t<div class=\"elementor-widget-container\">\r\n\t\t\t<h2 id=\"h2-header\" class=\"elementor-heading-title elementor-size-default\">❞</h2>\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t</div>\r\n\t\t\t\t\t\t\t</div>", "ae_dynamic_rules": [{"_id": "11f22ba"}]}, "elements": [], "isInner": false, "widgetType": "html", "elType": "widget"}], "isInner": false, "elType": "column"}], "isInner": false, "elType": "section"}, {"id": "194b4699", "settings": {"structure": "20", "background_background": "classic", "background_color": "#EAEAEA", "padding": {"unit": "px", "top": "30", "right": "0", "bottom": "30", "left": "0", "isLinked": false}, "ae_dynamic_rules": [{"_id": "3fabdc0"}], "slider_delay": "5000"}, "elements": [{"id": "26a6617b", "settings": {"_column_size": 50, "_inline_size": null, "ae_dynamic_rules": [{"_id": "57eccb8"}], "slider_delay": "5000"}, "elements": [{"id": "6103af42", "settings": {"selected_icon": {"value": "far fa-eye", "library": "fa-regular"}, "title_text": "Mission", "description_text": "To contiguously strive to offer the latest technology in the vast growing field of information and systems technology.", "title_typography_typography": "custom", "title_typography_font_family": "Nunito", "title_typography_font_size": {"unit": "rem", "size": 2, "sizes": []}, "title_typography_font_size_tablet": {"unit": "rem", "size": "", "sizes": []}, "title_typography_font_size_mobile": {"unit": "rem", "size": "", "sizes": []}, "title_typography_font_weight": "600", "description_color": "#000000", "description_typography_typography": "custom", "description_typography_font_family": "Nunito", "description_typography_font_weight": "400", "_margin": {"unit": "em", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": false}, "_margin_tablet": {"unit": "em", "top": "", "right": "", "bottom": "", "left": "", "isLinked": true}, "_margin_mobile": {"unit": "em", "top": "", "right": "", "bottom": "", "left": "", "isLinked": true}, "_padding": {"unit": "em", "top": "0", "right": "0", "bottom": "1.75", "left": "0", "isLinked": false}, "_padding_tablet": {"unit": "em", "top": "", "right": "", "bottom": "", "left": "", "isLinked": true}, "_padding_mobile": {"unit": "em", "top": "", "right": "", "bottom": "", "left": "", "isLinked": true}, "ae_dynamic_rules": [{"_id": "e39c98b"}]}, "elements": [], "isInner": false, "widgetType": "icon-box", "elType": "widget"}], "isInner": false, "elType": "column"}, {"id": "5fe94b6e", "settings": {"_column_size": 50, "_inline_size": null, "ae_dynamic_rules": [{"_id": "51b730d"}], "slider_delay": "5000"}, "elements": [{"id": "74bf7648", "settings": {"selected_icon": {"value": "far fa-map", "library": "fa-regular"}, "title_text": "Vision", "description_text": "To be a recognized innovative, future-oriented company in the markets we serve.", "title_typography_typography": "custom", "title_typography_font_family": "Nunito", "title_typography_font_size": {"unit": "rem", "size": 2, "sizes": []}, "title_typography_font_size_tablet": {"unit": "rem", "size": "", "sizes": []}, "title_typography_font_size_mobile": {"unit": "rem", "size": "", "sizes": []}, "title_typography_font_weight": "600", "description_color": "#000000", "description_typography_typography": "custom", "description_typography_font_family": "Nunito", "description_typography_font_weight": "400", "_margin": {"unit": "em", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": false}, "_margin_tablet": {"unit": "em", "top": "", "right": "", "bottom": "", "left": "", "isLinked": true}, "_margin_mobile": {"unit": "em", "top": "", "right": "", "bottom": "", "left": "", "isLinked": true}, "_padding": {"unit": "em", "top": "0", "right": "0", "bottom": "1.75", "left": "0", "isLinked": false}, "_padding_tablet": {"unit": "em", "top": "", "right": "", "bottom": "", "left": "", "isLinked": true}, "_padding_mobile": {"unit": "em", "top": "", "right": "", "bottom": "", "left": "", "isLinked": true}, "ae_dynamic_rules": [{"_id": "6a77dfb"}]}, "elements": [], "isInner": false, "widgetType": "icon-box", "elType": "widget"}], "isInner": false, "elType": "column"}], "isInner": false, "elType": "section"}, {"id": "6d2d90f5", "settings": {"structure": "20", "padding": {"unit": "px", "top": "60", "right": "0", "bottom": "60", "left": "0", "isLinked": false}, "ae_dynamic_rules": [{"_id": "7ce0f9f"}], "slider_delay": "5000"}, "elements": [{"id": "7cf6cd04", "settings": {"_column_size": 50, "_inline_size": 66.261, "ae_dynamic_rules": [{"_id": "6de6371"}], "slider_delay": "5000"}, "elements": [{"id": "59eb8287", "settings": {"title": "Taking care of our customers is our first principle and our number one priority", "title_color": "#000000", "typography_typography": "custom", "typography_font_family": "Nunito", "typography_font_size": {"unit": "px", "size": 40, "sizes": []}, "typography_font_weight": "300", "typography_font_size_mobile": {"unit": "px", "size": 22, "sizes": []}, "ae_dynamic_rules": [{"_id": "377000b"}]}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}, {"id": "13650a49", "settings": {"editor": "<p style=\"text-align: left;\">Established in 2010 in the USA as an integration company only. Started the Saudi office in 2011. Expanded services in 2013 to cover Fire Alarm and Fire management solutions. Further expansion in 2015 for Building Management, Lighting Controls, Power Monitoring systems &amp; Lab control. Covered many projects with major clients.</p>", "align": "left", "text_color": "#000000", "typography_typography": "custom", "typography_font_family": "Nunito", "typography_font_size": {"unit": "em", "size": 1, "sizes": []}, "typography_font_size_tablet": {"unit": "em", "size": "", "sizes": []}, "typography_font_size_mobile": {"unit": "em", "size": "", "sizes": []}, "typography_font_weight": "400", "ae_dynamic_rules": [{"_id": "d4bb9bb"}]}, "elements": [], "isInner": false, "widgetType": "text-editor", "elType": "widget"}], "isInner": false, "elType": "column"}, {"id": "52b75c4", "settings": {"_column_size": 50, "_inline_size": 33.257, "ae_dynamic_rules": [{"_id": "37120bd"}], "slider_delay": "5000"}, "elements": [{"id": "5f8c2399", "settings": {"image": {"url": "https://azoneus.com/wp-content/uploads/2022/02/hands-engineer-working-blueprint-construction-concept-engineering-tools-vintage-tone-retro-filter-effect-soft-focus-selective-focus_1418-477-768x512.jpg", "id": "", "source": "url", "alt": ""}, "space": {"unit": "%", "size": 100, "sizes": []}, "space_tablet": {"unit": "px", "size": "", "sizes": []}, "space_mobile": {"unit": "px", "size": "", "sizes": []}, "image_border_radius": {"unit": "px", "top": "15", "right": "15", "bottom": "15", "left": "15", "isLinked": true}, "ae_dynamic_rules": [{"_id": "b1712c1"}]}, "elements": [], "isInner": false, "widgetType": "image", "elType": "widget"}], "isInner": false, "elType": "column"}], "isInner": false, "elType": "section"}, {"id": "31b7b127", "settings": {"layout": "full_width", "background_background": "classic", "background_color": "#EAEAEA", "padding": {"unit": "px", "top": "30", "right": "0", "bottom": "40", "left": "0", "isLinked": false}, "ae_dynamic_rules": [{"_id": "e7ce292"}], "slider_delay": "5000"}, "elements": [{"id": "66166b35", "settings": {"_column_size": 100, "_inline_size": null, "ae_dynamic_rules": [{"_id": "bcaee0d"}], "slider_delay": "5000"}, "elements": [{"id": "17f3a45b", "settings": {"title": "Our Locations", "align": "center", "title_color": "#000000", "typography_typography": "custom", "typography_font_family": "Nunito", "typography_font_size": {"unit": "px", "size": 40, "sizes": []}, "typography_font_weight": "500", "ae_dynamic_rules": [{"_id": "64289a5"}]}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}, {"id": "20ff7a05", "settings": {"layout": "full_width", "gap": "no", "height_inner": "min-height", "structure": "30", "ae_dynamic_rules": [{"_id": "c9aaea9"}], "slider_delay": "5000"}, "elements": [{"id": "12d747bf", "settings": {"_column_size": 33, "_inline_size": null, "content_position": "center", "background_background": "classic", "background_color": "#FFFFFF", "border_border": "solid", "border_width": {"unit": "px", "top": "1", "right": "1", "bottom": "1", "left": "1", "isLinked": true}, "border_color": "#F5F5F5", "border_radius": {"unit": "px", "top": "200", "right": "200", "bottom": "0", "left": "0", "isLinked": false}, "margin": {"unit": "px", "top": "10", "right": "10", "bottom": "10", "left": "10", "isLinked": true}, "padding": {"unit": "px", "top": "15", "right": "15", "bottom": "15", "left": "15", "isLinked": true}, "background_hover_background": "classic", "background_hover_color": "#F8FCFF", "box_shadow_box_shadow": {"horizontal": 0, "vertical": 0, "blur": 10, "spread": 0, "color": "rgba(255, 255, 255, 0.5)"}, "box_shadow_hover_box_shadow_type": "yes", "box_shadow_hover_box_shadow": {"horizontal": 0, "vertical": 0, "blur": 10, "spread": 0, "color": "rgba(255, 255, 255, 0.5)"}, "border_radius_mobile": {"unit": "px", "top": "90", "right": "90", "bottom": "0", "left": "0", "isLinked": false}, "ae_dynamic_rules": [{"_id": "580baa8"}], "slider_delay": "5000"}, "elements": [{"id": "695cbfba", "settings": {"title": "<br><br>Head Office USA", "align": "center", "title_color": "#000000", "typography_typography": "custom", "typography_font_family": "Nunito", "typography_font_size": {"unit": "px", "size": 30, "sizes": []}, "typography_font_weight": "300", "_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "-10", "left": "0", "isLinked": false}, "ae_dynamic_rules": [{"_id": "eddd99e"}]}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}, {"id": "1cd7f9fe", "settings": {"editor": "<p style=\"color: #a6a6a6;\">Chicago</p>", "text_color": "#A6A6A6", "typography_typography": "custom", "typography_font_family": "Nunito", "typography_font_size": {"unit": "px", "size": 20, "sizes": []}, "typography_font_weight": "600", "_element_id": "location-width", "align": "center", "ae_dynamic_rules": [{"_id": "f720d2f"}]}, "elements": [], "isInner": false, "widgetType": "text-editor", "elType": "widget"}], "isInner": true, "elType": "column"}, {"id": "b2d578b", "settings": {"_column_size": 33, "_inline_size": null, "content_position": "center", "background_background": "classic", "background_color": "#FFFFFF", "border_border": "solid", "border_width": {"unit": "px", "top": "1", "right": "1", "bottom": "1", "left": "1", "isLinked": true}, "border_color": "#F5F5F5", "border_radius": {"unit": "px", "top": "200", "right": "200", "bottom": "0", "left": "0", "isLinked": false}, "margin": {"unit": "px", "top": "10", "right": "10", "bottom": "10", "left": "10", "isLinked": true}, "padding": {"unit": "px", "top": "15", "right": "15", "bottom": "15", "left": "15", "isLinked": true}, "background_hover_background": "classic", "background_hover_color": "#F8FCFF", "box_shadow_box_shadow": {"horizontal": 0, "vertical": 0, "blur": 10, "spread": 0, "color": "rgba(255, 255, 255, 0.5)"}, "box_shadow_hover_box_shadow_type": "yes", "box_shadow_hover_box_shadow": {"horizontal": 0, "vertical": 0, "blur": 10, "spread": 0, "color": "rgba(255, 255, 255, 0.5)"}, "border_radius_mobile": {"unit": "px", "top": "90", "right": "90", "bottom": "0", "left": "0", "isLinked": false}, "ae_dynamic_rules": [{"_id": "e5569e5"}], "slider_delay": "5000"}, "elements": [{"id": "499ee651", "settings": {"title": "<br><br>Saudi Arabia Branch", "align": "center", "title_color": "#000000", "typography_typography": "custom", "typography_font_family": "Nunito", "typography_font_size": {"unit": "px", "size": 30, "sizes": []}, "typography_font_weight": "300", "_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "-10", "left": "0", "isLinked": false}, "ae_dynamic_rules": [{"_id": "2c93dd0"}]}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}, {"id": "6ccca5e6", "settings": {"editor": "<p style=\"color: #a6a6a6;\">Jeddah</p>", "text_color": "#A6A6A6", "typography_typography": "custom", "typography_font_family": "Nunito", "typography_font_size": {"unit": "px", "size": 20, "sizes": []}, "typography_font_weight": "600", "align": "center", "ae_dynamic_rules": [{"_id": "e337592"}]}, "elements": [], "isInner": false, "widgetType": "text-editor", "elType": "widget"}], "isInner": true, "elType": "column"}, {"id": "57ec97dd", "settings": {"_column_size": 33, "_inline_size": null, "content_position": "center", "background_background": "classic", "background_color": "#FFFFFF", "border_border": "solid", "border_width": {"unit": "px", "top": "1", "right": "1", "bottom": "1", "left": "1", "isLinked": true}, "border_color": "#F5F5F5", "border_radius": {"unit": "px", "top": "200", "right": "200", "bottom": "0", "left": "0", "isLinked": false}, "margin": {"unit": "px", "top": "10", "right": "10", "bottom": "10", "left": "10", "isLinked": true}, "padding": {"unit": "px", "top": "15", "right": "15", "bottom": "15", "left": "15", "isLinked": true}, "background_hover_background": "classic", "background_hover_color": "#F8FCFF", "box_shadow_box_shadow": {"horizontal": 0, "vertical": 0, "blur": 10, "spread": 0, "color": "rgba(255, 255, 255, 0.5)"}, "box_shadow_hover_box_shadow_type": "yes", "box_shadow_hover_box_shadow": {"horizontal": 0, "vertical": 0, "blur": 10, "spread": 0, "color": "rgba(255, 255, 255, 0.5)"}, "border_radius_mobile": {"unit": "px", "top": "90", "right": "90", "bottom": "0", "left": "0", "isLinked": false}, "ae_dynamic_rules": [{"_id": "1d8ad70"}], "slider_delay": "5000"}, "elements": [{"id": "28ac3144", "settings": {"title": "<br><br>Turkey Branch", "align": "center", "title_color": "#000000", "typography_typography": "custom", "typography_font_family": "Nunito", "typography_font_size": {"unit": "px", "size": 30, "sizes": []}, "typography_font_weight": "300", "_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "-10", "left": "0", "isLinked": false}, "ae_dynamic_rules": [{"_id": "05cb220"}]}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}, {"id": "7cb4118d", "settings": {"editor": "<p style=\"color: #a6a6a6;\">Istanbul</p>", "text_color": "#A6A6A6", "typography_typography": "custom", "typography_font_family": "Nunito", "typography_font_size": {"unit": "px", "size": 20, "sizes": []}, "typography_font_weight": "600", "align": "center", "ae_dynamic_rules": [{"_id": "9b53b4f"}]}, "elements": [], "isInner": false, "widgetType": "text-editor", "elType": "widget"}], "isInner": true, "elType": "column"}], "isInner": true, "elType": "section"}], "isInner": false, "elType": "column"}], "isInner": false, "elType": "section"}], "page_settings": [], "version": "0.4", "title": "Azoneus_aboutus", "type": "page"}