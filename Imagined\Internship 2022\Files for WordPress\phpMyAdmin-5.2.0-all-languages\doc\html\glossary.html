
<!DOCTYPE html>

<html>
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Glossary &#8212; phpMyAdmin 5.2.0 documentation</title>
    <link rel="stylesheet" href="_static/pygments.css" type="text/css" />
    <link rel="stylesheet" href="_static/classic.css" type="text/css" />
    
    <script id="documentation_options" data-url_root="./" src="_static/documentation_options.js"></script>
    <script src="_static/jquery.js"></script>
    <script src="_static/underscore.js"></script>
    <script src="_static/doctools.js"></script>
    
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
    <link rel="copyright" title="Copyright" href="copyright.html" />
    <link rel="prev" title="Credits" href="credits.html" /> 
  </head><body>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="credits.html" title="Credits"
             accesskey="P">previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="index.html">phpMyAdmin 5.2.0 documentation</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Glossary</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <div class="section" id="glossary">
<span id="id1"></span><h1>Glossary<a class="headerlink" href="#glossary" title="Permalink to this headline">¶</a></h1>
<p>From Wikipedia, the free encyclopedia</p>
<dl class="glossary">
<dt id="term-.htaccess">.htaccess</dt><dd><p>the default name of Apache’s directory-level configuration file.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/.htaccess">https://en.wikipedia.org/wiki/.htaccess</a>&gt;</p>
</div>
</dd>
<dt id="term-ACL">ACL</dt><dd><p>Access Control List</p>
</dd>
<dt id="term-Blowfish">Blowfish</dt><dd><p>a keyed, symmetric block cipher, designed in 1993 by <a class="reference external" href="https://en.wikipedia.org/wiki/Bruce_Schneier">Bruce Schneier</a>.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/Blowfish_(cipher)">https://en.wikipedia.org/wiki/Blowfish_(cipher)</a>&gt;</p>
</div>
</dd>
<dt id="term-Browser">Browser</dt><dd><p>a software application that enables a user to display and interact with text, images, and other information typically located on a web page at a website on the World Wide Web.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/Web_browser">https://en.wikipedia.org/wiki/Web_browser</a>&gt;</p>
</div>
</dd>
<dt id="term-bzip2">bzip2</dt><dd><p>a free software/open-source data compression algorithm and program developed by Julian Seward.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/Bzip2">https://en.wikipedia.org/wiki/Bzip2</a>&gt;</p>
</div>
</dd>
<dt id="term-CGI">CGI</dt><dd><p>Common Gateway Interface is an important World Wide Web technology that
enables a client web browser to request data from a program executed on
the web server.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/Common_Gateway_Interface">https://en.wikipedia.org/wiki/Common_Gateway_Interface</a>&gt;</p>
</div>
</dd>
<dt id="term-Changelog">Changelog</dt><dd><p>a log or record of changes made to a project.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/Changelog">https://en.wikipedia.org/wiki/Changelog</a>&gt;</p>
</div>
</dd>
<dt id="term-Client">Client</dt><dd><p>a computer system that accesses a (remote) service on another computer by some kind of network.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/Client_(computing)">https://en.wikipedia.org/wiki/Client_(computing)</a>&gt;</p>
</div>
</dd>
<dt id="term-column">column</dt><dd><p>a set of data values of a particularly simple type, one for each row of the table.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/Column_(database)">https://en.wikipedia.org/wiki/Column_(database)</a>&gt;</p>
</div>
</dd>
<dt id="term-Cookie">Cookie</dt><dd><p>a packet of information sent by a server to a World Wide Web browser and then sent back by the browser each time it accesses that server.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/HTTP_cookie">https://en.wikipedia.org/wiki/HTTP_cookie</a>&gt;</p>
</div>
</dd>
<dt id="term-CSV">CSV</dt><dd><p>Comma-separated values</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/Comma-separated_values">https://en.wikipedia.org/wiki/Comma-separated_values</a>&gt;</p>
</div>
</dd>
<dt id="term-DB">DB</dt><dd><p>look at <a class="reference internal" href="#term-Database"><span class="xref std std-term">Database</span></a></p>
</dd>
<dt id="term-Database">Database</dt><dd><p>an organized collection of data.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/Database">https://en.wikipedia.org/wiki/Database</a>&gt;</p>
</div>
</dd>
<dt id="term-Engine">Engine</dt><dd><p>look at <a class="reference internal" href="#term-Storage-Engines"><span class="xref std std-term">Storage Engines</span></a></p>
</dd>
<dt id="term-PHP-extension">PHP extension</dt><dd><p>a PHP module that extends PHP with additional functionality.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/Software_extension">https://en.wikipedia.org/wiki/Software_extension</a>&gt;</p>
</div>
</dd>
<dt id="term-FAQ">FAQ</dt><dd><p>Frequently Asked Questions is a list of commonly asked question and their
answers.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/FAQ">https://en.wikipedia.org/wiki/FAQ</a>&gt;</p>
</div>
</dd>
<dt id="term-Field">Field</dt><dd><p>one part of divided data/columns.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/Field_(computer_science)">https://en.wikipedia.org/wiki/Field_(computer_science)</a>&gt;</p>
</div>
</dd>
<dt id="term-Foreign-key">Foreign key</dt><dd><p>a column or group of columns in a database row that points to a key column
or group of columns forming a key of another database row in some
(usually different) table.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/Foreign_key">https://en.wikipedia.org/wiki/Foreign_key</a>&gt;</p>
</div>
</dd>
<dt id="term-GD">GD</dt><dd><p>Graphics Library by Thomas Boutell and others for dynamically manipulating images.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/GD_Graphics_Library">https://en.wikipedia.org/wiki/GD_Graphics_Library</a>&gt;</p>
</div>
</dd>
<dt id="term-GD2">GD2</dt><dd><p>look at <a class="reference internal" href="#term-GD"><span class="xref std std-term">GD</span></a></p>
</dd>
<dt id="term-GZip">GZip</dt><dd><p>GZip is short for GNU zip, a GNU free software file compression program.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/Gzip">https://en.wikipedia.org/wiki/Gzip</a>&gt;</p>
</div>
</dd>
<dt id="term-host">host</dt><dd><p>any machine connected to a computer network, a node that has a hostname.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/Host_(network)">https://en.wikipedia.org/wiki/Host_(network)</a>&gt;</p>
</div>
</dd>
<dt id="term-hostname">hostname</dt><dd><p>the unique name by which a network-attached device is known on a network.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/Hostname">https://en.wikipedia.org/wiki/Hostname</a>&gt;</p>
</div>
</dd>
<dt id="term-HTTP">HTTP</dt><dd><p>Hypertext Transfer Protocol is the primary method used to transfer or
convey information on the World Wide Web.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/HyperText_Transfer_Protocol">https://en.wikipedia.org/wiki/HyperText_Transfer_Protocol</a>&gt;</p>
</div>
</dd>
<dt id="term-HTTPS">HTTPS</dt><dd><p>a <a class="reference internal" href="#term-HTTP"><span class="xref std std-term">HTTP</span></a>-connection with additional security measures.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/HTTPS">https://en.wikipedia.org/wiki/HTTPS</a>&gt;</p>
</div>
</dd>
<dt id="term-IEC">IEC</dt><dd><p>International Electrotechnical Commission</p>
</dd>
<dt id="term-IIS">IIS</dt><dd><p>Internet Information Services is a set of internet-based services for
servers using Microsoft Windows.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/Internet_Information_Services">https://en.wikipedia.org/wiki/Internet_Information_Services</a>&gt;</p>
</div>
</dd>
<dt id="term-Index">Index</dt><dd><p>a feature that allows quick access to the rows in a table.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/Database_index">https://en.wikipedia.org/wiki/Database_index</a>&gt;</p>
</div>
</dd>
<dt id="term-IP">IP</dt><dd><p>“Internet Protocol” is a data-oriented protocol used by source and
destination hosts for communicating data across a packet-switched
internetwork.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/Internet_Protocol">https://en.wikipedia.org/wiki/Internet_Protocol</a>&gt;</p>
</div>
</dd>
<dt id="term-IP-Address">IP Address</dt><dd><p>a unique number that devices use in order to identify and communicate with each other on a network utilizing the Internet Protocol standard.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/IP_Address">https://en.wikipedia.org/wiki/IP_Address</a>&gt;</p>
</div>
</dd>
<dt id="term-IPv6">IPv6</dt><dd><p>IPv6 (Internet Protocol version 6) is the latest revision of the
Internet Protocol (<a class="reference internal" href="#term-IP"><span class="xref std std-term">IP</span></a>), designed to deal with the
long-anticipated problem of its predecessor IPv4 running out of addresses.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/IPv6">https://en.wikipedia.org/wiki/IPv6</a>&gt;</p>
</div>
</dd>
<dt id="term-ISAPI">ISAPI</dt><dd><p>Internet Server Application Programming Interface is the API of Internet Information Services (IIS).</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/Internet_Server_Application_Programming_Interface">https://en.wikipedia.org/wiki/Internet_Server_Application_Programming_Interface</a>&gt;</p>
</div>
</dd>
<dt id="term-ISP">ISP</dt><dd><p>An Internet service provider is a business or organization that offers users
access to the Internet and related services.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/Internet_service_provider">https://en.wikipedia.org/wiki/Internet_service_provider</a>&gt;</p>
</div>
</dd>
<dt id="term-ISO">ISO</dt><dd><p>International Standards Organization</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><a class="reference external" href="https://www.iso.org/about-us.html">ISO organization website</a></p>
</div>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/International_Organization_for_Standardization">https://en.wikipedia.org/wiki/International_Organization_for_Standardization</a>&gt;</p>
</div>
</dd>
<dt id="term-JPEG">JPEG</dt><dd><p>a most commonly used standard method of lossy compression for photographic images.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/JPEG">https://en.wikipedia.org/wiki/JPEG</a>&gt;</p>
</div>
</dd>
<dt id="term-JPG">JPG</dt><dd><p>look at <a class="reference internal" href="#term-JPEG"><span class="xref std std-term">JPEG</span></a></p>
</dd>
<dt id="term-Key">Key</dt><dd><p>look at <a class="reference internal" href="#term-Index"><span class="xref std std-term">Index</span></a></p>
</dd>
<dt id="term-LATEX">LATEX</dt><dd><p>a document preparation system for the TeX typesetting program.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/LaTeX">https://en.wikipedia.org/wiki/LaTeX</a>&gt;</p>
</div>
</dd>
<dt id="term-Mac">Mac</dt><dd><p>Apple Macintosh is a line of personal computers designed, developed, manufactured, and marketed by Apple Inc.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/Macintosh">https://en.wikipedia.org/wiki/Macintosh</a>&gt;</p>
</div>
</dd>
<dt id="term-macOS">macOS</dt><dd><p>the operating system which is included with all currently shipping Apple Macintosh computers in the consumer and professional markets.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/MacOS">https://en.wikipedia.org/wiki/MacOS</a>&gt;</p>
</div>
</dd>
<dt id="term-mbstring">mbstring</dt><dd><p>The PHP <cite>mbstring</cite> functions provide support for languages represented by multi-byte character sets, most notably UTF-8.</p>
<p>If you have troubles installing this extension, please follow <a class="reference internal" href="faq.html#faqmysql"><span class="std std-ref">1.20 I receive an error about missing mysqli and mysql extensions.</span></a>, it provides useful hints.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>&lt;<a class="reference external" href="https://www.php.net/manual/en/book.mbstring.php">https://www.php.net/manual/en/book.mbstring.php</a>&gt;</p>
</div>
</dd>
<dt id="term-Media-type">Media type</dt><dd><p>A media type (formerly known as MIME type) is a two-part identifier
for file formats and format contents transmitted on the Internet.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/Media_type">https://en.wikipedia.org/wiki/Media_type</a>&gt;</p>
</div>
</dd>
<dt id="term-MIME">MIME</dt><dd><p>Multipurpose Internet Mail Extensions is
an Internet Standard for the format of e-mail.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/MIME">https://en.wikipedia.org/wiki/MIME</a>&gt;</p>
</div>
</dd>
<dt id="term-module">module</dt><dd><p>modular extension for the Apache HTTP Server httpd.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/Apache_HTTP_Server">https://en.wikipedia.org/wiki/Apache_HTTP_Server</a>&gt;</p>
</div>
</dd>
<dt id="term-mod_proxy_fcgi">mod_proxy_fcgi</dt><dd><p>an Apache module implementing a Fast CGI interface; PHP can be run as a CGI module, FastCGI, or
directly as an Apache module.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/Mod_proxy">https://en.wikipedia.org/wiki/Mod_proxy</a>&gt;</p>
</div>
</dd>
<dt id="term-MySQL">MySQL</dt><dd><p>a multithreaded, multi-user, SQL (Structured Query Language) Database Management System (DBMS).</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/MySQL">https://en.wikipedia.org/wiki/MySQL</a>&gt;</p>
</div>
</dd>
<dt id="term-MySQLi">MySQLi</dt><dd><p>the improved MySQL client PHP extension.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><a class="reference external" href="https://www.php.net/manual/en/book.mysqli.php">PHP manual for MySQL Improved Extension</a></p>
</div>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/MySQLi">https://en.wikipedia.org/wiki/MySQLi</a>&gt;</p>
</div>
</dd>
<dt id="term-mysql">mysql</dt><dd><p>the MySQL client PHP extension.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>&lt;<a class="reference external" href="https://www.php.net/manual/en/book.mysql.php">https://www.php.net/manual/en/book.mysql.php</a>&gt;</p>
</div>
</dd>
<dt id="term-OpenDocument">OpenDocument</dt><dd><p>an open standard for office documents.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/OpenDocument">https://en.wikipedia.org/wiki/OpenDocument</a>&gt;</p>
</div>
</dd>
<dt id="term-OS-X">OS X</dt><dd><p>look at <a class="reference internal" href="#term-macOS"><span class="xref std std-term">macOS</span></a>.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/MacOS">https://en.wikipedia.org/wiki/MacOS</a>&gt;</p>
</div>
</dd>
<dt id="term-PDF">PDF</dt><dd><p>Portable Document Format is a file format developed by Adobe Systems for
representing two-dimensional documents in a device-independent and
resolution-independent format.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/PDF">https://en.wikipedia.org/wiki/PDF</a>&gt;</p>
</div>
</dd>
<dt id="term-PEAR">PEAR</dt><dd><p>the PHP Extension and Application Repository.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><a class="reference external" href="https://pear.php.net/">PEAR website</a></p>
</div>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><a class="reference external" href="https://en.wikipedia.org/wiki/PEAR">Wikipedia page for PEAR</a></p>
</div>
</dd>
<dt id="term-PCRE">PCRE</dt><dd><p>Perl-Compatible Regular Expressions is the Perl-compatible regular
expression functions for PHP</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>&lt;<a class="reference external" href="https://www.php.net/pcre">https://www.php.net/pcre</a>&gt;</p>
</div>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><a class="reference external" href="https://www.php.net/pcre">PHP manual for Perl-Compatible Regular Expressions</a></p>
</div>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/Perl_Compatible_Regular_Expressions">https://en.wikipedia.org/wiki/Perl_Compatible_Regular_Expressions</a>&gt;</p>
</div>
</dd>
<dt id="term-PHP">PHP</dt><dd><p>short for “PHP: Hypertext Preprocessor”, is an open-source, reflective
programming language used mainly for developing server-side applications
and dynamic web content, and more recently, a broader range of software
applications.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/PHP">https://en.wikipedia.org/wiki/PHP</a>&gt;</p>
</div>
</dd>
<dt id="term-port">port</dt><dd><p>a connection through which data is sent and received.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/Port_(computer_networking)">https://en.wikipedia.org/wiki/Port_(computer_networking)</a>&gt;</p>
</div>
</dd>
<dt id="term-primary-key">primary key</dt><dd><p>A primary key is an index over one or more fields in a table with
unique values for every single row in this table. Every table should have
a primary key for easier accessing/identifying data in this table. There
can only be one primary key per table and it is named always <strong>PRIMARY</strong>.
In fact, a primary key is just an <a class="reference internal" href="#term-unique-key"><span class="xref std std-term">unique key</span></a> with the name
<strong>PRIMARY</strong>. If no primary key is defined MySQL will use first <em>unique
key</em> as primary key if there is one.</p>
<p>You can create the primary key when creating the table (in phpMyAdmin
just check the primary key radio buttons for each field you wish to be
part of the primary key).</p>
<p>You can also add a primary key to an existing table with <cite>ALTER</cite> <cite>TABLE</cite>
or <cite>CREATE</cite> <cite>INDEX</cite> (in phpMyAdmin you can just click on ‘add index’ on
the table structure page below the listed fields).</p>
</dd>
<dt id="term-RFC">RFC</dt><dd><p>Request for Comments (RFC) documents are a series of memoranda
encompassing new research, innovations, and methodologies applicable to
Internet technologies.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/Request_for_Comments">https://en.wikipedia.org/wiki/Request_for_Comments</a>&gt;</p>
</div>
</dd>
<dt id="term-RFC-1952">RFC 1952</dt><dd><p>GZIP file format specification version 4.3</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><span class="target" id="index-0"></span><a class="rfc reference external" href="https://tools.ietf.org/html/rfc1952.html"><strong>RFC 1952</strong></a></p>
</div>
</dd>
<dt id="term-Row-record-tuple">Row (record, tuple)</dt><dd><p>represents a single, implicitly structured data item in a table.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/Row_(database)">https://en.wikipedia.org/wiki/Row_(database)</a>&gt;</p>
</div>
</dd>
<dt id="term-Server">Server</dt><dd><p>a computer system that provides services to other computing systems over a network.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/Server_(computing)">https://en.wikipedia.org/wiki/Server_(computing)</a>&gt;</p>
</div>
</dd>
<dt id="term-Storage-Engines">Storage Engines</dt><dd><p>MySQL can use several different formats for storing data on disk, these
are called storage engines or table types. phpMyAdmin allows a user to
change their storage engine for a particular table through the operations
tab.</p>
<p>Common table types are InnoDB and MyISAM, though many others exist and
may be desirable in some situations.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><a class="reference external" href="https://dev.mysql.com/doc/refman/8.0/en/storage-engines.html">MySQL doc chapter about Alternative Storage Engines</a></p>
</div>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/Database_engine">https://en.wikipedia.org/wiki/Database_engine</a>&gt;</p>
</div>
</dd>
<dt id="term-socket">socket</dt><dd><p>a form of inter-process communication.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/Unix_domain_socket">https://en.wikipedia.org/wiki/Unix_domain_socket</a>&gt;</p>
</div>
</dd>
<dt id="term-SSL">SSL</dt><dd><p>Secure Sockets Layer, (now superseded by TLS) is a cryptographic protocol
which provides secure communication on the Internet.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/Transport_Layer_Security">https://en.wikipedia.org/wiki/Transport_Layer_Security</a>&gt;</p>
</div>
</dd>
<dt id="term-Stored-procedure">Stored procedure</dt><dd><p>a subroutine available to applications accessing a relational database system</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/Stored_procedure">https://en.wikipedia.org/wiki/Stored_procedure</a>&gt;</p>
</div>
</dd>
<dt id="term-SQL">SQL</dt><dd><p>Structured Query Language</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/SQL">https://en.wikipedia.org/wiki/SQL</a>&gt;</p>
</div>
</dd>
<dt id="term-table">table</dt><dd><p>a set of data elements (cells) that is organized, defined and stored as
horizontal rows and vertical columns where each item can be uniquely
identified by a label or key or by its position in relation to other
items.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/Table_(database)">https://en.wikipedia.org/wiki/Table_(database)</a>&gt;</p>
</div>
</dd>
<dt id="term-tar">tar</dt><dd><p>a type of archive file format, from “Tape Archive”.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/Tar_(computing)">https://en.wikipedia.org/wiki/Tar_(computing)</a>&gt;</p>
</div>
</dd>
<dt id="term-TCP">TCP</dt><dd><p>Transmission Control Protocol is one of the core protocols of the
Internet protocol suite.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/Internet_protocol_suite">https://en.wikipedia.org/wiki/Internet_protocol_suite</a>&gt;</p>
</div>
</dd>
<dt id="term-TCPDF">TCPDF</dt><dd><p>PHP library to generate PDF files.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>&lt;<a class="reference external" href="https://tcpdf.org/">https://tcpdf.org/</a>&gt;</p>
</div>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/TCPDF">https://en.wikipedia.org/wiki/TCPDF</a>&gt;</p>
</div>
</dd>
<dt id="term-trigger">trigger</dt><dd><p>a procedural code that is automatically executed in response to certain events on a particular table or view in a database</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/Database_trigger">https://en.wikipedia.org/wiki/Database_trigger</a>&gt;</p>
</div>
</dd>
<dt id="term-unique-key">unique key</dt><dd><p>A unique key is an index over one or more fields in a table which has a
unique value for each row.  The first unique key will be treated as
<a class="reference internal" href="#term-primary-key"><span class="xref std std-term">primary key</span></a> if there is no <em>primary key</em> defined.</p>
</dd>
<dt id="term-URL">URL</dt><dd><p>Uniform Resource Locator is a sequence of characters, conforming to a
standardized format, that is used for referring to resources, such as
documents and images on the Internet, by their location.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/URL">https://en.wikipedia.org/wiki/URL</a>&gt;</p>
</div>
</dd>
<dt id="term-Web-server">Web server</dt><dd><p>A computer (program) that is responsible for accepting HTTP requests from clients and serving them web pages.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/Web_server">https://en.wikipedia.org/wiki/Web_server</a>&gt;</p>
</div>
</dd>
<dt id="term-XML">XML</dt><dd><p>Extensible Markup Language is a W3C-recommended general-purpose markup
language for creating special-purpose markup languages, capable of
describing many different kinds of data.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/XML">https://en.wikipedia.org/wiki/XML</a>&gt;</p>
</div>
</dd>
<dt id="term-ZIP">ZIP</dt><dd><p>a popular data compression and archival format.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/Zip_(file_format)">https://en.wikipedia.org/wiki/Zip_(file_format)</a>&gt;</p>
</div>
</dd>
<dt id="term-Zlib">Zlib</dt><dd><p>an open-source, cross-platform data compression library by <a class="reference external" href="https://en.wikipedia.org/wiki/Jean-Loup_Gailly">Jean-loup Gailly</a> and <a class="reference external" href="https://en.wikipedia.org/wiki/Mark_Adler">Mark Adler</a>.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/Zlib">https://en.wikipedia.org/wiki/Zlib</a>&gt;</p>
</div>
</dd>
<dt id="term-Content-Security-Policy">Content Security Policy</dt><dd><p>The HTTP <cite>Content-Security-Policy</cite> response header allows web site administrators
to control resources the user agent is allowed to load for a given page.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/Content_Security_Policy">https://en.wikipedia.org/wiki/Content_Security_Policy</a>&gt;</p>
</div>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>&lt;<a class="reference external" href="https://developer.mozilla.org/en/docs/Web/HTTP/CSP">https://developer.mozilla.org/en/docs/Web/HTTP/CSP</a>&gt;</p>
</div>
</dd>
</dl>
</div>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <h4>Previous topic</h4>
  <p class="topless"><a href="credits.html"
                        title="previous chapter">Credits</a></p>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="_sources/glossary.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" />
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>$('#searchbox').show(0);</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="credits.html" title="Credits"
             >previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="index.html">phpMyAdmin 5.2.0 documentation</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Glossary</a></li> 
      </ul>
    </div>
    <div class="footer" role="contentinfo">
        &#169; <a href="copyright.html">Copyright</a> 2012 - 2021, The phpMyAdmin devel team.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 3.4.3.
    </div>
  </body>
</html>