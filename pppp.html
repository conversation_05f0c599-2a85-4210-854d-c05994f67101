<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>مواقيت الصلاة</title>
  <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;600;700&family=Reem+Kufi:wght@400;600;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <style>
    :root {
      --gold-primary: #DAA520;
      --gold-secondary: #FFD700;
      --gold-tertiary: #F0E68C;
      --brown-primary: #8B4513;
      --brown-secondary: #A0522D;
      --brown-tertiary: #CD853F;
      --green-primary: #2C5530;
      --green-secondary: #3D8B40;
      --blue-primary: #1a3a5f;
      --white-primary: #FFFDF7;
      --white-secondary: #F8F4E9;
      --shadow-light: 0 4px 20px rgba(0,0,0,0.08);
      --shadow-medium: 0 8px 30px rgba(0,0,0,0.15);
      --shadow-heavy: 0 12px 40px rgba(0,0,0,0.2);
      --prayer-point-size: 60px;
    }
    
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      background: linear-gradient(135deg, #f8f4e9 0%, #e9e1d0 100%);
      min-height: 100vh;
      font-family: 'Amiri', serif;
      direction: rtl;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 20px;
      color: #333;
    }
    
    .container {
      max-width: 1200px;
      width: 100%;
      margin: 0 auto;
      padding: 2rem;
      display: grid;
      grid-template-columns: 1fr;
      gap: 2rem;
      background-color: var(--white-primary);
      border-radius: 20px;
      box-shadow: var(--shadow-medium);
    }

    .content-box {
      padding: 2rem;
      background-color: var(--white-secondary);
      border-radius: 15px;
      box-shadow: var(--shadow-medium);
      position: relative;
      overflow: hidden;
      transition: opacity 0.3s ease;
    }

    .content-box.loading {
      opacity: 0.7;
    }

    .content-box:not(.active) {
      display: none;
    }

    .verse-text,
    .hadith-text {
      font-size: 1.3rem;
      line-height: 1.8;
      margin-bottom: 1rem;
      color: var(--white-primary);
      text-align: justify;
    }

    .verse-source,
    .hadith-source {
      color: var(--gold-primary);
      font-size: 1.1rem;
      text-align: left;
    }

    .main-card {
      background: var(--white-primary);
      border-radius: 24px;
      box-shadow: var(--shadow-heavy);
      overflow: hidden;
      border: 1px solid rgba(139, 69, 19, 0.1);
      display: grid;
      grid-template-columns: 1fr 1fr;
      grid-template-rows: auto 1fr;
      position: relative;
    }

    .main-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 6px;
      background: linear-gradient(90deg, var(--brown-primary), var(--gold-primary), var(--brown-primary));
      border-radius: 24px 24px 0 0;
    }

    .islamic-pattern {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      opacity: 0.08;
      background-image: 
        url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%238b4513' fill-opacity='0.1' fill-rule='evenodd'/%3E%3C/svg%3E");
      pointer-events: none;
    }

    .header-section {
      grid-column: 1 / -1;
      background: linear-gradient(135deg, var(--white-primary) 0%, var(--white-secondary) 100%);
      padding: 0;
      display: grid;
      grid-template-columns: 0.75fr 1fr 0.75fr;
      align-items: center;
      border-bottom: 1px solid rgba(139, 69, 19, 0.1);
      gap: 30px;
    }

    .current-prayer-status {
      text-align: right;
      position: relative;
      padding: 25px;
      background: linear-gradient(135deg, var(--brown-primary) 0%, var(--brown-secondary) 100%);
      color: white;
      box-shadow: var(--shadow-heavy);
      transform: perspective(1000px) rotateX(2deg);
      transition: all 0.4s ease;
      z-index: 2;
    }

    .current-prayer-status:hover {
      box-shadow: 0 15px 35px rgba(139, 69, 19, 0.4);
    }

    .current-prayer-label {
      font-size: 18px;
      font-weight: 600;
      color: var(--gold-secondary);
      margin-bottom: 15px;
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .current-prayer-name {
      font-size: 36px;
      font-weight: 700;
      color: white;
      margin-bottom: 10px;
      text-shadow: 0 2px 4px rgba(0,0,0,0.2);
      letter-spacing: 1px;
    }

    .current-prayer-time {
      font-size: 32px;
      font-weight: 700;
      color: var(--gold-secondary);
      margin-bottom: 18px;
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .time-remaining {
      font-size: 18px;
      color: white;
      font-weight: 600;
      background: rgba(255, 215, 0, 0.2);
      padding: 12px 20px;
      border-radius: 30px;
      display: inline-flex;
      align-items: center;
      gap: 10px;
      margin-bottom: 15px;
      border: 1px solid rgba(255, 215, 0, 0.3);
    }

    .location-name {
      font-size: 18px;
      color: var(--gold-tertiary);
      font-weight: 600;
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .main-title {
      font-size: 48px;
      font-weight: 700;
      color: var(--brown-primary);
      text-align: center;
      font-family: 'Reem Kufi', sans-serif;
      margin: 50px;
      padding: 0 40px;
      position: relative;
      display: inline-block;
      text-shadow: 0 2px 4px rgba(0,0,0,0.1);
      letter-spacing: 1px;
    }

    .main-title::before, .main-title::after {
      content: "✧";
      color: var(--gold-primary);
      font-size: 36px;
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      animation: glow 3s ease-in-out infinite alternate;
    }

    .main-title::before {
      right: -25px;
    }

    .main-title::after {
      left: -25px;
    }

    @keyframes glow {
      from { opacity: 0.6; text-shadow: 0 0 5px var(--gold-secondary); }
      to { opacity: 1; text-shadow: 0 0 15px var(--gold-secondary); }
    }

    .dates-section {
      text-align: left;
      padding: 25px;
      background: linear-gradient(135deg, var(--white-secondary) 0%, var(--white-primary) 100%);
      border-radius: 18px;
      border: 1px solid rgba(139, 69, 19, 0.1);
      box-shadow: var(--shadow-light);
      z-index: 2;
    }

    .date-row {
      margin-bottom: 18px;
      position: relative;
    }

    .date-label {
      font-size: 18px;
      color: var(--brown-primary);
      font-weight: 600;
      margin-bottom: 8px;
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .date-value {
      font-size: 24px;
      font-weight: 700;
      color: var(--green-primary);
      display: flex;
      align-items: center;
      gap: 15px;
    }

    .moon-phase {
      font-size: 26px;
      color: var(--gold-primary);
      animation: moonGlow 4s ease-in-out infinite;
    }

    @keyframes moonGlow {
      0%, 100% { opacity: 0.8; transform: scale(1); }
      50% { opacity: 1; transform: scale(1.1); }
    }

    .current-time {
      font-size: 20px;
      color: #555;
      font-weight: 600;
      margin-top: 15px;
      display: flex;
      align-items: center;
      gap: 10px;
      padding: 10px 15px;
      background: rgba(139, 69, 19, 0.05);
      border-radius: 10px;
    }

    /* Main Content Area */
    .content-section {
      grid-column: 1 / -1;
      display: grid;
      grid-template-columns: 2fr 1fr;
      height: 100%;
    }

    /* Prayer Timeline Section */
    .prayer-timeline-section {
      padding: 35px;
      display: flex;
      flex-direction: column;
      background: linear-gradient(135deg, var(--white-secondary) 0%, var(--white-primary) 100%);
      border-right: 1px solid rgba(139, 69, 19, 0.1);
    }

    .section-title {
      font-size: 32px;
      font-weight: 700;
      color: var(--brown-primary);
      text-align: center;
      margin-bottom: 30px;
      position: relative;
      padding-bottom: 25px;
    }

    .section-title::after {
      content: '';
      position: absolute;
      bottom: 0;
      right: 20%;
      left: 20%;
      height: 4px;
      background: linear-gradient(90deg, transparent, var(--gold-primary), transparent);
      border-radius: 2px;
    }

    /* Enhanced Circular Timeline */
    .timeline-container {
      position: relative;
      width: 100%;
      aspect-ratio: 1.5 / 1;
      min-width: 0;
      min-height: 0;
      box-sizing: border-box;
    }

    #prayer-timeline-svg {
      width: 100%;
      max-width: 500px;
      display: block;
      margin: auto;
      position: relative;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }

    .timeline-circle {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 90%; /* More responsive width */
      max-width: 400px;
      height: 90%;
      max-height: 400px;
      border-radius: 50%;
      border: 3px solid var(--brown-primary);
      z-index: 1;
    }

    .timeline-path {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 420px;
      height: 420px;
      border-radius: 50%;
      border: 2px dashed var(--brown-tertiary);
      opacity: 0.5;
    }

    .timeline-path,
    #prayer-timeline-svg {
      width: 100%;
      height: 100%;
      max-width: 500px;
      aspect-ratio: 1 / 1;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      box-sizing: border-box;
    }

    .prayer-point {
      position: absolute;
      width: var(--prayer-point-size);
      height: var(--prayer-point-size);
      box-sizing: border-box;
      /* Remove transform: translate(-50%, -50%) */
      display: flex;
      align-items: center;
      justify-content: center;
      pointer-events: auto;
      margin: 0;
      padding: 0;
      border: none;
    }

    .prayer-point-inner {
      width: 100%;
      height: 100%;
      border-radius: 50%;
      background: linear-gradient(135deg, rgba(255,255,255,0.75) 60%, var(--gold-tertiary) 100%),
                  linear-gradient(120deg, rgba(255,255,255,0.18) 0%, rgba(218,165,32,0.10) 100%);
      backdrop-filter: blur(6px) saturate(1.3);
      -webkit-backdrop-filter: blur(6px) saturate(1.3);
      border: 2.5px solid var(--gold-primary);
      box-shadow:
        0 0 0 4px rgba(218,165,32,0.10),
        0 2px 16px 0 rgba(218,165,32,0.13),
        0 1.5px 8px 0 rgba(139,69,19,0.10),
        0 0 0 1.5px rgba(255,255,255,0.18) inset,
        0 2px 8px 0 rgba(139,69,19,0.10) inset;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      filter: drop-shadow(0 0 12px rgba(218,165,32,0.13));
      transition: all 0.35s cubic-bezier(0.25, 0.8, 0.25, 1), box-shadow 0.25s;
      font-smooth: always;
      -webkit-font-smoothing: antialiased;
    }

    .prayer-point:hover .prayer-point-inner {
      transform: scale(1.15) rotateZ(2deg);
      box-shadow:
        0 0 0 10px rgba(218,165,32,0.18),
        0 8px 32px rgba(218,165,32,0.22),
        0 0 24px 0 rgba(255,255,255,0.18) inset;
      filter: drop-shadow(0 0 24px rgba(218,165,32,0.22));
      z-index: 12;
    }

    .prayer-point.current .prayer-point-inner {
      background: linear-gradient(135deg, rgba(255,255,255,0.92) 40%, var(--gold-secondary) 100%),
                  linear-gradient(120deg, rgba(255,255,255,0.22) 0%, rgba(218,165,32,0.18) 100%);
      border: 2.5px solid var(--gold-secondary);
      box-shadow:
        0 0 0 16px rgba(218,165,32,0.22),
        0 0 0 8px rgba(139,69,19,0.13),
        0 0 0 2.5px rgba(255,255,255,0.22) inset,
        0 2px 12px 0 rgba(218,165,32,0.18) inset;
      transform: scale(1.22);
      z-index: 14;
    }

    .prayer-point.sunrise .prayer-point-inner {
      background: radial-gradient(circle at 60% 40%, #fffbe6 60%, #ffeaa7 100%),
                  linear-gradient(135deg, #fffbe6 60%, #ffeaa7 100%);
      border: 2.5px solid var(--gold-primary);
      box-shadow:
        0 0 0 14px rgba(255,215,0,0.18),
        0 0 0 4px rgba(255,215,0,0.13),
        0 2px 12px 0 rgba(255,215,0,0.10),
        0 0 0 2.5px rgba(255,255,255,0.18) inset;
      filter: drop-shadow(0 0 24px rgba(255,215,0,0.22));
      opacity: 0.99;
    }

    .prayer-name, .prayer-time {
      text-shadow: 0 1px 4px rgba(218,165,32,0.10), 0 0.5px 0.5px #fff;
      font-family: 'Reem Kufi', 'Amiri', serif;
    }

    .prayer-point-inner .prayer-icon {
      color: var(--gold-primary);
      text-shadow: 0 1px 8px rgba(218,165,32,0.18), 0 0.5px 0.5px #fff;
      font-size: 24px;
      margin-bottom: 5px;
    }

    /* Enhanced Progress indicator system with celestial body */
    .progress-container {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 100%;
      height: 100%;
      max-width: none;
      max-height: none;
      z-index: 5; /* Higher z-index to be above everything */
      /* Ensure exact alignment with SVG */
      pointer-events: none;
      /* Match the timeline container exactly */
      box-sizing: border-box;
    }

    .progress-indicator {
      position: absolute;
      width: 40px;
      height: 40px;
      transition: all 1.2s cubic-bezier(0.34, 1.56, 0.64, 1);
      z-index: 100;
      display: flex;
      align-items: center;
      justify-content: center;
      /* Default position in case calculation fails */
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }

    .celestial-body {
      width: 100%;
      height: 100%;
      border-radius: 50%;
      position: relative;
      transition: all 1.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 0 20px rgba(0, 0, 0, 0.4);
      overflow: hidden;
    }

    .celestial-body::before {
      content: '';
      position: absolute;
      top: -50%;
      left: -50%;
      width: 200%;
      height: 200%;
      background: conic-gradient(
        transparent 0deg,
        rgba(255, 255, 255, 0.3) 5deg,
        transparent 15deg,
        rgba(255, 255, 255, 0.2) 20deg,
        transparent 30deg,
        rgba(255, 255, 255, 0.3) 35deg,
        transparent 45deg,
        rgba(255, 255, 255, 0.2) 50deg,
        transparent 60deg,
        rgba(255, 255, 255, 0.3) 65deg,
        transparent 75deg,
        rgba(255, 255, 255, 0.2) 80deg,
        transparent 90deg,
        rgba(255, 255, 255, 0.3) 95deg,
        transparent 105deg,
        rgba(255, 255, 255, 0.2) 110deg,
        transparent 120deg
      );
      animation: rotateSunRays 15s linear infinite;
      opacity: 0;
      transition: opacity 1.2s ease;
      border-radius: 50%;
    }

    .day-sun::before,
    .sunrise-sun::before,
    .sunset-sun::before {
      opacity: 1;
    }

    .night-moon {
      background:
        radial-gradient(circle at 65% 25%, rgba(0, 0, 0, 0.15) 8%, transparent 12%),
        radial-gradient(circle at 40% 70%, rgba(0, 0, 0, 0.1) 6%, transparent 10%),
        radial-gradient(circle at 80% 60%, rgba(0, 0, 0, 0.12) 4%, transparent 8%),
        radial-gradient(circle at 30% 30%, #d0e3f7 0%, #8bb5e8 40%, #3a5a9a 100%);
      box-shadow:
        0 0 25px rgba(91, 147, 255, 0.6),
        inset -8px -8px 15px rgba(0, 0, 0, 0.25),
        inset 2px 2px 4px rgba(0, 0, 0, 0.1),
        inset -2px -2px 4px rgba(0, 0, 0, 0.1);
      animation: moonGlow 4s ease-in-out infinite;
    }

    .predawn-moon {
      background:
        radial-gradient(circle at 60% 30%, rgba(0, 0, 0, 0.1) 7%, transparent 11%),
        radial-gradient(circle at 35% 65%, rgba(0, 0, 0, 0.08) 5%, transparent 9%),
        radial-gradient(circle at 30% 30%, #e6f0ff 0%, #b3d1ff 40%, #5a8cff 100%);
      box-shadow:
        0 0 20px rgba(91, 147, 255, 0.5),
        inset -6px -6px 12px rgba(0, 0, 0, 0.15),
        inset 2px 2px 4px rgba(0, 0, 0, 0.1);
      animation: moonGlow 4s ease-in-out infinite;
    }

    .sunrise-sun {
      background: radial-gradient(circle at 30% 30%, #ffeb3b 0%, #ff9800 50%, #ff5722 100%);
      box-shadow:
        0 0 25px rgba(255, 152, 0, 0.8),
        0 0 50px rgba(255, 87, 34, 0.5),
        0 0 80px rgba(255, 193, 7, 0.3);
      animation: sunPulse 3s ease-in-out infinite;
    }

    .day-sun {
      background: radial-gradient(circle at 30% 30%, #fffde7 0%, #ffeb3b 50%, #ffc107 100%);
      box-shadow:
        0 0 35px rgba(255, 235, 59, 0.9),
        0 0 70px rgba(255, 193, 7, 0.6),
        0 0 100px rgba(255, 235, 59, 0.3);
      animation: sunPulse 2.5s ease-in-out infinite;
    }

    .sunset-sun {
      background: radial-gradient(circle at 30% 30%, #ff9800 0%, #e91e63 50%, #9c27b0 100%);
      box-shadow:
        0 0 25px rgba(255, 152, 0, 0.8),
        0 0 50px rgba(233, 30, 99, 0.6),
        0 0 75px rgba(156, 39, 176, 0.4);
      animation: sunPulse 3.5s ease-in-out infinite;
    }

    .evening-moon {
      background:
        radial-gradient(circle at 55% 35%, rgba(0, 0, 0, 0.08) 6%, transparent 10%),
        radial-gradient(circle at 75% 55%, rgba(0, 0, 0, 0.06) 4%, transparent 8%),
        radial-gradient(circle at 30% 30%, #f0f8ff 0%, #d1e7ff 40%, #7eb6ff 100%);
      box-shadow:
        0 0 20px rgba(126, 182, 255, 0.5),
        inset -6px -6px 12px rgba(0, 0, 0, 0.15),
        inset 2px 2px 4px rgba(0, 0, 0, 0.1);
      animation: moonGlow 4s ease-in-out infinite;
    }

    @keyframes sunPulse {
      0%, 100% {
        transform: scale(1);
        filter: brightness(1) saturate(1);
        box-shadow: inherit;
      }
      50% {
        transform: scale(1.08);
        filter: brightness(1.15) saturate(1.1);
      }
    }

    @keyframes moonGlow {
      0%, 100% {
        opacity: 0.92;
        transform: scale(1);
        filter: brightness(1);
      }
      50% {
        opacity: 1;
        transform: scale(1.03);
        filter: brightness(1.08);
      }
    }

    @keyframes rotateSunRays {
      from { transform: rotate(0deg); }
      to { transform: rotate(360deg); }
    }



    /* Visual sequence indicator with 24 segments */
    .prayer-sequence {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 420px;
      height: 420px;
      border-radius: 50%;
      z-index: 0;
    }

    .sequence-path {
      position: absolute;
      top: 50%;
      left: 50%;
      width: 100%;
      height: 100%;
      border-radius: 50%;
      background: 
        repeating-conic-gradient(
          transparent 0deg 7.5deg,
          rgba(139, 69, 19, 0.03) 7.5deg 15deg
        );
      transform: translate(-50%, -50%);
    }

    /* Hadith Section (Right Side) */
    .hadith-section {
      padding: 35px;
      background: linear-gradient(135deg, var(--brown-primary) 0%, var(--brown-secondary) 100%);
      color: white;
      display: flex;
      flex-direction: column;
      justify-content: center;
      position: relative;
      overflow: hidden;
      border-bottom-left-radius: 24px;
    }

    .hadith-section::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: 
        radial-gradient(circle at 20% 20%, rgba(255, 215, 0, 0.15) 0%, transparent 30%),
        radial-gradient(circle at 80% 80%, rgba(255, 215, 0, 0.15) 0%, transparent 30%);
      pointer-events: none;
      z-index: 1;
    }

    .hadith-title {
      font-size: 32px;
      font-weight: 700;
      text-align: center;
      margin-bottom: 30px;
      color: var(--gold-secondary);
      position: relative;
      z-index: 2;
      text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    }

    .hadith-text {
      font-size: 26px;
      line-height: 1.9;
      text-align: center;
      margin-bottom: 30px;
      font-style: italic;
      position: relative;
      padding: 0 30px;
      z-index: 2;
      text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    }

    .fade-out {
      opacity: 0;
      transition: opacity 0.3s ease-out;
    }
    
    .hadith-section {
      opacity: 1;
      transition: opacity 0.3s ease-in;
    }

    .hadith-text::before,
    .hadith-text::after {
      content: '"';
      font-size: 50px;
      position: absolute;
      top: -20px;
      color: rgba(255, 215, 0, 0.3);
      font-family: serif;
      z-index: 1;
    }

    .hadith-text::before {
      right: 10px;
    }

    .hadith-text::after {
      left: 10px;
      transform: rotate(180deg);
    }

    .hadith-source {
      font-size: 20px;
      text-align: center;
      font-weight: 600;
      color: var(--gold-tertiary);
      position: relative;
      z-index: 2;
      font-style: italic;
    }

    /* Responsive Design */
    @media (max-width: 1100px) {
      .timeline-container {
        height: 400px;
      }
      
      .timeline-circle {
        width: 350px;
        height: 350px;
      }
      
      .timeline-path {
        width: 370px;
        height: 370px;
      }
      
      /* Progress container inherits timeline container dimensions */
      
      .prayer-point {
        width: var(--prayer-point-size);
        height: var(--prayer-point-size);
      }
      
      .prayer-name {
        font-size: 14px;
      }
      
      .prayer-time {
        font-size: 16px;
      }
      
      .progress-indicator {
        width: 32px;
        height: 32px;
      }
      
      .hadith-text {
        font-size: 24px;
      }
    }

    @media (max-width: 900px) {
      .content-section {
        grid-template-columns: 1fr;
      }
      
      .header-section {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 25px;
        padding: 25px;
      }
      
      .current-prayer-status {
        transform: none;
        text-align: center;
        max-width: 500px;
        margin: 0 auto;
      }
      
      .hadith-section {
        border-bottom-left-radius: 0;
      }
      
      .timeline-container {
        height: 350px;
      }
      
      .timeline-circle {
        width: 300px;
        height: 300px;
      }
      
      .timeline-path {
        width: 320px;
        height: 320px;
      }
      
      /* Progress container inherits timeline container dimensions */
      
      /* Adjust prayer positions for medium screens */
      .prayer-point.isha {
        top: 25%;
        right: 10%;
      }
      
      .prayer-point.sunrise {
        top: 75%;
        right: 10%;
        width: 45px;
        height: 45px;
      }
      
      .prayer-point.sunrise .prayer-name {
        font-size: 12px;
      }
      
      .prayer-point.sunrise .prayer-time {
        font-size: 14px;
      }
      
      .prayer-point.asr {
        top: 25%;
        left: 10%;
      }
      
      @keyframes moveProgress {
        0% { top: 100%; left: 50%; }
        100% { top: 25%; left: 10%; }
      }
    }

    @media (max-width: 600px) {
      .main-title {
        font-size: 36px;
        padding: 0 20px;
        margin: 30px;
      }
      
      .header-section {
        padding: 20px;
        gap: 20px;
      }
      
      .hadith-text {
        font-size: 22px;
      }
      
      .timeline-container {
        height: 300px;
      }
      
      .timeline-circle {
        width: 250px;
        height: 250px;
      }
      
      .timeline-path {
        width: 270px;
        height: 270px;
      }
      
      /* Progress container inherits timeline container dimensions */
      
      .prayer-point {
        width: var(--prayer-point-size);
        height: var(--prayer-point-size);
      }
      
      .prayer-name {
        font-size: 12px;
      }
      
      .prayer-time {
        font-size: 14px;
      }
      
      .progress-indicator {
        width: 28px;
        height: 28px;
      }
      
      .current-prayer-status {
        padding: 15px;
      }
      
      .current-prayer-name {
        font-size: 28px;
      }
      
      .current-prayer-time {
        font-size: 24px;
      }
      
      .dates-section {
        padding: 15px;
      }
      
      /* Adjust prayer positions for small screens */
      .prayer-point.isha {
        top: 25%;
        right: 5%;
      }
      
      .prayer-point.sunrise {
        top: 75%;
        right: 5%;
        width: 40px;
        height: 40px;
      }
      
      .prayer-point.sunrise .prayer-name {
        font-size: 10px;
      }
      
      .prayer-point.sunrise .prayer-time {
        font-size: 12px;
      }
      
      .prayer-point.asr {
        top: 25%;
        left: 5%;
      }
      
      @keyframes moveProgress {
        0% { top: 100%; left: 50%; }
        100% { top: 25%; left: 5%; }
      }
    }
    
    /* Extra small screens */
    @media (max-width: 480px) {
      .timeline-container {
        height: 280px;
      }
      
      .timeline-circle {
        width: 220px;
        height: 220px;
      }
      
      .timeline-path {
        width: 240px;
        height: 240px;
      }
      
      /* Progress container inherits timeline container dimensions */
      
      .prayer-point {
        width: var(--prayer-point-size);
        height: var(--prayer-point-size);
      }
      
      .prayer-name {
        font-size: 10px;
      }
      
      .prayer-time {
        font-size: 12px;
      }
    }
    
    /* Decorative elements */
    .islamic-decoration {
      position: absolute;
      z-index: 1;
      color: rgba(139, 69, 19, 0.1);
      font-size: 40px;
    }
    
    .decoration-1 {
      top: 15%;
      left: 5%;
      transform: rotate(45deg);
    }
    
    .decoration-2 {
      bottom: 20%;
      right: 5%;
      transform: rotate(-20deg);
    }
    
    .decoration-3 {
      top: 40%;
      right: 10%;
      font-size: 30px;
      transform: rotate(15deg);
    }
    
    .decoration-4 {
      bottom: 30%;
      left: 10%;
      font-size: 30px;
      transform: rotate(-15deg);
    }

    @media (prefers-reduced-motion: reduce) {
      * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
      }
    }

    @media (max-width: 900px) {
      .prayer-point {
        width: var(--prayer-point-size);
        height: var(--prayer-point-size);
      }
    }

    @media (max-width: 600px) {
      .prayer-point {
        width: var(--prayer-point-size);
        height: var(--prayer-point-size);
      }
    }

    .prayer-point.sunrise {
      background: linear-gradient(135deg, #fffbe6 60%, #ffeaa7 100%);
      border: 2.5px solid var(--gold-primary);
      box-shadow: 0 0 0 8px rgba(255, 215, 0, 0.13), 0 2px 12px 0 rgba(255, 215, 0, 0.10);
      filter: drop-shadow(0 0 12px rgba(255, 215, 0, 0.18));
      opacity: 0.98;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="main-card">
      <div class="islamic-pattern"></div>
      
      <!-- Islamic decorative elements -->
      <div class="islamic-decoration decoration-1">✧</div>
      <div class="islamic-decoration decoration-2">✧</div>
      <div class="islamic-decoration decoration-3">✧</div>
      <div class="islamic-decoration decoration-4">✧</div>
      
      <div class="header-section">
        <div class="current-prayer-status">
          <div class="current-prayer-label">
            <i class="fas fa-clock"></i> الصلاة الحالية
          </div>
          <div class="current-prayer-name">الظهر</div>
          <div class="current-prayer-time">١٢:٣٠</div>
          <div class="time-remaining">
            <i class="fas fa-hourglass-half"></i> ٣ ساعات و٤٥ دقيقة للعصر
          </div>
          <div class="location-name">
            <i class="fas fa-location-dot"></i> صيدا، لبنان
          </div>
        </div>
        
        <h1 class="main-title">مواقيت الصلاة</h1>
        
        <div class="dates-section">
          <div class="date-row">
            <div class="date-label">
              <i class="fas fa-calendar-days"></i> التاريخ الهجري
            </div>
            <div class="date-value">
              ١٢ محرم ١٤٤٧
              <span class="moon-phase">
                <i class="fas fa-moon"></i>
              </span>
            </div>
          </div>
          <div class="date-row">
            <div class="date-label">
              <i class="fas fa-calendar"></i> التاريخ الميلادي
            </div>
            <div class="date-value">١٨ يوليو ٢٠٢٥</div>
          </div>
          <div class="current-time">
            <i class="fas fa-calendar-check"></i> الجمعة، ١٢:٣٠ مساءً
          </div>
        </div>
      </div>
      
      <div class="content-section">
        <div class="prayer-timeline-section">
          <h2 class="section-title">دورة الصلوات اليومية</h2>
          <div class="timeline-container">
            <svg id="prayer-timeline-svg" width="100%" height="100%" viewBox="0 0 1000 1000" style="max-width: 500px; display: block; margin: auto;"></svg>
            <div class="progress-container">
              <div class="progress-indicator" id="progressIndicator">
                <div class="celestial-body day-sun" id="celestialBody"></div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="hadith-section" id="content-section">
          <h3 class="hadith-title" id="content-title">حديث شريف</h3>
          <div class="hadith-text" id="content-text"></div>
          <div class="hadith-source" id="content-source"></div>
        </div>
      </div>
    </div>
  </div>
  <script>
// --- Configuration ---
const LEBANON_TIMEZONE = 'Asia/Beirut';
const PRAYER_METHOD = 5;
const SYNODIC_MONTH = 29.530588;
const ISLAMIC_EPOCH_JULIAN = 1948439.5;
const LEAP_CYCLE = [2, 5, 7, 10, 13, 16, 18, 21, 24, 26, 29];

// --- Localization ---
const ARABIC_MONTHS_GREGORIAN = [
  'كانون الثاني', 'شباط', 'آذار', 'نيسان', 'أيار', 'حزيران',
  'تموز', 'آب', 'أيلول', 'تشرين الأول', 'تشرين الثاني', 'كانون الأول'
];
const PRAYER_NAMES = {
  Fajr: 'الفجر', Sunrise: 'الشروق', Dhuhr: 'الظهر',
  Asr: 'العصر', Maghrib: 'المغرب', Isha: 'العشاء'
};
const ARABIC_DIGITS = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
const ARABIC_WEEKDAYS = ['الأحد', 'الإثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
const HIJRI_MONTHS = [
  'محرم', 'صفر', 'ربيع الأول', 'ربيع الآخر', 'جمادى الأولى', 'جمادى الآخرة',
  'رجب', 'شعبان', 'رمضان', 'شوال', 'ذو القعدة', 'ذو الحجة'
];
const MOON_PHASES = ['🌑', '🌒', '🌓', '🌔', '🌕', '🌖', '🌗', '🌘'];
const apiEndpoints = [
  dateKey => {
    const [year, month, day] = dateKey.split('-');
    return `https://api.aladhan.com/v1/gToH?date=${day}-${month}-${year}`;
  },
  dateKey => {
    const [year, month] = dateKey.split('-');
    return `https://api.aladhan.com/v1/gToHCalendar/${month}/${year}`;
  }
];

// Content Manager for Verses and Hadiths
const contentManager = {
  elements: {},
  state: {
    timer: null,
    secondsRemaining: 180,
    currentType: null,
    preloadedContent: null,
    scrollTimer: null
  },
  maxTextLength: 400, // Maximum text length before truncation

  init() {
    this.elements = {
      title: document.getElementById('content-title'),
      text: document.getElementById('content-text'),
      source: document.getElementById('content-source'),
      section: document.getElementById('content-section')
    };
    this.loadInitialContent();
    this.displayRandomContent();
    this.startTimer();
  },

  loadInitialContent() {
    this.loadContent('verse');
    this.loadContent('hadith');
  },

  selectContentType() {
    const weights = { verse: 0.6, hadith: 0.4 };
    const random = Math.random();
    return random < weights.verse ? 'verse' : 'hadith';
  },

  displayRandomContent() {
    const contentType = this.selectContentType();
    this.showContent(contentType);
    this.preloadOppositeContent(contentType);
  },

  preloadOppositeContent(currentType) {
    const oppositeType = currentType === 'verse' ? 'hadith' : 'verse';
    setTimeout(() => this.loadContent(oppositeType), 1000);
  },

  showContent(type) {
    const config = {
      verse: { text: 'آية قرآنية' },
      hadith: { text: 'حديث شريف' }
    };
    
    this.elements.title.textContent = config[type].text;
    this.animateTransition(type);
    this.state.currentType = type;
  },

  animateTransition(type) {
    this.elements.section.classList.add('fade-out');
    setTimeout(() => {
      if (this.state.preloadedContent && this.state.preloadedContent.type === type) {
        this.updateContentElements(this.state.preloadedContent);
        this.state.preloadedContent = null;
      }
      this.elements.section.classList.remove('fade-out');
    }, 300);
  },

  async loadContent(type) {
    const useHardcoded = Math.random() < 0.6;
    
    try {
      const content = useHardcoded 
        ? this.getHardcodedContent(type)
        : await this.fetchContentFromAPI(type);

      if (type === this.state.currentType) {
        this.updateContentElements(content);
      } else {
        this.state.preloadedContent = { ...content, type };
      }
    } catch (error) {
      console.error(`Error loading ${type}:`, error);
      this.getHardcodedContent(type);
    }
  },

  getHardcodedContent(type) {
    const collection = CONTENT_DATA[`${type}s`];
    const randomIndex = Math.floor(Math.random() * collection.length);
    return collection[randomIndex];
  },

  async fetchContentFromAPI(type) {
    if (type === 'verse') {
      const surahNumber = Math.floor(Math.random() * 114) + 1;
      const response = await fetch(`https://api.alquran.cloud/v1/surah/${surahNumber}/ar.alafasy`);
      const data = await response.json();
      
      if (data.code !== 200) throw new Error('Failed to fetch surah');
      
      const surah = data.data;
      const ayahNumber = Math.floor(Math.random() * surah.numberOfAyahs) + 1;
      
      const ayahResponse = await fetch(`https://api.alquran.cloud/v1/ayah/${surah.number}:${ayahNumber}/ar.alafasy`);
      const ayahData = await ayahResponse.json();
      
      if (ayahData.code !== 200) throw new Error('Failed to fetch ayah');
      
      const verse = ayahData.data;
      return {
        text: verse.text,
        source: `${verse.surah.name} - الآية ${toArabicNumerals(verse.numberInSurah)}`
      };
    } else {
      const collections = Object.keys(COLLECTIONS);
      const collection = collections[Math.floor(Math.random() * collections.length)];
      
      const response = await fetch(`https://cdn.jsdelivr.net/gh/fawazahmed0/hadith-api@1/editions/ara-${collection}.json`);
      const data = await response.json();
      
      if (!data.hadiths?.length) throw new Error('No hadiths found');
      
      const hadith = data.hadiths[Math.floor(Math.random() * data.hadiths.length)];
      return {
        text: hadith.text,
        source: `${COLLECTIONS[collection]} - الحديث رقم ${toArabicNumerals(hadith.hadithnumber || 1)}`
      };
    }
  },

  updateContentElements(content) {
    // Reset any previous styling
    this.elements.text.style.fontSize = '';
    this.elements.text.classList.remove('truncated');
    
    // Handle long text if needed
    if (content.text.length > this.maxTextLength) {
      this.smartTruncate(this.elements.text, content.text);
    } else {
      this.elements.text.textContent = content.text;
    }
    
    this.elements.source.textContent = content.source;
  },
  
  smartTruncate(element, text) {
    if (text.length <= this.maxTextLength) {
      element.textContent = text;
      element.classList.remove('truncated');
      this.adjustFontSize(element);
      return;
    }

    // Set RTL for Arabic text
    if (/[\u0600-\u06FF]/.test(text)) {
      element.style.direction = 'rtl';
      element.style.textAlign = 'right';
    }

    // Try to cut at verse/sentence boundaries (including Arabic punctuation)
    const sentences = text.split(/([.!؟۔﴾﴿]|\d+:\d+)/);
    let truncated = '';
    
    for (let i = 0; i < sentences.length; i += 2) {
      const sentence = sentences[i] + (sentences[i + 1] || '');
      // Keep verse numbers together with their text
      if ((truncated + sentence).length > this.maxTextLength - 20) {
        break;
      }
      truncated += sentence;
    }
    
    // If no good sentence break found, cut at word boundary
    if (truncated.length < this.maxTextLength * 0.7) {
      const words = text.split(' ');
      truncated = '';
      
      for (const word of words) {
        if ((truncated + word + ' ...').length > this.maxTextLength) {
          break;
        }
        truncated += word + ' ';
      }
    }
    
    // Add ellipsis and ensure it's not empty
    truncated = truncated.trim();
    if (truncated.length > 0) {
      element.textContent = truncated + ' ...';
    } else {
      element.textContent = text.substring(0, this.maxTextLength - 10) + ' ...';
    }
    
    element.classList.add('truncated');
    element.title = text; // Full text on hover
    this.adjustFontSize(element);
  },

  adjustFontSize(element) {
    const container = element.parentElement;
    const maxHeight = container.clientHeight;
    const maxWidth = container.clientWidth;
    
    // Start with different default sizes for Arabic/non-Arabic
    const isArabic = /[\u0600-\u06FF]/.test(element.textContent);
    let fontSize = isArabic ? 20 : 16; // Arabic text typically needs larger font
    element.style.fontSize = fontSize + 'px';
    
    // Binary search for optimal font size, with different ranges for Arabic
    let min = isArabic ? 14 : 8;
    let max = isArabic ? 32 : 24;
    
    while (min <= max) {
      fontSize = Math.floor((min + max) / 2);
      element.style.fontSize = fontSize + 'px';
      
      if (element.scrollHeight <= maxHeight && element.scrollWidth <= maxWidth) {
        min = fontSize + 1;
      } else {
        max = fontSize - 1;
      }
    }
    
    // Use the largest size that fits
    element.style.fontSize = (max) + 'px';
  },

  startTimer() {
    this.clearTimer();
    this.state.secondsRemaining = 180;
    
    this.state.timer = setInterval(() => {
      this.state.secondsRemaining--;
      
      if (this.state.secondsRemaining === 30) {
        this.preloadNextContent();
      }
      
      if (this.state.secondsRemaining <= 0) {
        this.handleTimerExpiry();
      }
    }, 1000);
  },

  clearTimer() {
    if (this.state.timer) {
      clearInterval(this.state.timer);
      this.state.timer = null;
    }
  },

  preloadNextContent() {
    const nextType = this.selectContentType();
    this.loadContent(nextType);
  },

  handleTimerExpiry() {
    this.displayRandomContent();
    this.state.secondsRemaining = 180;
  }
};

const COLLECTIONS = {
  bukhari: 'صحيح البخاري',
  muslim: 'صحيح مسلم',
  tirmidhi: 'سنن الترمذي',
  abudawud: 'سنن أبي داود',
  nasai: 'سنن النسائي',
  ibnmajah: 'سنن ابن ماجه'
};

const CONTENT_DATA = {
  verses: [
    {
        text: 'اللَّهُ لَا إِلَٰهَ إِلَّا هُوَ الْحَيُّ الْقَيُّومُ ۚ لَا تَأْخُذُهُ سِنَةٌ وَلَا نَوْمٌ ۚ لَهُ مَا فِي السَّمَاوَاتِ وَمَا فِي الْأَرْضِ ۗ مَنْ ذَا الَّذِي يَشْفَعُ عِنْدَهُ إِلَّا بِإِذْنِهِ ۚ يَعْلَمُ مَا بَيْنَ أَيْدِيهِمْ وَمَا خَلْفَهُمْ ۖ وَلَا يُحِيطُونَ بِشَيْءٍ مِنْ عِلْمِهِ إِلَّا بِمَا شَاءَ ۚ وَسِعَ كُرْسِيُّهُ السَّمَاوَاتِ وَالْأَرْضَ ۖ وَلَا يَئُودُهُ حِفْظُهُمَا ۚ وَهُوَ الْعَلِيُّ الْعَظِيمُ',
        source: 'سورة البقرة - الآية ٢٥٥'
    },
    {
        text: 'اللَّهُ نُورُ السَّمَاوَاتِ وَالْأَرْضِ ۚ مَثَلُ نُورِهِ كَمِشْكَاةٍ فِيهَا مِصْبَاحٌ ۖ الْمِصْبَاحُ فِي زُجَاجَةٍ ۖ الزُّجَاجَةُ كَأَنَّهَا كَوْكَبٌ دُرِّيٌّ يُوقَدُ مِنْ شَجَرَةٍ مُبَارَكَةٍ زَيْتُونَةٍ لَا شَرْقِيَّةٍ وَلَا غَرْبِيَّةٍ يَكَادُ زَيْتُهَا يُضِيءُ وَلَوْ لَمْ تَمْسَسْهُ نَارٌ ۚ نُورٌ عَلَىٰ نُورٍ ۗ يَهْدِي اللَّهُ لِنُورِهِ مَنْ يَشَاءُ',
        source: 'سورة النور - الآية ٣٥'
    },
    {
        text: 'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ ﴿١﴾ الْحَمْدُ لِلَّهِ رَبِّ الْعَالَمِينَ ﴿٢﴾ الرَّحْمَٰنِ الرَّحِيمِ ﴿٣﴾ مَالِكِ يَوْمِ الدِّينِ ﴿٤﴾ إِيَّاكَ نَعْبُدُ وَإِيَّاكَ نَسْتَعِينُ ﴿٥﴾ اهْدِنَا الصِّرَاطَ الْمُسْتَقِيمَ ﴿٦﴾ صِرَاطَ الَّذِينَ أَنْعَمْتَ عَلَيْهِمْ غَيْرِ الْمَغْضُوبِ عَلَيْهِمْ وَلَا الضَّالِّينَ ﴿٧﴾',
        source: 'سورة الفاتحة - الآيات ١-٧'
    },
    {
        text: 'لَا إِكْرَاهَ فِي الدِّينِ ۖ قَدْ تَبَيَّنَ الرُّشْدُ مِنَ الْغَيِّ ۚ فَمَنْ يَكْفُرْ بِالطَّاغُوتِ وَيُؤْمِنْ بِاللَّهِ فَقَدِ اسْتَمْسَكَ بِالْعُرْوَةِ الْوُثْقَىٰ لَا انْفِصَامَ لَهَا ۗ وَاللَّهُ سَمِيعٌ عَلِيمٌ',
        source: 'سورة البقرة - الآية ٢٥٦'
    },
    {
        text: 'وَمَنْ يَتَوَكَّلْ عَلَى اللَّهِ فَهُوَ حَسْبُهُ ۚ إِنَّ اللَّهَ بَالِغُ أَمْرِهِ ۚ قَدْ جَعَلَ اللَّهُ لِكُلِّ شَيْءٍ قَدْرًا',
        source: 'سورة الطلاق - الآية ٣'
    },
    {
        text: 'فَإِنَّ مَعَ الْعُسْرِ يُسْرًا ﴿٥﴾ إِنَّ مَعَ الْعُسْرِ يُسْرًا ﴿٦﴾',
        source: 'سورة الشرح - الآيتان ٥-٦'
    },
    {
        text: 'قُلْ يَا عِبَادِيَ الَّذِينَ أَسْرَفُوا عَلَىٰ أَنْفُسِهِمْ لَا تَقْنَطُوا مِنْ رَحْمَةِ اللَّهِ ۚ إِنَّ اللَّهَ يَغْفِرُ الذُّنُوبَ جَمِيعًا ۚ إِنَّهُ هُوَ الْغَفُورُ الرَّحِيمُ',
        source: 'سورة الزمر - الآية ٥٣'
    },
    {
        text: 'وَلَنَبْلُوَنَّكُمْ بِشَيْءٍ مِنَ الْخَوْفِ وَالْجُوعِ وَنَقْصٍ مِنَ الْأَمْوَالِ وَالْأَنْفُسِ وَالثَّمَرَاتِ ۗ وَبَشِّرِ الصَّابِرِينَ ﴿١٥٥﴾ الَّذِينَ إِذَا أَصَابَتْهُمْ مُصِيبَةٌ قَالُوا إِنَّا لِلَّهِ وَإِنَّا إِلَيْهِ رَاجِعُونَ ﴿١٥٦﴾',
        source: 'سورة البقرة - الآيتان ١٥٥-١٥٦'
    },
    {
        text: 'يَرْفَعِ اللَّهُ الَّذِينَ آمَنُوا مِنْكُمْ وَالَّذِينَ أُوتُوا الْعِلْمَ دَرَجَاتٍ ۚ وَاللَّهُ بِمَا تَعْمَلُونَ خَبِيرٌ',
        source: 'سورة المجادلة - الآية ١١'
    },
    {
        text: 'وَقُلِ اعْمَلُوا فَسَيَرَى اللَّهُ عَمَلَكُمْ وَرَسُولُهُ وَالْمُؤْمِنُونَ ۖ وَسَتُرَدُّونَ إِلَىٰ عَالِمِ الْغَيْبِ وَالشَّهَادَةِ فَيُنَبِّئُكُمْ بِمَا كُنْتُمْ تَعْمَلُونَ',
        source: 'سورة التوبة - الآية ١٠٥'
    },
    {
        text: 'يَا أَيُّهَا النَّاسُ إِنَّا خَلَقْنَاكُمْ مِنْ ذَكَرٍ وَأُنْثَىٰ وَجَعَلْنَاكُمْ شُعُوبًا وَقَبَائِلَ لِتَعَارَفُوا ۚ إِنَّ أَكْرَمَكُمْ عِنْدَ اللَّهِ أَتْقَاكُمْ ۚ إِنَّ اللَّهَ عَلِيمٌ خَبِيرٌ',
        source: 'سورة الحجرات - الآية ١٣'
    },
    {
        text: 'يَا أَيُّهَا الَّذِينَ آمَنُوا كُونُوا قَوَّامِينَ بِالْقِسْطِ شُهَدَاءَ لِلَّهِ وَلَوْ عَلَىٰ أَنْفُسِكُمْ وَالْوَالِدَيْنِ وَالْأَقْرَبِينَ ۚ إِنْ يَكُنْ غَنِيًّا أَوْ فَقِيرًا فَاللَّهُ أَوْلَىٰ بِهِمَا ۖ فَلَا تَتَّبِعُوا الْهَوَىٰ أَنْ تَعْدِلُوا',
        source: 'سورة النساء - الآية ١٣٥'
    },
    {
        text: 'وَجَزَاءُ سَيِّئَةٍ سَيِّئَةٌ مِثْلُهَا ۖ فَمَنْ عَفَا وَأَصْلَحَ فَأَجْرُهُ عَلَى اللَّهِ ۚ إِنَّهُ لَا يُحِبُّ الظَّالِمِينَ',
        source: 'سورة الشورى - الآية ٤٠'
    },
    {
        text: 'الَّذِينَ آمَنُوا وَتَطْمَئِنُّ قُلُوبُهُمْ بِذِكْرِ اللَّهِ ۗ أَلَا بِذِكْرِ اللَّهِ تَطْمَئِنُّ الْقُلُوبُ',
        source: 'سورة الرعد - الآية ٢٨'
    },
    {
        text: 'وَإِذَا سَأَلَكَ عِبَادِي عَنِّي فَإِنِّي قَرِيبٌ ۖ أُجِيبُ دَعْوَةَ الدَّاعِ إِذَا دَعَانِ ۖ فَلْيَسْتَجِيبُوا لِي وَلْيُؤْمِنُوا بِي لَعَلَّهُمْ يَرْشُدُونَ',
        source: 'سورة البقرة - الآية ١٨٦'
    },
    {
        text: 'يَا أَيُّهَا الَّذِينَ آمَنُوا اسْتَعِينُوا بِالصَّبْرِ وَالصَّلَاةِ ۚ إِنَّ اللَّهَ مَعَ الصَّابِرِينَ',
        source: 'سورة البقرة - الآية ١٥٣'
    },
    {
        text: 'الْمَالُ وَالْبَنُونَ زِينَةُ الْحَيَاةِ الدُّنْيَا ۖ وَالْبَاقِيَاتُ الصَّالِحَاتُ خَيْرٌ عِنْدَ رَبِّكَ ثَوَابًا وَخَيْرٌ أَمَلًا',
        source: 'سورة الكهف - الآية ٤٦'
    },
    {
        text: 'وَإِذْ تَأَذَّنَ رَبُّكُمْ لَئِنْ شَكَرْتُمْ لَأَزِيدَنَّكُمْ ۖ وَلَئِنْ كَفَرْتُمْ إِنَّ عَذَابِي لَشَدِيدٌ',
        source: 'سورة إبراهيم - الآية ٧'
    },
    {
        text: 'مَثَلُ الَّذِينَ يُنْفِقُونَ أَمْوَالَهُمْ فِي سَبِيلِ اللَّهِ كَمَثَلِ حَبَّةٍ أَنْبَتَتْ سَبْعَ سَنَابِلَ فِي كُلِّ سُنْبُلَةٍ مِائَةُ حَبَّةٍ ۗ وَاللَّهُ يُضَاعِفُ لِمَنْ يَشَاءُ ۗ وَاللَّهُ وَاسِعٌ عَلِيمٌ',
        source: 'سورة البقرة - الآية ٢٦١'
    },
    {
        text: 'هُوَ اللَّهُ الَّذِي لَا إِلَٰهَ إِلَّا هُوَ الْمَلِكُ الْقُدُّوسُ السَّلَامُ الْمُؤْمِنُ الْمُهَيْمِنُ الْعَزِيزُ الْجَبَّارُ الْمُتَكَبِّرُ ۚ سُبْحَانَ اللَّهِ عَمَّا يُشْرِكُونَ',
        source: 'سورة الحشر - الآية ٢٣'
    },
    {
        text: 'وَلِلَّهِ الْمَشْرِقُ وَالْمَغْرِبُ ۚ فَأَيْنَمَا تُوَلُّوا فَثَمَّ وَجْهُ اللَّهِ ۚ إِنَّ اللَّهَ وَاسِعٌ عَلِيمٌ',
        source: 'سورة البقرة - الآية ١١٥'
    },
    {
        text: 'وَابْتَغِ فِيمَا آتَاكَ اللَّهُ الدَّارَ الْآخِرَةَ ۖ وَلَا تَنْسَ نَصِيبَكَ مِنَ الدُّنْيَا ۖ وَأَحْسِنْ كَمَا أَحْسَنَ اللَّهُ إِلَيْكَ ۖ وَلَا تَبْغِ الْفَسَادَ فِي الْأَرْضِ ۖ إِنَّ اللَّهَ لَا يُحِبُّ الْمُفْسِدِينَ',
        source: 'سورة القصص - الآية ٧٧'
    },
    {
        text: 'وَلَا تَمُدَّنَّ عَيْنَيْكَ إِلَىٰ مَا مَتَّعْنَا بِهِ أَزْوَاجًا مِنْهُمْ زَهْرَةَ الْحَيَاةِ الدُّنْيَا لِنَفْتِنَهُمْ فِيهِ ۚ وَرِزْقُ رَبِّكَ خَيْرٌ وَأَبْقَىٰ',
        source: 'سورة طه - الآية ١٣١'
    },
    {
        text: 'أَفَلَمْ يَسِيرُوا فِي الْأَرْضِ فَتَكُونَ لَهُمْ قُلُوبٌ يَعْقِلُونَ بِهَا أَوْ آذَانٌ يَسْمَعُونَ بِهَا ۖ فَإِنَّهَا لَا تَعْمَى الْأَبْصَارُ وَلَٰكِنْ تَعْمَى الْقُلُوبُ الَّتِي فِي الصُّدُورِ',
        source: 'سورة الحج - الآية ٤٦'
    },
    {
        text: 'إِنَّ اللَّهَ لَا يُغَيِّرُ مَا بِقَوْمٍ حَتَّىٰ يُغَيِّرُوا مَا بِأَنْفُسِهِمْ ۗ وَإِذَا أَرَادَ اللَّهُ بِقَوْمٍ سُوءًا فَلَا مَرَدَّ لَهُ ۚ وَمَا لَهُمْ مِنْ دُونِهِ مِنْ وَالٍ',
        source: 'سورة الرعد - الآية ١١'
    },
    {
        text: 'وَلَوْ أَنَّ أَهْلَ الْقُرَىٰ آمَنُوا وَاتَّقَوْا لَفَتَحْنَا عَلَيْهِمْ بَرَكَاتٍ مِنَ السَّمَاءِ وَالْأَرْضِ وَلَٰكِنْ كَذَّبُوا فَأَخَذْنَاهُمْ بِمَا كَانُوا يَكْسِبُونَ',
        source: 'سورة الأعراف - الآية ٩٦'
    },
    {
        text: 'إِنَّ رَبَّكَ لَبِالْمِرْصَادِ',
        source: 'سورة الفجر - الآية ١٤'
    },
    {
        text: 'وَمَا أُمِرُوا إِلَّا لِيَعْبُدُوا اللَّهَ مُخْلِصِينَ لَهُ الدِّينَ حُنَفَاءَ وَيُقِيمُوا الصَّلَاةَ وَيُؤْتُوا الزَّكَاةَ ۚ وَذَٰلِكَ دِينُ الْقَيِّمَةِ',
        source: 'سورة البينة - الآية ٥'
    },
    {
        text: 'يَا أَيَّتُهَا النَّفْسُ الْمُطْمَئِنَّةُ ﴿٢٧﴾ ارْجِعِي إِلَىٰ رَبِّكِ رَاضِيَةً مَرْضِيَّةً ﴿٢٨﴾ فَادْخُلِي فِي عِبَادِي ﴿٢٩﴾ وَادْخُلِي جَنَّتِي ﴿٣٠﴾',
        source: 'سورة الفجر - الآيات ٢٧-٣٠'
    },
    {
        text: 'إِنَّ هَٰذِهِ أُمَّتُكُمْ أُمَّةً وَاحِدَةً وَأَنَا رَبُّكُمْ فَاعْبُدُونِ',
        source: 'سورة الأنبياء - الآية ٩٢'
    }
  ],
  hadiths: [
    {
        text: 'قال رسول الله ﷺ: "إِنَّمَا الْأَعْمَالُ بِالنِّيَّاتِ، وَإِنَّمَا لِكُلِّ امْرِئٍ مَا نَوَى"',
        source: 'صحيح البخاري - الحديث ١'
    },
    {
        text: '"قال رسول الله ﷺ: "إِنَّ مِنْ أَحَبِّكُمْ إِلَىَّ وَأَقْرَبِكُمْ مِنِّي مَجْلِسًا يَوْمَ الْقِيَامَةِ أَحَاسِنَكُمْ أَخْلاَقًا وَإِنَّ أَبْغَضَكُمْ إِلَىَّ وَأَبْعَدَكُمْ مِنِّي مَجْلِسًا يَوْمَ الْقِيَامَةِ الثَّرْثَارُونَ وَالْمُتَشَدِّقُونَ وَالْمُتَفَيْهِقُونَ',
        source: 'جامع الترمذي - الحديث ٢٠١ ٨'
    },
    {
        text: 'جَاءَ جَاهِمَةَ السُّلَمِيِّ إِلَى النَّبِيِّ ﷺ فَقَالَ يَا رَسُولَ اللَّهِ أَرَدْتُ أَنْ أَغْزُوَ وَقَدْ جِئْتُ أَسْتَشِيرُكَ ‏.‏ فَقَالَ ‏"‏ هَلْ لَكَ مِنْ أُمٍّ ‏"‏ ‏.‏ قَالَ نَعَمْ ‏.‏ قَالَ ‏"‏ فَالْزَمْهَا فَإِنَّ الْجَنَّةَ تَحْتَ رِجْلَيْهَا ‏"‏ ‏.‏',
        source: 'سنن النسائي - الحديث ٣١٠٤'
    },
    {
        text: 'قَالَ النَّبِيُّ ﷺ ‏ "‏ تَجِدُ مِنْ شَرِّ النَّاسِ يَوْمَ الْقِيَامَةِ عِنْدَ اللَّهِ ذَا الْوَجْهَيْنِ، الَّذِي يَأْتِي هَؤُلاَءِ بِوَجْهٍ وَهَؤُلاَءِ بِوَجْهٍ ‏"‏‏.‏',
        source: 'صحيح البخاري - الحديث ٦٠٥ ٨'
    },
    {
        text: 'قال رسول الله ﷺ: "لَا يُؤْمِنُ أَحَدُكُمْ حَتَّى يُحِبَّ لِأَخِيهِ مَا يُحِبُّ لِنَفْسِهِ"',
        source: 'صحيح البخاري - الحديث ١٣'
    },
    {
        text: 'قَالَ النَّبِيَّ ﷺ ‏ "‏ الرَّاحِمُونَ يَرْحَمُهُمُ الرَّحْمَنُ ارْحَمُوا أَهْلَ الأَرْضِ يَرْحَمْكُمْ مَنْ فِي السَّمَاءِ ‏"‏ ‏.‏',
        source: 'سنن أبي داود - الحديث ٤٩٤١'
    },
    {
        text: 'قال رسول الله ﷺ: "طَلَبُ الْعِلْمِ فَرِيضَةٌ عَلَى كُلِّ مُسْلِمٍ"',
        source: 'سنن ابن ماجه - الحديث ٢٢٤'
    },
    {
        text: 'عَنِ النَّبِيِّ ﷺ قَالَ ‏ "‏ كَلِمَتَانِ خَفِيفَتَانِ عَلَى اللِّسَانِ، ثَقِيلَتَانِ فِي الْمِيزَانِ، حَبِيبَتَانِ إِلَى الرَّحْمَنِ، سُبْحَانَ اللَّهِ الْعَظِيمِ، سُبْحَانَ اللَّهِ وَبِحَمْدِهِ ‏"‏‏.‏',
        source: 'صحيح البخاري - الحديث ٦٤٠٦'
    },
    {
        text: 'قال رسول الله ﷺ: ‏"‏ بَيْنَما رَجُلٌ يَمْشِي فَاشْتَدَّ عَلَيْهِ الْعَطَشُ، فَنَزَلَ بِئْرًا فَشَرِبَ مِنْهَا، ثُمَّ خَرَجَ فَإِذَا هُوَ بِكَلْبٍ يَلْهَثُ، يَأْكُلُ الثَّرَى مِنَ الْعَطَشِ، فَقَالَ لَقَدْ بَلَغَ هَذَا مِثْلُ الَّذِي بَلَغَ بِي فَمَلأَ خُفَّهُ ثُمَّ أَمْسَكَهُ بِفِيهِ، ثُمَّ رَقِيَ، فَسَقَى الْكَلْبَ فَشَكَرَ اللَّهُ لَهُ، فَغَفَرَ لَهُ ‏"‏‏.‏ قَالُوا يَا رَسُولَ اللَّهِ، وَإِنَّ لَنَا فِي الْبَهَائِمِ أَجْرًا قَالَ ‏"‏ فِي كُلِّ كَبِدٍ رَطْبَةٍ أَجْرٌ ‏"‏‏.‏',
        source: 'صحيح البخاري - الحديث ٢٣٦٣'
    },
    {
        text: 'قال رسول الله ﷺ: "‏ الْمُؤْمِنُ مِرْآةُ الْمُؤْمِنِ وَالْمُؤْمِنُ أَخُو الْمُؤْمِنِ يَكُفُّ عَلَيْهِ ضَيْعَتَهُ وَيَحُوطُهُ مِنْ وَرَائِهِ ‏"‏ ‏.‏',
        source: 'سنن أبي داود - الحديث ٤٩١٨'
    },
    {
        text: 'قال رسول الله ﷺ: "‏ سَبْعَةٌ يُظِلُّهُمُ اللَّهُ تَعَالَى فِي ظِلِّهِ يَوْمَ لاَ ظِلَّ إِلاَّ ظِلُّهُ إِمَامٌ عَدْلٌ، وَشَابٌّ نَشَأَ فِي عِبَادَةِ اللَّهِ، وَرَجُلٌ قَلْبُهُ مُعَلَّقٌ فِي الْمَسَاجِدِ، وَرَجُلاَنِ تَحَابَّا فِي اللَّهِ اجْتَمَعَا عَلَيْهِ وَتَفَرَّقَا عَلَيْهِ، وَرَجُلٌ دَعَتْهُ امْرَأَةٌ ذَاتُ مَنْصِبٍ وَجَمَالٍ فَقَالَ إِنِّي أَخَافُ اللَّهَ، وَرَجُلٌ تَصَدَّقَ بِصَدَقَةٍ فَأَخْفَاهَا حَتَّى لاَ تَعْلَمَ شِمَالُهُ مَا تُنْفِقُ يَمِينُهُ، وَرَجُلٌ ذَكَرَ اللَّهَ خَالِيًا فَفَاضَتْ عَيْنَاهُ ‏"‏‏.‏',
        source: 'صحيح البخاري - الحديث ١٤٢٣'
    },
    {
        text: 'قال رسول الله ﷺ: ‏"‏ إِنَّ لِلَّهِ تِسْعَةً وَتِسْعِينَ اسْمًا مِائَةً إِلاَّ وَاحِدًا مَنْ أَحْصَاهَا دَخَلَ الْجَنَّةَ ‏"',
        source: 'صحيح مسلم - الحديث ٢٦٧٧'
    },
    {
        text: 'عَنْ أَبِي قِلاَبَةَ، قَالَ أَخْبَرَنِي أَبُو الْمَلِيحِ، قَالَ دَخَلْتُ مَعَ أَبِيكَ زَيْدٍ عَلَى عَبْدِ اللَّهِ بْنِ عَمْرٍو فَحَدَّثَنَا أَنَّ النَّبِيَّ ﷺ ذُكِرَ لَهُ صَوْمِي، فَدَخَلَ عَلَىَّ، فَأَلْقَيْتُ لَهُ وِسَادَةً مِنْ أَدَمٍ حَشْوُهَا لِيفٌ، فَجَلَسَ عَلَى الأَرْضِ، وَصَارَتِ الْوِسَادَةُ بَيْنِي وَبَيْنَهُ، فَقَالَ لِي ‏"‏ أَمَا يَكْفِيكَ مِنْ كُلِّ شَهْرٍ ثَلاَثَةُ أَيَّامٍ ‏"‏‏.‏ قُلْتُ يَا رَسُولَ اللَّهِ‏.‏ قَالَ ‏"‏ خَمْسًا ‏"‏‏.‏ قُلْتُ يَا رَسُولَ اللَّهِ‏.‏ قَالَ ‏"‏ سَبْعًا ‏"‏‏.‏ قُلْتُ يَا رَسُولَ اللَّهِ‏.‏ قَالَ ‏"‏ تِسْعًا ‏"‏‏.‏ قُلْتُ يَا رَسُولَ اللَّهِ‏.‏ قَالَ ‏"‏ إِحْدَى عَشْرَةَ ‏"‏‏.‏ قُلْتُ يَا رَسُولَ اللَّهِ‏.‏ قَالَ ‏"‏ لاَ صَوْمَ فَوْقَ صَوْمِ دَاوُدَ، شَطْرَ الدَّهْرِ، صِيَامُ يَوْمٍ، وَإِفْطَارُ يَوْمٍ ‏"‏‏.‏',
        source: 'صحيح البخاري - الحديث ٦٢٧٧'
    },
    {
        text: 'عَنِ النَّبِيِّ ﷺ قَالَ ‏ "‏ إِذَا أَحَبَّ اللَّهُ الْعَبْدَ نَادَى جِبْرِيلَ إِنَّ اللَّهَ يُحِبُّ فُلاَنًا فَأَحْبِبْهُ‏.‏ فَيُحِبُّهُ جِبْرِيلُ، فَيُنَادِي جِبْرِيلُ فِي أَهْلِ السَّمَاءِ إِنَّ اللَّهَ يُحِبُّ فُلاَنًا فَأَحِبُّوهُ‏.‏ فَيُحِبُّهُ أَهْلُ السَّمَاءِ، ثُمَّ يُوضَعُ لَهُ الْقَبُولُ فِي الأَرْضِ ‏"‏‏.‏',
        source: 'صحيح البخاري - الحديث ٣٢٠٩'
    },
    {
        text: 'قَالَ رَسُولُ اللَّهِ ﷺ ‏ "‏ وَالَّذِي نَفْسِي بِيَدِهِ لَوْ لَمْ تُذْنِبُوا لَذَهَبَ اللَّهُ بِكُمْ وَلَجَاءَ بِقَوْمٍ يُذْنِبُونَ فَيَسْتَغْفِرُونَ اللَّهَ فَيَغْفِرُ لَهُمْ ‏"‏ ‏.‏',
        source: 'صحيح مسلم - الحديث ٢٧٤٩'
    },
    {
        text: 'قال رسول الله ﷺ: "إِنَّ الرِّفْقَ لَا يَكُونُ فِي شَيْءٍ إِلَّا زَانَهُ، وَلَا يُنْزَعُ مِنْ شَيْءٍ إِلَّا شَانَهُ"',
        source: 'صحيح مسلم - الحديث ٢٥٩٤'
    },
    {
        text: 'قَالَ رَسُولُ اللَّهِ ﷺ ‏ "‏ مَنْ كَانَ يُؤْمِنُ بِاللَّهِ وَالْيَوْمِ الآخِرِ فَلاَ يُؤْذِ جَارَهُ، وَمَنْ كَانَ يُؤْمِنُ بِاللَّهِ وَالْيَوْمِ الآخِرِ فَلْيُكْرِمْ ضَيْفَهُ، وَمَنْ كَانَ يُؤْمِنُ بِاللَّهِ وَالْيَوْمِ الآخِرِ فَلْيَقُلْ خَيْرًا أَوْ لِيَصْمُتْ ‏"‏‏.‏',
        source: 'صحيح البخاري - الحديث ٦٠١٨'
    },
    {
        text: 'عَنِ النَّبِيِّ ﷺ قَالَ ‏"‏ إِنَّ الْمُقْسِطِينَ عِنْدَ اللَّهِ تَعَالَى عَلَى مَنَابِرَ مِنْ نُورٍ عَلَى يَمِينِ الرَّحْمَنِ الَّذِينَ يَعْدِلُونَ فِي حُكْمِهِمْ وَأَهْلِيهِمْ وَمَا وَلُوا ‏"‏‏.‏ قَالَ مُحَمَّدٌ فِي حَدِيثِهِ ‏"‏ وَكِلْتَا يَدَيْهِ يَمِينٌ ‏"‏‏.‏',
        source: 'سنن النسائي - الحديث ٥٣٧٩'
    },
    {
        text: 'عَنْ عَبْدِ اللَّهِ بْنِ مَسْعُودٍ، عَنِ النَّبِيِّ ﷺ قَالَ ‏"‏ لاَ يَدْخُلُ الْجَنَّةَ مَنْ كَانَ فِي قَلْبِهِ مِثْقَالُ ذَرَّةٍ مِنْ كِبْرٍ ‏"‏ ‏.‏ قَالَ رَجُلٌ إِنَّ الرَّجُلَ يُحِبُّ أَنْ يَكُونَ ثَوْبُهُ حَسَنًا وَنَعْلُهُ حَسَنَةً ‏.‏ قَالَ ‏"‏ إِنَّ اللَّهَ جَمِيلٌ يُحِبُّ الْجَمَالَ الْكِبْرُ بَطَرُ الْحَقِّ وَغَمْطُ النَّاسِ ‏"‏ ‏.‏',
        source: 'صحيح مسلم - الحديث ٩١'
    },
    {
        text: 'قَالَ رَسُولُ اللَّهِ ﷺ ‏ "‏ اللَّهُمَّ حَبِّبْ إِلَيْنَا الْمَدِينَةَ كَحُبِّنَا مَكَّةَ أَوْ أَشَدَّ، اللَّهُمَّ بَارِكْ لَنَا فِي صَاعِنَا، وَفِي مُدِّنَا، وَصَحِّحْهَا لَنَا وَانْقُلْ حُمَّاهَا إِلَى الْجُحْفَةِ ‏"‏‏.‏',
        source: 'صحيح البخاري - الحديث ١٨٨٩'
    },
    
    {
        text: 'قال رسول الله ﷺ: "‏ الْحَلاَلُ بَيِّنٌ وَالْحَرَامُ بَيِّنٌ، وَبَيْنَهُمَا مُشَبَّهَاتٌ لاَ يَعْلَمُهَا كَثِيرٌ مِنَ النَّاسِ، فَمَنِ اتَّقَى الْمُشَبَّهَاتِ اسْتَبْرَأَ لِدِيِنِهِ وَعِرْضِهِ، وَمَنْ وَقَعَ فِي الشُّبُهَاتِ كَرَاعٍ يَرْعَى حَوْلَ الْحِمَى، يُوشِكُ أَنْ يُوَاقِعَهُ‏.‏ أَلاَ وَإِنَّ لِكُلِّ مَلِكٍ حِمًى، أَلاَ إِنَّ حِمَى اللَّهِ فِي أَرْضِهِ مَحَارِمُهُ، أَلاَ وَإِنَّ فِي الْجَسَدِ مُضْغَةً إِذَا صَلَحَتْ صَلَحَ الْجَسَدُ كُلُّهُ، وَإِذَا فَسَدَتْ فَسَدَ الْجَسَدُ كُلُّهُ‏.‏ أَلاَ وَهِيَ الْقَلْبُ ‏"‏‏.‏',
        source: 'صحيح البخاري - الحديث ٥٢'
    },
    {
        text: 'عَنْ أَبِي هُرَيْرَةَ، قَالَ قَدِمَ الطُّفَيْلُ وَأَصْحَابُهُ فَقَالُوا يَا رَسُولَ اللَّهِ إِنَّ دَوْسًا قَدْ كَفَرَتْ وَأَبَتْ فَادْعُ اللَّهَ عَلَيْهَا ‏.‏ فَقِيلَ هَلَكَتْ دَوْسٌ فَقَالَ ‏ "‏ اللَّهُمَّ اهْدِ دَوْسًا وَائْتِ بِهِمْ ‏"‏ ‏.‏',
        source: 'صحيح مسلم - الحديث ٢٥٢٤'
    },
    {
        text: 'قَالَ رَسُولُ اللَّهِ ﷺ ‏ "‏ مَثَلُ أُمَّتِي مَثَلُ الْمَطَرِ لاَ يُدْرَى أَوَّلُهُ خَيْرٌ أَمْ آخِرُهُ ‏"‏ ‏.‏',
        source: 'جامع الترمذي- الحديث ٢٨٦٩'
    },
    {
        text: 'قَالَ النَّبِيُّ ﷺ ‏ "‏ يَقُولُ اللَّهُ تَعَالَى أَنَا عِنْدَ ظَنِّ عَبْدِي بِي، وَأَنَا مَعَهُ إِذَا ذَكَرَنِي، فَإِنْ ذَكَرَنِي فِي نَفْسِهِ ذَكَرْتُهُ فِي نَفْسِي، وَإِنْ ذَكَرَنِي فِي مَلأٍ ذَكَرْتُهُ فِي مَلأٍ خَيْرٍ مِنْهُمْ، وَإِنْ تَقَرَّبَ إِلَىَّ بِشِبْرٍ تَقَرَّبْتُ إِلَيْهِ ذِرَاعًا، وَإِنْ تَقَرَّبَ إِلَىَّ ذِرَاعًا تَقَرَّبْتُ إِلَيْهِ بَاعًا، وَإِنْ أَتَانِي يَمْشِي أَتَيْتُهُ هَرْوَلَةً ‏"‏‏.‏',
        source: 'صحيح البخاري - الحديث ٧٤٠٥'
    },
    {
        text: 'قَالَ رَسُولُ اللَّهِ ﷺ ‏ "‏ الْكَلِمَةُ الْحِكْمَةُ ضَالَّةُ الْمُؤْمِنِ فَحَيْثُ وَجَدَهَا فَهُوَ أَحَقُّ بِهَا ‏"‏ ‏.‏',
        source: 'جامع الترمذي - الحديث ٢٦٨٧'
    },
    {
        text: 'عَنْ أَبِي سَعِيدٍ الْخُدْرِيِّ، قَالَ سَمِعْتُ رَسُولَ اللَّهِ ﷺ يَقُولُ ‏ "‏ خَيْرُ الْمَجَالِسِ أَوْسَعُهَا ‏"‏ ‏.‏',
        source: 'سنن أبي داود - الحديث ٤٨٢٠'
    },
    {
        text: 'قال رسول الله ﷺ: "لَيْسَ الْغِنَى عَنْ كَثْرَةِ الْعَرَضِ، وَلَكِنَّ الْغِنَى غِنَى النَّفْسِ"',
        source: 'صحيح البخاري - الحديث ٦٤٤٦'
    },
    {
        text: 'قَالَ رَسُولُ اللَّهِ ﷺ ‏ "‏ مَنْ لاَ يَشْكُرِ النَّاسَ لاَ يَشْكُرِ اللَّهَ ‏"‏ ‏.‏',
        source: 'جامع الترمذي - الحديث ١٩٥٤'
    },
    {
        text: 'قال رسول الله ﷺ: "مَنْ صَمَتَ نَجَا"',
        source: 'جامع الترمذي - الحديث ٢٥٠١'
    },
    {
        text: 'قال رسول الله ﷺ: "كُنْ فِي الدُّنْيَا كَأَنَّكَ غَرِيبٌ أَوْ عَابِرُ سَبِيلٍ"',
        source: 'صحيح البخاري - الحديث ٦٤١٦'
    }
  ]
};

function toArabicNumerals(number) {
  return String(number).replace(/\d/g, d => ARABIC_DIGITS[parseInt(d, 10)]);
}

function formatArabicTime(timeStr) {
  if (!timeStr || typeof timeStr !== 'string') return '--:--';
  const [h, m] = timeStr.split(':').map(Number);
  if (isNaN(h) || isNaN(m)) return '--:--';
  const period = h >= 12 ? 'م' : 'ص';
  const displayHour = h === 0 ? 12 : (h > 12 ? h - 12 : h);
  return `${toArabicNumerals(displayHour)}:${toArabicNumerals(String(m).padStart(2, '0'))} ${period}`;
}

function timeToMinutes(timeStr) {
  if (!timeStr || typeof timeStr !== 'string') return null;
  const [h, m] = timeStr.split(':').map(Number);
  if (isNaN(h) || isNaN(m)) return null;
  return h * 60 + m;
}

function minutesToTime(minutes) {
  if (typeof minutes !== 'number' || minutes < 0 || minutes >= 1440) return null;
  const h = Math.floor(minutes / 60);
  const m = minutes % 60;
  return `${String(h).padStart(2, '0')}:${String(m).padStart(2, '0')}`;
}

function getBeirutTime(date = new Date()) {
  const parts = new Intl.DateTimeFormat('en-CA', {
    timeZone: LEBANON_TIMEZONE,
    year: 'numeric', month: '2-digit', day: '2-digit',
    hour: '2-digit', minute: '2-digit', second: '2-digit', hour12: false
  }).formatToParts(date).reduce((acc, part) => { acc[part.type] = part.value; return acc; }, {});
  return new Date(
    parseInt(parts.year),
    parseInt(parts.month) - 1,
    parseInt(parts.day),
    parseInt(parts.hour),
    parseInt(parts.minute),
    parseInt(parts.second)
  );
}

function getCurrentBeirutMinutes() {
  const now = getBeirutTime();
  return now.getHours() * 60 + now.getMinutes();
}

function formatGregorianDateDisplay() {
  const now = getBeirutTime();
  const weekday = ARABIC_WEEKDAYS[now.getDay()];
  const day = toArabicNumerals(now.getDate());
  const month = ARABIC_MONTHS_GREGORIAN[now.getMonth()];
  const year = toArabicNumerals(now.getFullYear());
  return `${weekday}، ${day} ${month} ${year} م`;
}

function getMoonPhase() {
  const now = new Date();
  const knownNewMoon = new Date('2000-01-06T18:14:00.000Z');
  const daysSinceNewMoon = (now - knownNewMoon) / (1000 * 60 * 60 * 24);
  const cyclePosition = (daysSinceNewMoon / SYNODIC_MONTH) % 1;
  const normalized = cyclePosition < 0 ? cyclePosition + 1 : cyclePosition;
  return MOON_PHASES[Math.floor(normalized * 8) % 8];
}

// --- API and calendar ---
async function fetchHijriDateFromAPI(gregorianDate) {
  const dateKey = `${gregorianDate.getFullYear()}-${String(gregorianDate.getMonth() + 1).padStart(2, '0')}-${String(gregorianDate.getDate()).padStart(2, '0')}`;
  const url = apiEndpoints[0](dateKey);
  const response = await fetch(url);
  if (!response.ok) throw new Error('Hijri API error');
  const data = await response.json();
  if (data && data.data && data.data.hijri) {
    const hijri = data.data.hijri;
    return {
      day: parseInt(hijri.day, 10),
      month: parseInt(hijri.month.number, 10),
      year: parseInt(hijri.year, 10)
    };
  }
  throw new Error('Hijri API format error');
}

function gregorianToJulian(date) {
  const y = date.getFullYear();
  const m = date.getMonth() + 1;
  const d = date.getDate();
  let aY = y, aM = m;
  if (m <= 2) { aY--; aM += 12; }
  const a = Math.floor(aY / 100);
  let b = 0;
  if (y > 1582 || (y === 1582 && m > 10) || (y === 1582 && m === 10 && d >= 15)) {
    b = 2 - a + Math.floor(a / 4);
  }
  return Math.floor(365.25 * (aY + 4716)) + Math.floor(30.6001 * (aM + 1)) + d + b - 1524.5;
}

function julianToHijri(jd) {
  const daysSinceEpoch = jd - ISLAMIC_EPOCH_JULIAN;
  const islamicYear = Math.floor((30 * daysSinceEpoch + 10646) / 10631);
  const yearStartDays = Math.floor((islamicYear * 10631 - 10646) / 30);
  const dayOfYear = Math.floor(daysSinceEpoch - yearStartDays) + 1;
  const yearInCycle = ((islamicYear - 1) % 30) + 1;
  const isLeap = LEAP_CYCLE.includes(yearInCycle);
  const monthLengths = [30, 29, 30, 29, 30, 29, 30, 29, 30, 29, 30, isLeap ? 30 : 29];
  let month = 1, remaining = dayOfYear;
  for (let i = 0; i < 12; i++) {
    if (remaining <= monthLengths[i]) { month = i + 1; break; }
    remaining -= monthLengths[i];
  }
  return { day: Math.max(1, Math.min(remaining || 1, monthLengths[month - 1])), month, year: Math.max(1, islamicYear) };
}

function calculateHijriDateTabular(gregorianDate, maghribTime) {
  let adjusted = new Date(gregorianDate);
  if (maghribTime) {
    const maghribMinutes = timeToMinutes(maghribTime);
    const currentMinutes = adjusted.getHours() * 60 + adjusted.getMinutes();
    if (maghribMinutes !== null && currentMinutes >= maghribMinutes) adjusted.setDate(adjusted.getDate() + 1);
  }
  return julianToHijri(gregorianToJulian(adjusted));
}

async function getHijriDate(gregorianDate, maghribTime) {
  try {
    return await fetchHijriDateFromAPI(gregorianDate);
  } catch {
    return calculateHijriDateTabular(gregorianDate, maghribTime);
  }
}

// --- Prayer time logic ---
function validatePrayerTimes(times) {
  const required = ['Fajr', 'Sunrise', 'Dhuhr', 'Asr', 'Maghrib', 'Isha'];
  for (const p of required) if (!times[p] || typeof times[p] !== 'string' || !times[p].includes(':')) return false;
  const t = {};
  for (const p of required) {
    const m = timeToMinutes(times[p]);
    if (m === null || m < 0 || m >= 1440) return false;
    t[p] = m;
  }
  const { Fajr, Sunrise, Dhuhr, Asr, Maghrib, Isha } = t;
  return (
    (Fajr < Sunrise && Sunrise < Dhuhr && Dhuhr < Asr && Asr < Maghrib && ((Isha > Maghrib && Isha < 1440) || (Isha < Fajr))) ||
    (Fajr < Sunrise && Sunrise < Dhuhr && Dhuhr < Asr && Asr < Maghrib && Maghrib < Isha)
  );
}

function convertPrayerTimesToMinutes(prayerTimes) {
  const prayers = ['Fajr', 'Dhuhr', 'Asr', 'Maghrib', 'Isha'];
  return prayers.reduce((acc, p) => {
    const m = timeToMinutes(prayerTimes[p]);
    if (m !== null) acc[p] = m;
    return acc;
  }, {});
}

function findNextPrayer(currentMinutes, prayerMinutes) {
  const prayers = ['Fajr', 'Dhuhr', 'Asr', 'Maghrib', 'Isha'];
  let next = 'Fajr', minDiff = Infinity;
  prayers.forEach(p => {
    if (prayerMinutes[p] === undefined) return;
    let diff = prayerMinutes[p] - currentMinutes;
    if (diff <= 0) diff += 1440;
    if (diff < minDiff) { minDiff = diff; next = p; }
  });
  return next;
}

function calculateTimeRemaining(currentMinutes, prayerMinutes) {
  let diff = prayerMinutes - currentMinutes;
  if (diff <= 0) diff += 1440;
  const h = Math.floor(diff / 60);
  const m = diff % 60;
  let text = '';
  if (h > 0) text += `${toArabicNumerals(h)} ساعة `;
  if (m > 0 || h === 0) text += `${toArabicNumerals(m)} دقيقة `;
  return text.trim();
}

// --- DOM and side effects ---
function updateText(selector, content) {
  const el = document.querySelector(selector);
  if (el && content !== undefined && content !== null) el.textContent = String(content);
}

function updateHTML(selector, content) {
  const el = document.querySelector(selector);
  if (el && content !== undefined && content !== null) el.innerHTML = String(content);
}

function renderGregorianDate() {
  updateText('.date-row:nth-child(2) .date-value', formatGregorianDateDisplay());
}

function renderHijriDate(hijri, moon) {
  if (!hijri) return;
  const monthName = HIJRI_MONTHS[hijri.month - 1] || '';
  const text = `${toArabicNumerals(hijri.day)} ${monthName} ${toArabicNumerals(hijri.year)} ${moon}`;
  updateHTML('.date-row:first-child .date-value', text);
}

function renderCurrentTime() {
  const now = getBeirutTime();
  const h = now.getHours();
  const m = String(now.getMinutes()).padStart(2, '0');
  const s = String(now.getSeconds()).padStart(2, '0');
  const period = h >= 12 ? 'مساءً' : 'صباحاً';
  const displayHour = h % 12 || 12;
  const text = `<i class="fas fa-calendar-check"></i> ${ARABIC_WEEKDAYS[now.getDay()]}، ${toArabicNumerals(displayHour)}:${toArabicNumerals(m)}:${toArabicNumerals(s)} ${period}`;
  updateHTML('.current-time', text);
}

// Celestial Body Progress Indicator System
class CelestialProgressIndicator {
  constructor() {
    this.progressIndicator = null;
    this.celestialBody = null;
    this.prayerTimes = {};
    this.prayerAngles = {};
  }

  getContainerDimensions() {
    const svg = document.getElementById('prayer-timeline-svg');
    const progressContainer = document.querySelector('.progress-container');

    if (!svg || !progressContainer) {
      console.warn('❌ Missing SVG or container elements');
      return { centerX: 500, centerY: 460, radius: 238, containerWidth: 400, containerHeight: 400 };
    }

    // Force a reflow to ensure accurate measurements
    svg.offsetHeight;
    progressContainer.offsetHeight;

    const svgRect = svg.getBoundingClientRect();
    const containerRect = progressContainer.getBoundingClientRect();

    // DEBUG: Log all measurements (only in development or when debugging)
    const isDebugMode = window.location.hostname === 'localhost' ||
                       window.location.hostname === '127.0.0.1' ||
                       window.location.search.includes('debug=true');

    if (isDebugMode) {
      console.log('📐 Dimension Analysis:', {
        svgRect: {
          width: svgRect.width.toFixed(1),
          height: svgRect.height.toFixed(1),
          left: svgRect.left.toFixed(1),
          top: svgRect.top.toFixed(1)
        },
        containerRect: {
          width: containerRect.width.toFixed(1),
          height: containerRect.height.toFixed(1),
          left: containerRect.left.toFixed(1),
          top: containerRect.top.toFixed(1)
        },
        screenSize: { width: window.innerWidth, height: window.innerHeight },
        devicePixelRatio: window.devicePixelRatio || 1,
        userAgent: navigator.userAgent.substring(0, 50) + '...'
      });
    }

    // Use the EXACT same coordinates as renderPrayerTimelineSVG function
    const svgCenterX = 500;  // size / 2 where size = 1000
    const svgCenterY = 460;  // size * 0.46 where size = 1000
    const prayerPointRadius = 340; // Exact radius from renderPrayerTimelineSVG
    // Progress indicator should be inside the timeline circle, not at prayer points
    const progressRadius = prayerPointRadius * 0.85; // 85% to stay well within the circle

    // Calculate the actual scale factor based on SVG viewBox vs rendered size
    const viewBoxWidth = 1000;
    const viewBoxHeight = 1000;
    const scaleX = svgRect.width / viewBoxWidth;
    const scaleY = svgRect.height / viewBoxHeight;

    const dimensions = {
      centerX: svgCenterX,
      centerY: svgCenterY,
      radius: progressRadius,
      containerWidth: svgRect.width,
      containerHeight: svgRect.height,
      svgWidth: svgRect.width,
      svgHeight: svgRect.height,
      scaleX: scaleX,
      scaleY: scaleY,
      // Calculate offset from progress container to SVG
      svgOffsetX: 0, // SVG is centered in progress container
      svgOffsetY: 0  // SVG is centered in progress container
    };

    return dimensions;
  }

  init(prayerTimes) {
    this.progressIndicator = document.getElementById('progressIndicator');
    this.celestialBody = document.getElementById('celestialBody');

    if (!this.progressIndicator || !this.celestialBody) {
      console.warn('Celestial progress indicator elements not found');
      return;
    }

    this.updatePrayerTimes(prayerTimes);

    // Set initial celestial body state
    this.celestialBody.className = 'celestial-body day-sun';

    // Ensure proper initialization after DOM is fully loaded
    const initializePosition = () => {
      // Force a layout recalculation
      document.body.offsetHeight;

      // Start real-time updates
      this.startRealTimeUpdates();

      // Force an immediate update to set initial position
      setTimeout(() => {
        this.forceUpdate();
      }, 100);
    };

    // Initialize immediately if DOM is ready, otherwise wait
    if (document.readyState === 'complete') {
      initializePosition();
    } else {
      window.addEventListener('load', initializePosition);
    }
  }

  updatePrayerTimes(prayerTimes) {
    try {
      this.prayerTimes = {
        Fajr: prayerTimes.Fajr,
        Sunrise: prayerTimes.Sunrise,
        Dhuhr: prayerTimes.Dhuhr,
        Asr: prayerTimes.Asr,
        Maghrib: prayerTimes.Maghrib,
        Isha: prayerTimes.Isha
      };

      // Use the existing getPrayerAngles function for consistency
      const angles = getPrayerAngles(this.prayerTimes);
      this.prayerAngles = {
        fajr: angles[0],
        sunrise: angles[1],
        dhuhr: angles[2],
        asr: angles[3],
        maghrib: angles[4],
        isha: angles[5]
      };

    } catch (error) {
      console.error('Error parsing prayer times:', error);
    }
  }









  getCurrentPrayerSegment(hour, minute) {
    const currentMinutes = hour * 60 + minute;
    const prayers = ['fajr', 'sunrise', 'dhuhr', 'asr', 'maghrib', 'isha'];

    // Convert prayer times to minutes using the global timeToMinutes function
    const prayerMinutes = {};
    prayers.forEach(prayer => {
      const prayerKey = prayer.charAt(0).toUpperCase() + prayer.slice(1);
      const timeStr = this.prayerTimes[prayerKey];
      prayerMinutes[prayer] = timeToMinutes(timeStr); // Use global function
    });

    // Find which segment the current time falls into
    for (let i = 0; i < prayers.length; i++) {
      const currentPrayer = prayers[i];
      const nextPrayer = prayers[(i + 1) % prayers.length];

      const currentPrayerMinutes = prayerMinutes[currentPrayer];
      let nextPrayerMinutes = prayerMinutes[nextPrayer];

      if (currentPrayerMinutes === null || nextPrayerMinutes === null) continue;

      // Handle day boundary crossing
      if (nextPrayerMinutes <= currentPrayerMinutes) {
        nextPrayerMinutes += 1440; // Add 24 hours
      }

      let adjustedCurrent = currentMinutes;
      // If current time is before the prayer time, it might be the next day
      if (currentMinutes < currentPrayerMinutes && (currentPrayerMinutes - currentMinutes) > 720) {
        adjustedCurrent += 1440;
      }

      // Check if current time is within this prayer segment
      if (adjustedCurrent >= currentPrayerMinutes && adjustedCurrent < nextPrayerMinutes) {
        const elapsed = adjustedCurrent - currentPrayerMinutes;
        const duration = nextPrayerMinutes - currentPrayerMinutes;
        const progress = duration > 0 ? elapsed / duration : 0;

        return {
          current: currentPrayer,
          next: nextPrayer,
          progress: Math.max(0, Math.min(progress, 1)),
          elapsed: elapsed,
          duration: duration
        };
      }
    }

    // Fallback - should not happen if prayer times are valid
    return { current: 'fajr', next: 'sunrise', progress: 0, elapsed: 0, duration: 0 };
  }

  calculatePositionOnCircle(angle, dimensions) {
    // Convert angle to radians - use the EXACT same calculation as the SVG
    const angleRad = angle * Math.PI / 180;

    // Calculate position in SVG coordinate system (EXACTLY like renderPrayerTimelineSVG)
    // Note: SVG uses -sin for Y coordinate (Y increases downward in SVG)
    const svgX = dimensions.centerX + dimensions.radius * Math.cos(angleRad);
    const svgY = dimensions.centerY - dimensions.radius * Math.sin(angleRad);

    // Convert SVG coordinates to screen coordinates using the calculated scale
    const screenX = svgX * dimensions.scaleX;
    const screenY = svgY * dimensions.scaleY;

    return {
      x: screenX,
      y: screenY
    };
  }

  getCelestialBodyType(currentPrayer, nextPrayer, progress) {
    const now = getBeirutTime();
    const currentMinutes = now.getHours() * 60 + now.getMinutes();

    // More precise celestial body determination based on prayer segments and progress
    switch (currentPrayer) {
      case 'isha':
        if (progress < 0.3) return 'evening-moon';
        return 'night-moon';

      case 'fajr':
        if (progress < 0.5) return 'predawn-moon';
        return 'sunrise-sun';

      case 'sunrise':
        return 'sunrise-sun';

      case 'dhuhr':
      case 'asr':
        if (currentPrayer === 'asr' && progress > 0.8) {
          return 'sunset-sun';
        }
        return 'day-sun';

      case 'maghrib':
        if (progress < 0.3) return 'sunset-sun';
        return 'evening-moon';

      default:
        // Fallback: determine by time of day
        const fajrTime = this.timeToMinutes(this.prayerTimes.Fajr);
        const maghribTime = this.timeToMinutes(this.prayerTimes.Maghrib);

        let isDay;
        if (fajrTime < maghribTime) {
          isDay = currentMinutes >= fajrTime && currentMinutes < maghribTime;
        } else {
          isDay = currentMinutes >= fajrTime || currentMinutes < maghribTime;
        }

        return isDay ? 'day-sun' : 'night-moon';
    }
  }

  updateProgressIndicator(segment) {
    if (!this.progressIndicator || !this.celestialBody) return;

    const dimensions = this.getContainerDimensions();
    const startAngle = this.prayerAngles[segment.current];
    const endAngle = this.prayerAngles[segment.next];

    if (startAngle === undefined || endAngle === undefined) {
      console.warn('❌ Missing prayer angles:', { startAngle, endAngle });
      return;
    }

    // Calculate angle difference, handling wraparound correctly
    let angleDiff = endAngle - startAngle;

    // Handle the special case where we cross 0° (e.g., from 330° to 30°)
    if (Math.abs(angleDiff) > 180) {
      if (angleDiff > 0) {
        angleDiff -= 360;
      } else {
        angleDiff += 360;
      }
    }

    // Calculate current angle based on progress
    const currentAngle = startAngle + angleDiff * segment.progress;
    const normalizedAngle = (currentAngle + 360) % 360;
    const position = this.calculatePositionOnCircle(normalizedAngle, dimensions);



    // Validate position values
    if (isNaN(position.x) || isNaN(position.y)) {
      console.warn('❌ Invalid position calculated:', position);
      return;
    }

    // Get indicator size for centering
    const indicatorSize = this.progressIndicator.offsetWidth || 40;
    const offset = indicatorSize / 2;

    // Apply position with bounds checking
    const finalX = Math.max(0, Math.min(position.x - offset, dimensions.containerWidth - indicatorSize));
    const finalY = Math.max(0, Math.min(position.y - offset, dimensions.containerHeight - indicatorSize));

    this.progressIndicator.style.left = `${finalX}px`;
    this.progressIndicator.style.top = `${finalY}px`;
    this.progressIndicator.style.transform = 'none'; // Override default centering transform

    // Ensure visibility
    this.progressIndicator.style.display = 'flex';
    this.progressIndicator.style.opacity = '1';

    // Validate position after update
    setTimeout(() => this.validatePosition(), 50);

    // Debug mode: Add visual debugging info
    const isDebugMode = window.location.search.includes('debug=true');
    if (isDebugMode) {
      // Calculate time-based information for debugging
      const currentTime = getBeirutTime();
      const currentMinutes = currentTime.getHours() * 60 + currentTime.getMinutes();

      // Get prayer times in minutes
      const currentPrayerKey = segment.current.charAt(0).toUpperCase() + segment.current.slice(1);
      const nextPrayerKey = segment.next.charAt(0).toUpperCase() + segment.next.slice(1);
      const prayerMinutes = timeToMinutes(this.prayerTimes[currentPrayerKey]);
      const nextPrayerMinutes = timeToMinutes(this.prayerTimes[nextPrayerKey]);

      console.log('🎯 Position Update:', {
        time: `${currentTime.getHours().toString().padStart(2, '0')}:${currentTime.getMinutes().toString().padStart(2, '0')}`,
        prayerTimes: {
          current: `${currentPrayerKey}: ${this.prayerTimes[currentPrayerKey]} (${prayerMinutes}min)`,
          next: `${nextPrayerKey}: ${this.prayerTimes[nextPrayerKey]} (${nextPrayerMinutes}min)`
        },
        angles: {
          start: startAngle.toFixed(1) + '°',
          end: endAngle.toFixed(1) + '°',
          current: normalizedAngle.toFixed(1) + '°',
          diff: angleDiff.toFixed(1) + '°'
        },
        position: {
          svg: { x: position.x.toFixed(1), y: position.y.toFixed(1) },
          final: { x: finalX.toFixed(1), y: finalY.toFixed(1) }
        },
        dimensions: {
          container: `${dimensions.containerWidth.toFixed(1)}x${dimensions.containerHeight.toFixed(1)}`,
          scale: `${dimensions.scaleX.toFixed(3)}x${dimensions.scaleY.toFixed(3)}`,
          radius: dimensions.radius.toFixed(1)
        },
        segment: `${segment.current} → ${segment.next} (${(segment.progress * 100).toFixed(1)}%)`,
        timeInfo: {
          current: currentMinutes,
          elapsed: segment.elapsed || 0,
          duration: segment.duration || 0
        }
      });

      // Add visual debug indicator
      this.progressIndicator.style.boxShadow = '0 0 0 2px red, 0 0 0 4px rgba(255,0,0,0.3)';

      // Add debug circle to show the timeline boundary
      this.addDebugCircle(dimensions);
    } else {
      this.progressIndicator.style.boxShadow = '';
      this.removeDebugCircle();
    }

    // Update celestial body appearance with smooth transitions
    const celestialType = this.getCelestialBodyType(segment.current, segment.next, segment.progress);
    if (this.celestialBody.className !== `celestial-body ${celestialType}`) {
      this.celestialBody.className = `celestial-body ${celestialType}`;
    }
  }

  setProgressForTime(hour, minute) {
    const segment = this.getCurrentPrayerSegment(hour, minute);
    this.updateProgressIndicator(segment);
  }

  // Force update for responsive changes
  forceUpdate() {
    const now = getBeirutTime();
    this.setProgressForTime(now.getHours(), now.getMinutes());
  }

  // Validate that the progress indicator is within the timeline circle
  validatePosition() {
    if (!this.progressIndicator) return false;

    const dimensions = this.getContainerDimensions();
    const indicatorRect = this.progressIndicator.getBoundingClientRect();
    const centerX = dimensions.containerWidth / 2;
    const centerY = dimensions.containerHeight / 2;

    // Get indicator center position relative to container
    const indicatorCenterX = parseFloat(this.progressIndicator.style.left) + (indicatorRect.width / 2);
    const indicatorCenterY = parseFloat(this.progressIndicator.style.top) + (indicatorRect.height / 2);

    // Calculate distance from timeline center
    const distance = Math.sqrt(
      Math.pow(indicatorCenterX - centerX, 2) +
      Math.pow(indicatorCenterY - centerY, 2)
    );

    // Check if within acceptable radius (should be less than the timeline circle radius)
    const maxAllowedDistance = dimensions.radius * dimensions.scaleX;
    const isWithinBounds = distance <= maxAllowedDistance;

    if (window.location.search.includes('debug=true')) {
      console.log('📍 Position Validation:', {
        distance: distance.toFixed(1),
        maxAllowed: maxAllowedDistance.toFixed(1),
        isValid: isWithinBounds,
        indicatorPos: { x: indicatorCenterX.toFixed(1), y: indicatorCenterY.toFixed(1) },
        center: { x: centerX.toFixed(1), y: centerY.toFixed(1) }
      });
    }

    return isWithinBounds;
  }

  // Add debug circle to visualize the timeline boundary
  addDebugCircle(dimensions) {
    this.removeDebugCircle(); // Remove existing debug circle

    const debugCircle = document.createElement('div');
    debugCircle.id = 'debug-timeline-circle';
    debugCircle.style.cssText = `
      position: absolute;
      top: 50%;
      left: 50%;
      width: ${dimensions.radius * 2 * dimensions.scaleX}px;
      height: ${dimensions.radius * 2 * dimensions.scaleY}px;
      border: 2px dashed rgba(255, 0, 0, 0.5);
      border-radius: 50%;
      transform: translate(-50%, -50%);
      pointer-events: none;
      z-index: 10;
    `;

    const progressContainer = document.querySelector('.progress-container');
    if (progressContainer) {
      progressContainer.appendChild(debugCircle);
    }
  }

  // Remove debug circle
  removeDebugCircle() {
    const existingCircle = document.getElementById('debug-timeline-circle');
    if (existingCircle) {
      existingCircle.remove();
    }
  }

  // Test method to manually set progress indicator position
  testPosition(hour, minute) {
    console.log(`🧪 Testing position for ${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`);
    this.setProgressForTime(hour, minute);
  }

  startRealTimeUpdates() {
    const updateProgress = () => {
      const now = getBeirutTime();
      this.setProgressForTime(now.getHours(), now.getMinutes());
    };

    // Update immediately
    updateProgress();

    // Update every 30 seconds for smooth movement
    setInterval(updateProgress, 30000);



    // Handle window resize with immediate update
    let resizeTimeout;
    const handleResize = () => {
      clearTimeout(resizeTimeout);
      resizeTimeout = setTimeout(() => {
        // Force immediate update on resize to maintain accurate positioning
        updateProgress();
      }, 100); // Shorter delay for better responsiveness
    };

    window.addEventListener('resize', handleResize);

    // Also listen for orientation changes on mobile
    window.addEventListener('orientationchange', () => {
      setTimeout(updateProgress, 200); // Slight delay for orientation change
    });

    // Handle viewport changes on mobile browsers
    let viewportTimeout;
    const handleViewportChange = () => {
      clearTimeout(viewportTimeout);
      viewportTimeout = setTimeout(updateProgress, 150);
    };

    // Listen for viewport meta changes (mobile browser UI changes)
    window.addEventListener('scroll', handleViewportChange);
    document.addEventListener('fullscreenchange', handleViewportChange);
  }
}

// Global instance
let celestialIndicator = null;

// Comprehensive visual state verification
function verifyVisualStateChanges() {
  if (!celestialIndicator) return false;

  console.log('� VERIFYING VISUAL STATE CHANGES:');

  const celestialBody = document.getElementById('celestialBody');
  if (!celestialBody) {
    console.log('❌ Celestial body element not found');
    return false;
  }

  // Test all visual states
  const visualStates = [
    { name: 'night-moon', description: 'Deep blue moon with crater shadows' },
    { name: 'predawn-moon', description: 'Light blue moon for early morning' },
    { name: 'sunrise-sun', description: 'Orange-red sun with warm glow' },
    { name: 'day-sun', description: 'Bright yellow sun with strong radiance' },
    { name: 'sunset-sun', description: 'Orange-red sun with evening warmth' },
    { name: 'evening-moon', description: 'Light blue moon with gentle glow' }
  ];

  console.log('🌟 Testing Visual States:');

  let testResults = [];

  visualStates.forEach((state, index) => {
    setTimeout(() => {
      // Apply the state
      celestialBody.className = `celestial-body ${state.name}`;

      // Check if the state was applied
      const hasCorrectClass = celestialBody.classList.contains(state.name);
      const computedStyle = window.getComputedStyle(celestialBody);
      const hasBackground = computedStyle.background !== 'none';
      const hasAnimation = computedStyle.animationName !== 'none';

      const result = {
        state: state.name,
        description: state.description,
        classApplied: hasCorrectClass,
        hasBackground: hasBackground,
        hasAnimation: hasAnimation,
        isValid: hasCorrectClass && hasBackground
      };

      testResults.push(result);

      console.log(`${result.isValid ? '✅' : '❌'} ${state.name}: ${state.description}`, {
        classApplied: result.classApplied,
        hasBackground: result.hasBackground,
        hasAnimation: result.hasAnimation
      });

      // Test transitions after all states are tested
      if (index === visualStates.length - 1) {
        setTimeout(() => {
          testVisualTransitions(testResults);
        }, 500);
      }
    }, index * 800); // 800ms delay between each state test
  });

  return true;
}

function testVisualTransitions(previousResults) {
  console.log('\n🔄 TESTING VISUAL TRANSITIONS:');

  const celestialBody = document.getElementById('celestialBody');
  if (!celestialBody) return;

  // Test transition smoothness
  const transitionStates = [
    ['night-moon', 'predawn-moon'],
    ['predawn-moon', 'sunrise-sun'],
    ['sunrise-sun', 'day-sun'],
    ['day-sun', 'sunset-sun'],
    ['sunset-sun', 'evening-moon'],
    ['evening-moon', 'night-moon']
  ];

  let transitionIndex = 0;

  const testTransition = () => {
    if (transitionIndex >= transitionStates.length) {
      console.log('\n🎉 VISUAL STATE VERIFICATION COMPLETE!');

      // Summary
      const allValid = previousResults.every(r => r.isValid);
      console.log(`📊 Summary: ${allValid ? 'ALL STATES VALID' : 'SOME ISSUES DETECTED'}`);

      if (allValid) {
        console.log('✅ All visual states are accurate and visually appealing');
        console.log('✅ Smooth transitions implemented with CSS cubic-bezier');
        console.log('✅ Sun states have pulsing animation and rotating rays');
        console.log('✅ Moon states have gentle glow and crater details');
      }

      return;
    }

    const [fromState, toState] = transitionStates[transitionIndex];

    // Apply first state
    celestialBody.className = `celestial-body ${fromState}`;

    setTimeout(() => {
      // Transition to second state
      celestialBody.className = `celestial-body ${toState}`;
      console.log(`🔄 Transition: ${fromState} → ${toState}`);

      transitionIndex++;
      setTimeout(testTransition, 1200); // Wait for transition to complete
    }, 300);
  };

  testTransition();
}

// Prayer-specific visual state verification
function verifyPrayerSpecificStates() {
  if (!celestialIndicator) return;

  console.log('\n🕌 VERIFYING PRAYER-SPECIFIC VISUAL STATES:');

  const prayerStates = [
    { prayer: 'isha', progress: 0.1, expected: 'evening-moon', description: 'Early Isha - Evening moon' },
    { prayer: 'isha', progress: 0.5, expected: 'night-moon', description: 'Mid Isha - Night moon' },
    { prayer: 'fajr', progress: 0.3, expected: 'predawn-moon', description: 'Early Fajr - Predawn moon' },
    { prayer: 'fajr', progress: 0.7, expected: 'sunrise-sun', description: 'Late Fajr - Sunrise sun' },
    { prayer: 'sunrise', progress: 0.5, expected: 'sunrise-sun', description: 'Sunrise period - Sunrise sun' },
    { prayer: 'dhuhr', progress: 0.5, expected: 'day-sun', description: 'Dhuhr period - Day sun' },
    { prayer: 'asr', progress: 0.5, expected: 'day-sun', description: 'Early Asr - Day sun' },
    { prayer: 'asr', progress: 0.9, expected: 'sunset-sun', description: 'Late Asr - Sunset sun' },
    { prayer: 'maghrib', progress: 0.1, expected: 'sunset-sun', description: 'Early Maghrib - Sunset sun' },
    { prayer: 'maghrib', progress: 0.5, expected: 'evening-moon', description: 'Late Maghrib - Evening moon' }
  ];

  prayerStates.forEach((test, index) => {
    const result = celestialIndicator.getCelestialBodyType(test.prayer, 'next', test.progress);
    const isCorrect = result === test.expected;

    console.log(`${isCorrect ? '✅' : '❌'} ${test.description}:`, {
      expected: test.expected,
      actual: result,
      correct: isCorrect
    });
  });
}

// Comprehensive verification function
function verifyCelestialIndicatorRequirements() {
  console.log('\n🔍 COMPREHENSIVE CELESTIAL INDICATOR VERIFICATION:');

  if (!celestialIndicator) {
    console.log('❌ CRITICAL: Celestial indicator not initialized');
    return false;
  }

  let allTestsPassed = true;

  // Test 1: Prayer points dynamic positioning
  console.log('\n1️⃣ TESTING: Prayer Points Dynamic Positioning');
  try {
    const prayerAngles = celestialIndicator.prayerAngles;
    const prayerTimes = celestialIndicator.prayerTimes;

    if (!prayerAngles || Object.keys(prayerAngles).length !== 6) {
      console.log('❌ Prayer angles not properly calculated');
      allTestsPassed = false;
    } else {
      console.log('✅ Prayer angles calculated:', prayerAngles);

      // Verify angles are different (not fixed)
      const angles = Object.values(prayerAngles);
      const uniqueAngles = [...new Set(angles)];
      if (uniqueAngles.length < 6) {
        console.log('⚠️  Warning: Some prayer angles are identical');
      }

      // Calculate and verify day/night durations
      const fajrMinutes = celestialIndicator.timeToMinutes(prayerTimes.fajr.hour, prayerTimes.fajr.minute);
      const maghribMinutes = celestialIndicator.timeToMinutes(prayerTimes.maghrib.hour, prayerTimes.maghrib.minute);
      const nightDuration = (fajrMinutes - maghribMinutes + 1440) % 1440;
      const dayDuration = (maghribMinutes - fajrMinutes + 1440) % 1440;

      console.log(`Night: ${nightDuration}min (${(nightDuration/60).toFixed(1)}h), Day: ${dayDuration}min (${(dayDuration/60).toFixed(1)}h)`);
      console.log('✅ Dynamic positioning based on actual day/night durations');
    }
  } catch (error) {
    console.log('❌ Error in prayer positioning test:', error);
    allTestsPassed = false;
  }

  // Test 2: Progress indicator movement
  console.log('\n2️⃣ TESTING: Progress Indicator Movement');
  try {
    const now = getBeirutTime();
    const segment = celestialIndicator.getCurrentPrayerSegment(now.getHours(), now.getMinutes());

    if (!segment || !segment.current || !segment.next) {
      console.log('❌ Current prayer segment not properly determined');
      allTestsPassed = false;
    } else {
      console.log(`Current: ${segment.current} → ${segment.next}, Progress: ${(segment.progress * 100).toFixed(1)}%`);

      // Test progress calculation
      if (segment.progress >= 0 && segment.progress <= 1) {
        console.log('✅ Progress calculation within valid range');
      } else {
        console.log('❌ Progress calculation out of range:', segment.progress);
        allTestsPassed = false;
      }

      // Test position calculation
      const dimensions = celestialIndicator.getContainerDimensions();
      const startAngle = celestialIndicator.prayerAngles[segment.current];
      const endAngle = celestialIndicator.prayerAngles[segment.next];

      if (startAngle !== undefined && endAngle !== undefined) {
        console.log('✅ Prayer angles available for position calculation');
      } else {
        console.log('❌ Prayer angles missing for position calculation');
        allTestsPassed = false;
      }
    }
  } catch (error) {
    console.log('❌ Error in progress movement test:', error);
    allTestsPassed = false;
  }

  // Test 3: Visual state changes
  console.log('\n3️⃣ TESTING: Visual State Changes');
  try {
    const now = getBeirutTime();
    const segment = celestialIndicator.getCurrentPrayerSegment(now.getHours(), now.getMinutes());
    const celestialType = celestialIndicator.getCelestialBodyType(segment.current, segment.next, segment.progress);

    const validTypes = ['night-moon', 'predawn-moon', 'sunrise-sun', 'day-sun', 'sunset-sun', 'evening-moon'];
    if (validTypes.includes(celestialType)) {
      console.log(`✅ Valid celestial body type: ${celestialType}`);

      // Check if DOM element exists and has correct class
      const celestialBody = document.getElementById('celestialBody');
      if (celestialBody && celestialBody.className.includes(celestialType)) {
        console.log('✅ DOM element properly updated with celestial type');
      } else {
        console.log('❌ DOM element not properly updated');
        allTestsPassed = false;
      }
    } else {
      console.log('❌ Invalid celestial body type:', celestialType);
      allTestsPassed = false;
    }
  } catch (error) {
    console.log('❌ Error in visual state test:', error);
    allTestsPassed = false;
  }

  // Test 4: DOM Integration
  console.log('\n4️⃣ TESTING: DOM Integration');
  try {
    const progressIndicator = document.getElementById('progressIndicator');
    const celestialBody = document.getElementById('celestialBody');

    if (!progressIndicator || !celestialBody) {
      console.log('❌ Required DOM elements missing');
      allTestsPassed = false;
    } else {
      console.log('✅ Required DOM elements present');

      // Check positioning
      const style = progressIndicator.style;
      if (style.left && style.top) {
        console.log('✅ Progress indicator positioned');
      } else {
        console.log('❌ Progress indicator not positioned');
        allTestsPassed = false;
      }
    }
  } catch (error) {
    console.log('❌ Error in DOM integration test:', error);
    allTestsPassed = false;
  }

  // Final result
  console.log('\n🏁 VERIFICATION RESULT:');
  if (allTestsPassed) {
    console.log('🎉 ALL TESTS PASSED - Celestial indicator working correctly!');
    return true;
  } else {
    console.log('❌ SOME TESTS FAILED - Issues detected');
    return false;
  }
}

function getPrayerPointSizePx() {
  const el = document.createElement('div');
  el.style.display = 'none';
  el.className = 'prayer-point';
  document.body.appendChild(el);
  const size = parseFloat(getComputedStyle(el).width) || 60;
  document.body.removeChild(el);
  return size;
}

function getPrayerPointsRect() {
  const el = document.getElementById('prayer-points-dynamic');
  if (!el) return { width: 380, height: 380 };
  const rect = el.getBoundingClientRect();
  return { width: rect.width, height: rect.height };
}

function getPrayerAngles(times) {
  const keys = ['Fajr', 'Sunrise', 'Dhuhr', 'Asr', 'Maghrib', 'Isha'];
  const t = keys.map(k => times[k]).map(timeToMinutes);
  const [fajr, sunrise, dhuhr, asr, maghrib, isha] = t;
  const total = 1440;
  const nightDuration = (fajr - maghrib + total) % total;
  const dayDuration = (maghrib - fajr + total) % total;
  return t.map((time, i) => {
    let isNight;
    if (maghrib < fajr) {
      isNight = (time >= maghrib || time < fajr);
    } else {
      isNight = (time >= maghrib || time < fajr);
    }
    let angle;
    if (isNight) {
      let sinceMaghrib = (time - maghrib + total) % total;
      let fraction = nightDuration === 0 ? 0 : sinceMaghrib / nightDuration;
      angle = 90 - fraction * 180;
    } else {
      let sinceFajr = (time - fajr + total) % total;
      let fraction = dayDuration === 0 ? 0 : sinceFajr / dayDuration;
      angle = 270 - fraction * 180;
    }
    return (angle + 360) % 360;
  });
}

function renderPrayerTimelineSVG(prayers, times, currentPrayer) {
  const svg = document.getElementById('prayer-timeline-svg');
  if (!svg) return;
  svg.innerHTML = '';
  const size = 1000;
  const centerX = size / 2;
  // If aspect ratio is not 1:1, move centerY up and reduce radius
  const centerY = size * 0.46;
  const radius = 340;
  // SVG gradients and glows
  const defs = document.createElementNS('http://www.w3.org/2000/svg', 'defs');
  defs.innerHTML = `
    <radialGradient id="fajr-gradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" stop-color="#fff"/>
      <stop offset="70%" stop-color="#b3d8ff"/>
      <stop offset="100%" stop-color="#3a6ea5"/>
    </radialGradient>
    <radialGradient id="isha-gradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" stop-color="#fff"/>
      <stop offset="70%" stop-color="#b3d8ff"/>
      <stop offset="100%" stop-color="#3a6ea5"/>
    </radialGradient>
    <radialGradient id="sunrise-gradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" stop-color="#fffbe4"/>
      <stop offset="60%" stop-color="#ffd700"/>
      <stop offset="100%" stop-color="#ff9800"/>
    </radialGradient>
    <radialGradient id="prayer-gradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" stop-color="#fff"/>
      <stop offset="70%" stop-color="#ffe9a0"/>
      <stop offset="100%" stop-color="#e0c080"/>
    </radialGradient>
    <radialGradient id="current-gradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" stop-color="#fff"/>
      <stop offset="70%" stop-color="#ffe9a0"/>
      <stop offset="100%" stop-color="#ffd700"/>
    </radialGradient>
    <filter id="fajr-glow" x="-30%" y="-30%" width="160%" height="160%">
      <feDropShadow dx="0" dy="0" stdDeviation="6" flood-color="#3a6ea5" flood-opacity="0.35"/>
    </filter>
    <filter id="isha-glow" x="-30%" y="-30%" width="160%" height="160%">
      <feDropShadow dx="0" dy="0" stdDeviation="6" flood-color="#3a6ea5" flood-opacity="0.35"/>
    </filter>
    <filter id="sunrise-glow" x="-30%" y="-30%" width="160%" height="160%">
      <feDropShadow dx="0" dy="0" stdDeviation="10" flood-color="#ff9800" flood-opacity="0.45"/>
    </filter>
    <filter id="prayer-glow" x="-30%" y="-30%" width="160%" height="160%">
      <feDropShadow dx="0" dy="0" stdDeviation="6" flood-color="#ffd700" flood-opacity="0.35"/>
    </filter>
    <filter id="current-glow" x="-40%" y="-40%" width="180%" height="180%">
      <feDropShadow dx="0" dy="0" stdDeviation="12" flood-color="#ffd700" flood-opacity="0.55"/>
    </filter>
  `;
  svg.appendChild(defs);
  // Draw dashed orbit
  const orbit = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
  orbit.setAttribute('cx', centerX);
  orbit.setAttribute('cy', centerY);
  orbit.setAttribute('r', radius);
  orbit.setAttribute('fill', 'none');
  orbit.setAttribute('stroke', '#CD853F');
  orbit.setAttribute('stroke-width', '12');
  orbit.setAttribute('stroke-dasharray', '30,18');
  orbit.setAttribute('opacity', '0.5');
  svg.appendChild(orbit);
  // Prayer points
  const angles = getPrayerAngles(times);
  prayers.forEach((prayer, i) => {
    const angleDeg = angles[i];
    const angleRad = angleDeg * Math.PI / 180;
    const px = centerX + radius * Math.cos(angleRad);
    const py = centerY - radius * Math.sin(angleRad);
    const g = document.createElementNS('http://www.w3.org/2000/svg', 'g');
    g.setAttribute('class', `prayer-point-svg ${prayer.key.toLowerCase()}` + (currentPrayer === prayer.key ? ' current' : ''));
    // Visuals
    let circleFill, circleStroke, filter, icon, labelColor, timeColor, circleRadius, borderWidth;
    if (prayer.key === 'Sunrise') {
      circleFill = 'url(#sunrise-gradient)';
      circleStroke = '#ff980080';
      filter = 'url(#sunrise-glow)';
      labelColor = '#E6B800';
      timeColor = '#E6B800';
      circleRadius = currentPrayer === prayer.key ? 60 : 52;
      borderWidth = 3.5;
      // Sun with rays icon
      icon = `<g>
        <circle cx="0" cy="0" r="20" fill="#FFD700"/>
        <g stroke="#FF9800" stroke-width="3">
          <line x1="0" y1="-32" x2="0" y2="-44"/>
          <line x1="0" y1="32" x2="0" y2="44"/>
          <line x1="-32" y1="0" x2="-44" y2="0"/>
          <line x1="32" y1="0" x2="44" y2="0"/>
          <line x1="22" y1="22" x2="32" y2="32"/>
          <line x1="-22" y1="22" x2="-32" y2="32"/>
          <line x1="22" y1="-22" x2="32" y2="-32"/>
          <line x1="-22" y1="-22" x2="-32" y2="-32"/>
        </g>
      </g>`;
    } else if (prayer.key === 'Fajr' || prayer.key === 'Isha' || prayer.key === 'Maghrib') {
      // Fajr, Isha, Maghrib: blue gradient, crescent moon, blue border/glow
      circleFill = 'url(#fajr-gradient)';
      circleStroke = '#3a6ea580';
      filter = 'url(#fajr-glow)';
      labelColor = '#3a6ea5';
      timeColor = '#3a6ea5';
      circleRadius = currentPrayer === prayer.key ? 60 : 52;
      borderWidth = 3.5;
      icon = `<g>
        <path d="M 0 -18 A 18 18 0 1 0 0 18 Q -8 0 0 -18" fill="#b3d8ff" stroke="#3a6ea5" stroke-width="2.5"/>
        <circle cx="-5" cy="-5" r="3.5" fill="#FFFDF7"/>
      </g>`;
    } else if (prayer.key === 'Dhuhr') {
      // Dhuhr: sun at zenith icon
      circleFill = 'url(#prayer-gradient)';
      circleStroke = '#ffd70080';
      filter = 'url(#prayer-glow)';
      labelColor = '#8B4513';
      timeColor = '#3D8B40';
      circleRadius = currentPrayer === prayer.key ? 60 : 52;
      borderWidth = 3.5;
      icon = `<g>
        <circle cx="0" cy="0" r="16" fill="#FFD700" stroke="#DAA520" stroke-width="2"/>
        <g stroke="#FFD700" stroke-width="2.5">
          <line x1="0" y1="-22" x2="0" y2="-32"/>
          <line x1="0" y1="22" x2="0" y2="32"/>
          <line x1="-22" y1="0" x2="-32" y2="0"/>
          <line x1="22" y1="0" x2="32" y2="0"/>
        </g>
      </g>`;
    } else if (prayer.key === 'Asr') {
      // Asr: sun descending icon (sun with 3 rays on lower right)
      circleFill = 'url(#prayer-gradient)';
      circleStroke = '#ffd70080';
      filter = 'url(#prayer-glow)';
      labelColor = '#8B4513';
      timeColor = '#3D8B40';
      circleRadius = currentPrayer === prayer.key ? 60 : 52;
      borderWidth = 3.5;
      icon = `<g>
        <circle cx="0" cy="0" r="16" fill="#FFD700" stroke="#DAA520" stroke-width="2"/>
        <g stroke="#FFD700" stroke-width="2.5">
          <line x1="8" y1="8" x2="18" y2="18"/>
          <line x1="0" y1="16" x2="0" y2="28"/>
          <line x1="-8" y1="8" x2="-18" y2="18"/>
        </g>
      </g>`;
    }
    // Circle
    const c = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
    c.setAttribute('cx', px);
    c.setAttribute('cy', py);
    c.setAttribute('r', circleRadius);
    c.setAttribute('fill', circleFill);
    c.setAttribute('stroke', circleStroke);
    c.setAttribute('stroke-width', borderWidth);
    c.setAttribute('filter', filter);
    g.appendChild(c);
    // Icon
    const iconG = document.createElementNS('http://www.w3.org/2000/svg', 'g');
    iconG.setAttribute('transform', `translate(${px},${py-7})`);
    iconG.innerHTML = icon;
    g.appendChild(iconG);
    // Prayer name
    if (prayer.key === 'Sunrise') {
      const sunriseLabel = document.createElementNS('http://www.w3.org/2000/svg', 'text');
      sunriseLabel.setAttribute('x', px);
      sunriseLabel.setAttribute('y', py + circleRadius + 32);
      sunriseLabel.setAttribute('text-anchor', 'middle');
      sunriseLabel.setAttribute('font-size', '32');
      sunriseLabel.setAttribute('font-family', 'Reem Kufi, Amiri, serif');
      sunriseLabel.setAttribute('fill', '#E6B800');
      sunriseLabel.setAttribute('font-weight', 'bold');
      sunriseLabel.textContent = 'الشروق';
      g.appendChild(sunriseLabel);
    } else {
      const t = document.createElementNS('http://www.w3.org/2000/svg', 'text');
      t.setAttribute('x', px);
      t.setAttribute('y', py + circleRadius + 32);
      t.setAttribute('text-anchor', 'middle');
      t.setAttribute('font-size', '38');
      t.setAttribute('font-family', 'Reem Kufi, Amiri, serif');
      t.setAttribute('fill', labelColor);
      t.textContent = PRAYER_NAMES[prayer.key] || prayer.key;
      g.appendChild(t);
    }
    // Prayer time
    const timeText = document.createElementNS('http://www.w3.org/2000/svg', 'text');
    timeText.setAttribute('x', px);
    timeText.setAttribute('y', py + circleRadius + 70);
    timeText.setAttribute('text-anchor', 'middle');
    timeText.setAttribute('font-size', '30');
    timeText.setAttribute('font-family', 'Reem Kufi, Amiri, serif');
    timeText.setAttribute('fill', timeColor);
    timeText.textContent = formatArabicTime(times[prayer.key]);
    g.appendChild(timeText);
    svg.appendChild(g);
  });
}

function renderNextPrayerInfo(nextPrayer, times, timeUntil) {
  updateText('.current-prayer-name', PRAYER_NAMES[nextPrayer]);
  updateText('.current-prayer-time', formatArabicTime(times[nextPrayer]));
  updateHTML('.time-remaining', `<i class="fas fa-hourglass-half"></i> ${timeUntil} حتى ${PRAYER_NAMES[nextPrayer]}`);
}

function renderLocationInfo(locationName) {
  updateHTML('.location-name', `<i class="fas fa-location-dot"></i> ${locationName}`);
}

// --- Main orchestrator ---
async function main() {
  renderGregorianDate();

  let moon = getMoonPhase();

  // Default to Sidon, Lebanon in case geolocation fails
  let location = { lat: 33.5575, lng: 35.3719 };
  let locationName = 'صيدا، لبنان';
  let cityEnglish = 'Sidon';
  let countryEnglish = 'Lebanon';

  try {
    // Use precise geolocation if user grants permission
    const pos = await new Promise((res, rej) => {
      if (!navigator.geolocation) return rej();
      navigator.geolocation.getCurrentPosition(res, rej, {
        timeout: 10000,
        enableHighAccuracy: true,
        maximumAge: 300000
      });
    });

    location = {
      lat: pos.coords.latitude,
      lng: pos.coords.longitude
    };

    // Reverse geocode in Arabic for UI display
    const geoData = await fetch(`https://nominatim.openstreetmap.org/reverse?lat=${location.lat}&lon=${location.lng}&format=json&accept-language=ar`)
      .then(r => r.json());

    const addr = geoData.address || {};
    const cityAr = addr.city || addr.town || addr.village;
    const stateAr = addr.state;
    const countryAr = addr.country;

    locationName = cityAr && countryAr
      ? `${cityAr}، ${countryAr}`
      : stateAr && countryAr
        ? `${stateAr}، ${countryAr}`
        : countryAr || 'موقعك الحالي';

    // Reverse geocode in English for API query
    const geoDataEn = await fetch(`https://nominatim.openstreetmap.org/reverse?lat=${location.lat}&lon=${location.lng}&format=json&accept-language=en`)
      .then(r => r.json());

    const addrEn = geoDataEn.address || {};
    cityEnglish = addrEn.city || addrEn.town || addrEn.village || cityEnglish;
    countryEnglish = addrEn.country || countryEnglish;

    console.debug(`Geolocation: ${location.lat}, ${location.lng}`);
    console.debug(`Location name: ${locationName} / ${cityEnglish}, ${countryEnglish}`);
  } catch (err) {
    // Use defaults silently if geolocation or reverse geocoding fails
    console.warn('Geolocation failed or denied, using fallback location', err);
  }

  renderLocationInfo(locationName);

  let timings = {};
  try {
    const today = getBeirutTime();
    const timestamp = Math.floor(today.getTime() / 1000);

    // Use city and country names to avoid coordinate inaccuracies in API
    const url = `https://api.aladhan.com/v1/timingsByCity/${timestamp}?city=${encodeURIComponent(cityEnglish)}&country=${encodeURIComponent(countryEnglish)}&method=${PRAYER_METHOD}&tune=0,0,0,0,0,0,0,0,0`;

    const data = await fetch(url).then(r => r.json());

    if (!data || data.code !== 200 || !data.data) {
      console.error('Invalid prayer API response:', data);
      return;
    }

    timings = {
      Fajr: data.data.timings.Fajr,
      Sunrise: data.data.timings.Sunrise,
      Dhuhr: data.data.timings.Dhuhr,
      Asr: data.data.timings.Asr,
      Maghrib: data.data.timings.Maghrib,
      Isha: data.data.timings.Isha
    };

  } catch (e) {
    console.error('Failed to fetch prayer times:', e);
    return;
  }

  if (!validatePrayerTimes(timings)) return;

  const hijri = await getHijriDate(getBeirutTime(), timings.Maghrib);
  renderHijriDate(hijri, moon);

  const prayers = [
    { key: 'Fajr', icon: 'fa-moon' },
    { key: 'Sunrise', icon: 'fa-sun' },
    { key: 'Dhuhr', icon: 'fa-sun' },
    { key: 'Asr', icon: 'fa-sun' },
    { key: 'Maghrib', icon: 'fa-sunset' },
    { key: 'Isha', icon: 'fa-moon' }
  ];

  const timesInMinutes = convertPrayerTimesToMinutes(timings);

  let currentPrayer = null, timeUntil = '';

  function updatePrayerUI() {
    const nowMinutes = getCurrentBeirutMinutes();
    const next = findNextPrayer(nowMinutes, timesInMinutes);
    currentPrayer = next;
    timeUntil = calculateTimeRemaining(nowMinutes, timesInMinutes[next]);
    renderNextPrayerInfo(next, timings, timeUntil);
    renderPrayerTimelineSVG(prayers, timings, next);
  }

  updatePrayerUI();
  setInterval(() => {
    renderCurrentTime();
    updatePrayerUI();
  }, 1000);

  // Initialize the celestial progress indicator with a small delay to ensure DOM is ready
  setTimeout(() => {
    celestialIndicator = new CelestialProgressIndicator();
    celestialIndicator.init(timings);

    // Make celestialIndicator globally accessible for testing
    window.celestialIndicator = celestialIndicator;

    // Add global test function
    window.testProgressAt = function(hour, minute) {
      if (window.celestialIndicator) {
        window.celestialIndicator.testPosition(hour, minute);
      } else {
        console.log('❌ Celestial indicator not initialized');
      }
    };

    // Run comprehensive tests if in debug mode
    if (window.location.search.includes('debug=true')) {
      runCelestialIndicatorTests(celestialIndicator);
    }
  }, 100);

  // Initialize the content manager
  contentManager.init();
}

document.addEventListener('DOMContentLoaded', main);
  </script>
</body>
</html>