.. _glossary:

Glossary
========

From Wikipedia, the free encyclopedia

.. glossary::

    .htaccess
      the default name of Apache's directory-level configuration file.

      .. seealso:: <https://en.wikipedia.org/wiki/.htaccess>

    ACL
      Access Control List

    Blowfish
      a keyed, symmetric block cipher, designed in 1993 by <PERSON><PERSON> <https://en.wikipedia.org/wiki/<PERSON>>`_.

      .. seealso:: <https://en.wikipedia.org/wiki/Blowfish_(cipher)>

    Browser
      a software application that enables a user to display and interact with text, images, and other information typically located on a web page at a website on the World Wide Web.

      .. seealso:: <https://en.wikipedia.org/wiki/Web_browser>

    bzip2
      a free software/open-source data compression algorithm and program developed by <PERSON>.

      .. seealso:: <https://en.wikipedia.org/wiki/Bzip2>

    CGI
      Common Gateway Interface is an important World Wide Web technology that
      enables a client web browser to request data from a program executed on
      the web server.

      .. seealso:: <https://en.wikipedia.org/wiki/Common_Gateway_Interface>

    Changelog
      a log or record of changes made to a project.

      .. seealso:: <https://en.wikipedia.org/wiki/Changelog>

    Client
      a computer system that accesses a (remote) service on another computer by some kind of network.

      .. seealso:: <https://en.wikipedia.org/wiki/Client_(computing)>

    column
      a set of data values of a particularly simple type, one for each row of the table.

      .. seealso:: <https://en.wikipedia.org/wiki/Column_(database)>

    Cookie
      a packet of information sent by a server to a World Wide Web browser and then sent back by the browser each time it accesses that server.

      .. seealso:: <https://en.wikipedia.org/wiki/HTTP_cookie>

    CSV
      Comma-separated values

      .. seealso:: <https://en.wikipedia.org/wiki/Comma-separated_values>

    DB
      look at :term:`Database`

    Database
      an organized collection of data.

      .. seealso:: <https://en.wikipedia.org/wiki/Database>

    Engine
      look at :term:`Storage Engines`

    PHP extension
      a PHP module that extends PHP with additional functionality.

      .. seealso:: <https://en.wikipedia.org/wiki/Software_extension>

    FAQ
      Frequently Asked Questions is a list of commonly asked question and their
      answers.

      .. seealso:: <https://en.wikipedia.org/wiki/FAQ>

    Field
      one part of divided data/columns.

      .. seealso:: <https://en.wikipedia.org/wiki/Field_(computer_science)>

    Foreign key
      a column or group of columns in a database row that points to a key column
      or group of columns forming a key of another database row in some
      (usually different) table.

      .. seealso:: <https://en.wikipedia.org/wiki/Foreign_key>

    GD
      Graphics Library by Thomas Boutell and others for dynamically manipulating images.

      .. seealso:: <https://en.wikipedia.org/wiki/GD_Graphics_Library>

    GD2
      look at :term:`GD`

    GZip
      GZip is short for GNU zip, a GNU free software file compression program.

      .. seealso:: <https://en.wikipedia.org/wiki/Gzip>

    host
      any machine connected to a computer network, a node that has a hostname.

      .. seealso:: <https://en.wikipedia.org/wiki/Host_(network)>

    hostname
      the unique name by which a network-attached device is known on a network.

      .. seealso:: <https://en.wikipedia.org/wiki/Hostname>

    HTTP
      Hypertext Transfer Protocol is the primary method used to transfer or
      convey information on the World Wide Web.

      .. seealso:: <https://en.wikipedia.org/wiki/HyperText_Transfer_Protocol>

    HTTPS
      a :term:`HTTP`-connection with additional security measures.

      .. seealso:: <https://en.wikipedia.org/wiki/HTTPS>

    IEC
      International Electrotechnical Commission

    IIS
      Internet Information Services is a set of internet-based services for
      servers using Microsoft Windows.

      .. seealso:: <https://en.wikipedia.org/wiki/Internet_Information_Services>

    Index
      a feature that allows quick access to the rows in a table.

      .. seealso:: <https://en.wikipedia.org/wiki/Database_index>

    IP
      "Internet Protocol" is a data-oriented protocol used by source and
      destination hosts for communicating data across a packet-switched
      internetwork.

      .. seealso:: <https://en.wikipedia.org/wiki/Internet_Protocol>

    IP Address
      a unique number that devices use in order to identify and communicate with each other on a network utilizing the Internet Protocol standard.

      .. seealso:: <https://en.wikipedia.org/wiki/IP_Address>

    IPv6
      IPv6 (Internet Protocol version 6) is the latest revision of the
      Internet Protocol (:term:`IP`), designed to deal with the
      long-anticipated problem of its predecessor IPv4 running out of addresses.

      .. seealso:: <https://en.wikipedia.org/wiki/IPv6>

    ISAPI
      Internet Server Application Programming Interface is the API of Internet Information Services (IIS).

      .. seealso:: <https://en.wikipedia.org/wiki/Internet_Server_Application_Programming_Interface>

    ISP
      An Internet service provider is a business or organization that offers users
      access to the Internet and related services.

      .. seealso:: <https://en.wikipedia.org/wiki/Internet_service_provider>

    ISO
      International Standards Organization

      .. seealso:: `ISO organization website <https://www.iso.org/about-us.html>`_
      .. seealso:: <https://en.wikipedia.org/wiki/International_Organization_for_Standardization>

    JPEG
      a most commonly used standard method of lossy compression for photographic images.

      .. seealso:: <https://en.wikipedia.org/wiki/JPEG>

    JPG
      look at :term:`JPEG`

    Key
      look at :term:`Index`

    LATEX
      a document preparation system for the TeX typesetting program.

      .. seealso:: <https://en.wikipedia.org/wiki/LaTeX>

    Mac
       Apple Macintosh is a line of personal computers designed, developed, manufactured, and marketed by Apple Inc.

       .. seealso:: <https://en.wikipedia.org/wiki/Macintosh>

    macOS
      the operating system which is included with all currently shipping Apple Macintosh computers in the consumer and professional markets.

      .. seealso:: <https://en.wikipedia.org/wiki/MacOS>

    mbstring
       The PHP `mbstring` functions provide support for languages represented by multi-byte character sets, most notably UTF-8.

       If you have troubles installing this extension, please follow :ref:`faqmysql`, it provides useful hints.

       .. seealso:: <https://www.php.net/manual/en/book.mbstring.php>

    Media type
      A media type (formerly known as MIME type) is a two-part identifier
      for file formats and format contents transmitted on the Internet.

      .. seealso:: <https://en.wikipedia.org/wiki/Media_type>

    MIME
      Multipurpose Internet Mail Extensions is
      an Internet Standard for the format of e-mail.

      .. seealso:: <https://en.wikipedia.org/wiki/MIME>

    module
      modular extension for the Apache HTTP Server httpd.

      .. seealso:: <https://en.wikipedia.org/wiki/Apache_HTTP_Server>

    mod_proxy_fcgi
      an Apache module implementing a Fast CGI interface; PHP can be run as a CGI module, FastCGI, or
      directly as an Apache module.

      .. seealso:: <https://en.wikipedia.org/wiki/Mod_proxy>

    MySQL
      a multithreaded, multi-user, SQL (Structured Query Language) Database Management System (DBMS).

      .. seealso:: <https://en.wikipedia.org/wiki/MySQL>

    MySQLi
      the improved MySQL client PHP extension.

      .. seealso:: `PHP manual for MySQL Improved Extension <https://www.php.net/manual/en/book.mysqli.php>`_
      .. seealso:: <https://en.wikipedia.org/wiki/MySQLi>

    mysql
      the MySQL client PHP extension.

      .. seealso:: <https://www.php.net/manual/en/book.mysql.php>

    OpenDocument
      an open standard for office documents.

      .. seealso:: <https://en.wikipedia.org/wiki/OpenDocument>

    OS X
      look at :term:`macOS`.

      .. seealso:: <https://en.wikipedia.org/wiki/MacOS>

    PDF
      Portable Document Format is a file format developed by Adobe Systems for
      representing two-dimensional documents in a device-independent and
      resolution-independent format.

      .. seealso:: <https://en.wikipedia.org/wiki/PDF>

    PEAR
      the PHP Extension and Application Repository.

      .. seealso:: `PEAR website <https://pear.php.net/>`_
      .. seealso:: `Wikipedia page for PEAR <https://en.wikipedia.org/wiki/PEAR>`_

    PCRE
      Perl-Compatible Regular Expressions is the Perl-compatible regular
      expression functions for PHP

      .. seealso:: <https://www.php.net/pcre>
      .. seealso:: `PHP manual for Perl-Compatible Regular Expressions <https://www.php.net/pcre>`_
      .. seealso:: <https://en.wikipedia.org/wiki/Perl_Compatible_Regular_Expressions>

    PHP
      short for "PHP: Hypertext Preprocessor", is an open-source, reflective
      programming language used mainly for developing server-side applications
      and dynamic web content, and more recently, a broader range of software
      applications.

      .. seealso:: <https://en.wikipedia.org/wiki/PHP>

    port
      a connection through which data is sent and received.

      .. seealso:: <https://en.wikipedia.org/wiki/Port_(computer_networking)>

    primary key
      A primary key is an index over one or more fields in a table with
      unique values for every single row in this table. Every table should have
      a primary key for easier accessing/identifying data in this table. There
      can only be one primary key per table and it is named always **PRIMARY**.
      In fact, a primary key is just an :term:`unique key` with the name
      **PRIMARY**. If no primary key is defined MySQL will use first *unique
      key* as primary key if there is one.

      You can create the primary key when creating the table (in phpMyAdmin
      just check the primary key radio buttons for each field you wish to be
      part of the primary key).

      You can also add a primary key to an existing table with `ALTER` `TABLE`
      or `CREATE` `INDEX` (in phpMyAdmin you can just click on 'add index' on
      the table structure page below the listed fields).

    RFC
      Request for Comments (RFC) documents are a series of memoranda
      encompassing new research, innovations, and methodologies applicable to
      Internet technologies.

      .. seealso:: <https://en.wikipedia.org/wiki/Request_for_Comments>

    RFC 1952
      GZIP file format specification version 4.3

      .. seealso:: :rfc:`1952`

    Row (record, tuple)
      represents a single, implicitly structured data item in a table.

      .. seealso:: <https://en.wikipedia.org/wiki/Row_(database)>

    Server
      a computer system that provides services to other computing systems over a network.

      .. seealso:: <https://en.wikipedia.org/wiki/Server_(computing)>

    Storage Engines
      MySQL can use several different formats for storing data on disk, these
      are called storage engines or table types. phpMyAdmin allows a user to
      change their storage engine for a particular table through the operations
      tab.

      Common table types are InnoDB and MyISAM, though many others exist and
      may be desirable in some situations.

      .. seealso:: `MySQL doc chapter about Alternative Storage Engines <https://dev.mysql.com/doc/refman/8.0/en/storage-engines.html>`_
      .. seealso:: <https://en.wikipedia.org/wiki/Database_engine>

    socket
      a form of inter-process communication.

      .. seealso:: <https://en.wikipedia.org/wiki/Unix_domain_socket>

    SSL
      Secure Sockets Layer, (now superseded by TLS) is a cryptographic protocol
      which provides secure communication on the Internet.

      .. seealso:: <https://en.wikipedia.org/wiki/Transport_Layer_Security>

    Stored procedure
      a subroutine available to applications accessing a relational database system

      .. seealso:: <https://en.wikipedia.org/wiki/Stored_procedure>

    SQL
      Structured Query Language

      .. seealso:: <https://en.wikipedia.org/wiki/SQL>

    table
      a set of data elements (cells) that is organized, defined and stored as
      horizontal rows and vertical columns where each item can be uniquely
      identified by a label or key or by its position in relation to other
      items.

      .. seealso:: <https://en.wikipedia.org/wiki/Table_(database)>

    tar
      a type of archive file format, from "Tape Archive".

      .. seealso:: <https://en.wikipedia.org/wiki/Tar_(computing)>

    TCP
      Transmission Control Protocol is one of the core protocols of the
      Internet protocol suite.

      .. seealso:: <https://en.wikipedia.org/wiki/Internet_protocol_suite>

    TCPDF
      PHP library to generate PDF files.

      .. seealso:: <https://tcpdf.org/>
      .. seealso:: <https://en.wikipedia.org/wiki/TCPDF>

    trigger
      a procedural code that is automatically executed in response to certain events on a particular table or view in a database

      .. seealso:: <https://en.wikipedia.org/wiki/Database_trigger>

    unique key
      A unique key is an index over one or more fields in a table which has a
      unique value for each row.  The first unique key will be treated as
      :term:`primary key` if there is no *primary key* defined.

    URL
      Uniform Resource Locator is a sequence of characters, conforming to a
      standardized format, that is used for referring to resources, such as
      documents and images on the Internet, by their location.

      .. seealso:: <https://en.wikipedia.org/wiki/URL>

    Web server
      A computer (program) that is responsible for accepting HTTP requests from clients and serving them web pages.

      .. seealso:: <https://en.wikipedia.org/wiki/Web_server>

    XML
      Extensible Markup Language is a W3C-recommended general-purpose markup
      language for creating special-purpose markup languages, capable of
      describing many different kinds of data.

      .. seealso:: <https://en.wikipedia.org/wiki/XML>

    ZIP
      a popular data compression and archival format.

      .. seealso:: <https://en.wikipedia.org/wiki/Zip_(file_format)>

    Zlib
      an open-source, cross-platform data compression library by `Jean-loup Gailly <https://en.wikipedia.org/wiki/Jean-Loup_Gailly>`_ and `Mark Adler <https://en.wikipedia.org/wiki/Mark_Adler>`_.

      .. seealso:: <https://en.wikipedia.org/wiki/Zlib>

    Content Security Policy
      The HTTP `Content-Security-Policy` response header allows web site administrators
      to control resources the user agent is allowed to load for a given page.

      .. seealso:: <https://en.wikipedia.org/wiki/Content_Security_Policy>
      .. seealso:: <https://developer.mozilla.org/en/docs/Web/HTTP/CSP>
