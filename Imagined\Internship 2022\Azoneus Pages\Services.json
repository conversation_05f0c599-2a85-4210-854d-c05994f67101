{"content": [{"id": "5d07801f", "settings": {"layout": "full_width", "gap": "no", "background_background": "classic", "background_color": "#EAEAEA", "padding": {"unit": "px", "top": "30", "right": "0", "bottom": "0", "left": "0", "isLinked": false}, "ae_dynamic_rules": [{"_id": "e72e479"}], "slider_delay": "5000"}, "elements": [{"id": "79dae3d6", "settings": {"_column_size": 100, "_inline_size": null, "ae_dynamic_rules": [{"_id": "d4f0398"}], "slider_delay": "5000"}, "elements": [{"id": "5d790a2c", "settings": {"ae_dynamic_rules": [{"_id": "e98321d"}], "slider_delay": "5000"}, "elements": [{"id": "4e357b9e", "settings": {"_column_size": 100, "_inline_size": null, "ae_dynamic_rules": [{"_id": "a4140a6"}], "slider_delay": "5000"}, "elements": [{"id": "5abcc731", "settings": {"title": "Services", "typography_typography": "custom", "typography_font_family": "Nunito", "typography_font_size": {"unit": "px", "size": 40, "sizes": []}, "typography_font_size_mobile": {"unit": "px", "size": 26, "sizes": []}, "typography_font_weight": "600", "_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "-35", "left": "0", "isLinked": false}, "typography_text_transform": "uppercase", "ae_dynamic_rules": [{"_id": "374b477"}]}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}, {"id": "304982b2", "settings": {"width": {"unit": "px", "size": 25, "sizes": []}, "width_tablet": {"unit": "px", "size": "", "sizes": []}, "width_mobile": {"unit": "px", "size": 20, "sizes": []}, "text": "Divider", "color": "#F4A41C", "weight": {"unit": "px", "size": 4, "sizes": []}, "gap": {"unit": "px", "size": 3, "sizes": []}, "_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": false}, "_margin_mobile": {"unit": "px", "top": "0", "right": "0", "bottom": "-7", "left": "0", "isLinked": false}, "_padding": {"unit": "px", "top": "15", "right": "0", "bottom": "15", "left": "0", "isLinked": false}, "ae_dynamic_rules": [{"_id": "a50b318"}]}, "elements": [], "isInner": false, "widgetType": "divider", "elType": "widget"}], "isInner": true, "elType": "column"}], "isInner": true, "elType": "section"}, {"id": "5fab8ce5", "settings": {"html": "<div class=\"elementor-shape elementor-shape-bottom\" data-negative=\"false\">\r\n\t\t\t<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 1000 100\" preserveAspectRatio=\"none\">\r\n\t<path class=\"elementor-shape-fill\" opacity=\"0.33\" d=\"M473,67.3c-203.9,88.3-263.1-34-320.3,0C66,119.1,0,59.7,0,59.7V0h1000v59.7 c0,0-62.1,26.1-94.9,29.3c-32.8,3.3-62.8-12.3-75.8-22.1C806,49.6,745.3,8.7,694.9,4.7S492.4,59,473,67.3z\"></path>\r\n\t<path class=\"elementor-shape-fill\" opacity=\"0.66\" d=\"M734,67.3c-45.5,0-77.2-23.2-129.1-39.1c-28.6-8.7-150.3-10.1-254,39.1 s-91.7-34.4-149.2,0C115.7,118.3,0,39.8,0,39.8V0h1000v36.5c0,0-28.2-18.5-92.1-18.5C810.2,18.1,775.7,67.3,734,67.3z\"></path>\r\n\t<path class=\"elementor-shape-fill\" d=\"M766.1,28.9c-200-57.5-266,65.5-395.1,19.5C242,1.8,242,5.4,184.8,20.6C128,35.8,132.3,44.9,89.9,52.5C28.6,63.7,0,0,0,0 h1000c0,0-9.9,40.9-83.6,48.1S829.6,47,766.1,28.9z\"></path>\r\n</svg>\t\t</div>", "_margin": {"unit": "px", "top": "83", "right": "0", "bottom": "0", "left": "0", "isLinked": false}, "_margin_tablet": {"unit": "px", "top": "25", "right": "0", "bottom": "0", "left": "0", "isLinked": false}, "_margin_mobile": {"unit": "px", "top": "-50", "right": "0", "bottom": "0", "left": "0", "isLinked": false}, "_padding": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": true}, "ae_dynamic_rules": [{"_id": "fe66d56"}]}, "elements": [], "isInner": false, "widgetType": "html", "elType": "widget"}], "isInner": false, "elType": "column"}], "isInner": false, "elType": "section"}, {"id": "d5bd59", "settings": {"gap": "no", "margin": {"unit": "px", "top": "60", "right": 0, "bottom": "60", "left": 0, "isLinked": true}, "padding": {"unit": "px", "top": "10", "right": "10", "bottom": "10", "left": "10", "isLinked": true}, "ae_dynamic_rules": [{"_id": "cd3cffb"}], "slider_delay": "5000"}, "elements": [{"id": "48bc66a5", "settings": {"_column_size": 100, "_inline_size": null, "ae_dynamic_rules": [{"_id": "4553816"}], "slider_delay": "5000"}, "elements": [{"id": "4e7b344a", "settings": {"title": "Overview", "typography_typography": "custom", "typography_font_family": "Nunito", "typography_font_size": {"unit": "px", "size": 40, "sizes": []}, "typography_font_weight": "600", "ae_dynamic_rules": [{"_id": "a75f652"}]}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}, {"id": "640106a4", "settings": {"editor": "<ul><li>We are dedicated to meeting all the business requirements of our customers and thereby improving the reliability, productivity, and efficiency of their assets.</li><li>Our in-house team of experts, with a strong focus on customer satisfaction, provides unmatched services which is a benchmark in the industry.</li><li>We offer services in all domains of a project from Project Management, System Design &amp; Engineering to Systems Implementation &amp; Commissioning.</li></ul>", "text_color": "#000000", "typography_typography": "custom", "typography_font_family": "Nunito", "typography_font_size": {"unit": "px", "size": 18, "sizes": []}, "typography_font_size_mobile": {"unit": "px", "size": 16, "sizes": []}, "typography_font_weight": "400", "_margin_mobile": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "-25", "isLinked": false}, "_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "-33", "isLinked": false}, "ae_dynamic_rules": [{"_id": "54e17a2"}]}, "elements": [], "isInner": false, "widgetType": "text-editor", "elType": "widget"}], "isInner": false, "elType": "column"}], "isInner": false, "elType": "section"}, {"id": "3078fc44", "settings": {"structure": "20", "background_background": "classic", "background_color": "#FFFFFF", "__globals__": {"background_color": ""}, "ae_dynamic_rules": [{"_id": "c2947d5"}], "slider_delay": "5000"}, "elements": [{"id": "ebcd349", "settings": {"_column_size": 50, "_inline_size": null, "border_radius": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": true}, "__globals__": {"background_color": ""}, "_inline_size_tablet": 100, "margin": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": true}, "ae_dynamic_rules": [{"_id": "be5562a"}], "slider_delay": "5000"}, "elements": [{"id": "15666067", "settings": {"image": {"url": "https://azoneus.com/wp-content/uploads/2022/02/d3e43f7b-6ae0-4188-b96d-91d7f2e656a1-1024x682.jpg", "id": "", "source": "url"}, "image_border_radius": {"unit": "px", "top": "5", "right": "5", "bottom": "0", "left": "0", "isLinked": false}, "width": {"unit": "%", "size": 100, "sizes": []}, "space": {"unit": "%", "size": 100, "sizes": []}, "height": {"unit": "px", "size": 353, "sizes": []}, "height_tablet": {"unit": "vh", "size": "", "sizes": []}, "height_mobile": {"unit": "vh", "size": "", "sizes": []}, "object-fit": "cover", "ae_dynamic_rules": [{"_id": "d0583ab"}]}, "elements": [], "isInner": false, "widgetType": "image", "elType": "widget"}, {"id": "15c768a6", "settings": {"background_background": "classic", "margin": {"unit": "px", "top": "-20", "right": 0, "bottom": "0", "left": 0, "isLinked": false}, "padding": {"unit": "px", "top": "10", "right": "0", "bottom": "0", "left": "0", "isLinked": false}, "__globals__": {"background_color": "globals/colors?id=secondary"}, "height_inner": "min-height", "custom_height_inner": {"unit": "px", "size": 63, "sizes": []}, "ae_dynamic_rules": [{"_id": "fecde16"}], "slider_delay": "5000"}, "elements": [{"id": "4e6d2cf7", "settings": {"_column_size": 100, "_inline_size": null, "ae_dynamic_rules": [{"_id": "b10631e"}], "slider_delay": "5000"}, "elements": [{"id": "32b996e2", "settings": {"title": "Project Management", "title_color": "#FFFFFF", "typography_typography": "custom", "typography_font_family": "Nunito", "typography_font_size": {"unit": "px", "size": 22, "sizes": []}, "typography_font_weight": "600", "_padding": {"unit": "px", "top": "0", "right": "0", "bottom": "1", "left": "0", "isLinked": false}, "_background_background": "classic", "__globals__": {"_background_color": "globals/colors?id=secondary"}, "ae_dynamic_rules": [{"_id": "bb00234"}]}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}, {"id": "650c873e", "settings": {"width": {"unit": "%", "size": 3, "sizes": []}, "width_tablet": {"unit": "px", "size": "", "sizes": []}, "width_mobile": {"unit": "px", "size": "", "sizes": []}, "text": "Divider", "color": "#F4A41C", "weight": {"unit": "px", "size": 4, "sizes": []}, "_margin": {"unit": "px", "top": "-30", "right": "0", "bottom": "-5", "left": "0", "isLinked": false}, "ae_dynamic_rules": [{"_id": "ff495f2"}]}, "elements": [], "isInner": false, "widgetType": "divider", "elType": "widget"}], "isInner": true, "elType": "column"}], "isInner": true, "elType": "section"}, {"id": "8123e5e", "settings": {"gap": "no", "height_inner": "min-height", "custom_height_inner": {"unit": "px", "size": 355, "sizes": []}, "background_background": "classic", "border_radius": {"unit": "px", "top": "0", "right": "0", "bottom": "5", "left": "5", "isLinked": false}, "__globals__": {"background_color": "globals/colors?id=primary"}, "custom_height_inner_tablet": {"unit": "px", "size": 184, "sizes": []}, "ae_dynamic_rules": [{"_id": "ace53b7"}], "slider_delay": "5000"}, "elements": [{"id": "a6fea32", "settings": {"_column_size": 100, "_inline_size": null, "ae_dynamic_rules": [{"_id": "42e9afd"}], "slider_delay": "5000"}, "elements": [{"id": "4ae83bc5", "settings": {"editor": "<p>Automation Zone has managers have executed successfully many automation projects kingdom wide.Maintaining project progress in accordance With an agreed schedule, ensuring customer Satisfaction and controlling deliverables versus cost is the main targets of Project Management.In recognition of Automation Zone project management capabilities, King <PERSON> University of Science and Technology produced an appreciation trophy as indicated here with.</p>", "text_color": "#FFFFFF", "typography_typography": "custom", "typography_font_family": "Nunito", "typography_font_size": {"unit": "px", "size": 16, "sizes": []}, "typography_font_weight": "700", "typography_line_height": {"unit": "em", "size": 1.7, "sizes": []}, "typography_letter_spacing": {"unit": "px", "size": -0.3, "sizes": []}, "_padding": {"unit": "px", "top": "10", "right": "10", "bottom": "10", "left": "10", "isLinked": true}, "_background_background": "classic", "__globals__": {"_background_color": "globals/colors?id=primary"}, "ae_dynamic_rules": [{"_id": "55f3983"}]}, "elements": [], "isInner": false, "widgetType": "text-editor", "elType": "widget"}], "isInner": true, "elType": "column"}], "isInner": true, "elType": "section"}], "isInner": false, "elType": "column"}, {"id": "6e64ce81", "settings": {"_column_size": 50, "_inline_size": null, "__globals__": {"background_color": ""}, "_inline_size_tablet": 100, "ae_dynamic_rules": [{"_id": "c09fab6"}], "slider_delay": "5000"}, "elements": [{"id": "3e9ca454", "settings": {"image": {"url": "https://azoneus.com/wp-content/uploads/2022/02/hands-engineer-working-blueprint-construction-concept-engineering-tools-vintage-tone-retro-filter-effect-soft-focus-selective-focus_1418-477.jpg", "id": "", "source": "url", "alt": ""}, "image_border_radius": {"unit": "px", "top": "5", "right": "5", "bottom": "0", "left": "0", "isLinked": false}, "width": {"unit": "%", "size": 100, "sizes": []}, "space": {"unit": "%", "size": 100, "sizes": []}, "height": {"unit": "px", "size": 353, "sizes": []}, "height_tablet": {"unit": "vh", "size": "", "sizes": []}, "height_mobile": {"unit": "vh", "size": "", "sizes": []}, "object-fit": "cover", "ae_dynamic_rules": [{"_id": "64d452e"}]}, "elements": [], "isInner": false, "widgetType": "image", "elType": "widget"}, {"id": "15be45ef", "settings": {"background_background": "classic", "margin": {"unit": "px", "top": "-20", "right": 0, "bottom": "0", "left": 0, "isLinked": false}, "padding": {"unit": "px", "top": "10", "right": "0", "bottom": "0", "left": "0", "isLinked": false}, "__globals__": {"background_color": "globals/colors?id=secondary"}, "height_inner": "min-height", "custom_height_inner": {"unit": "px", "size": 63, "sizes": []}, "ae_dynamic_rules": [{"_id": "05dce4e"}], "slider_delay": "5000"}, "elements": [{"id": "3d78e4c4", "settings": {"_column_size": 100, "_inline_size": null, "ae_dynamic_rules": [{"_id": "15a6800"}], "slider_delay": "5000"}, "elements": [{"id": "65434aa8", "settings": {"title": "System Engineering", "title_color": "#FFFFFF", "typography_typography": "custom", "typography_font_family": "Nunito", "typography_font_size": {"unit": "px", "size": 22, "sizes": []}, "typography_font_weight": "600", "_padding": {"unit": "px", "top": "0", "right": "0", "bottom": "1", "left": "0", "isLinked": false}, "_background_background": "classic", "__globals__": {"_background_color": "globals/colors?id=secondary"}, "ae_dynamic_rules": [{"_id": "7067d15"}]}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}, {"id": "3fdf7ff0", "settings": {"width": {"unit": "%", "size": 3, "sizes": []}, "width_tablet": {"unit": "px", "size": "", "sizes": []}, "width_mobile": {"unit": "px", "size": "", "sizes": []}, "text": "Divider", "color": "#F4A41C", "weight": {"unit": "px", "size": 4, "sizes": []}, "_margin": {"unit": "px", "top": "-30", "right": "0", "bottom": "-5", "left": "0", "isLinked": false}, "ae_dynamic_rules": [{"_id": "628b42e"}]}, "elements": [], "isInner": false, "widgetType": "divider", "elType": "widget"}], "isInner": true, "elType": "column"}], "isInner": true, "elType": "section"}, {"id": "3c7e5195", "settings": {"gap": "no", "height_inner": "min-height", "custom_height_inner": {"unit": "px", "size": 355, "sizes": []}, "background_background": "classic", "border_radius": {"unit": "px", "top": "0", "right": "0", "bottom": "5", "left": "5", "isLinked": false}, "__globals__": {"background_color": "globals/colors?id=primary"}, "custom_height_inner_tablet": {"unit": "px", "size": 211, "sizes": []}, "ae_dynamic_rules": [{"_id": "a869180"}], "slider_delay": "5000"}, "elements": [{"id": "7c0df105", "settings": {"_column_size": 100, "_inline_size": null, "ae_dynamic_rules": [{"_id": "def3b55"}], "slider_delay": "5000"}, "elements": [{"id": "53db68fa", "settings": {"editor": "<p>Automation Zone offers complete systems hardware and software design capabilities with arrangements with major vendors and systems suppliers in the filed of automation and buildings services.Design would include production of logic and schematic diagrams, Input/output summary, sequence of operations, cause and effect matrix, wiring diagrams, shop drawings, graphics design, panels design layout drawings.Design offering by Automation Zone also includes complete production of as built documentation and maintenance manuals.</p>", "text_color": "#FFFFFF", "typography_typography": "custom", "typography_font_family": "Nunito", "typography_font_size": {"unit": "px", "size": 16, "sizes": []}, "typography_font_weight": "700", "typography_line_height": {"unit": "em", "size": 1.7, "sizes": []}, "typography_letter_spacing": {"unit": "px", "size": -0.3, "sizes": []}, "_padding": {"unit": "px", "top": "10", "right": "10", "bottom": "10", "left": "10", "isLinked": false}, "_background_background": "classic", "__globals__": {"_background_color": "globals/colors?id=primary"}, "ae_dynamic_rules": [{"_id": "75fe82c"}]}, "elements": [], "isInner": false, "widgetType": "text-editor", "elType": "widget"}], "isInner": true, "elType": "column"}], "isInner": true, "elType": "section"}], "isInner": false, "elType": "column"}], "isInner": false, "elType": "section"}, {"id": "41d78adc", "settings": {"structure": "20", "background_background": "classic", "background_color": "#FFFFFF", "__globals__": {"background_color": ""}, "padding": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": false}, "ae_dynamic_rules": [{"_id": "756559a"}], "slider_delay": "5000"}, "elements": [{"id": "306321cf", "settings": {"_column_size": 50, "_inline_size": null, "_inline_size_tablet": 100, "ae_dynamic_rules": [{"_id": "5090445"}], "slider_delay": "5000"}, "elements": [{"id": "51c65c22", "settings": {"image": {"url": "https://azoneus.com/wp-content/uploads/2022/02/working-code-768x513.jpg", "id": "", "source": "url", "alt": ""}, "image_border_radius": {"unit": "px", "top": "5", "right": "5", "bottom": "0", "left": "0", "isLinked": false}, "width": {"unit": "%", "size": 100, "sizes": []}, "space": {"unit": "%", "size": 100, "sizes": []}, "height": {"unit": "px", "size": 353, "sizes": []}, "height_tablet": {"unit": "vh", "size": "", "sizes": []}, "height_mobile": {"unit": "vh", "size": "", "sizes": []}, "object-fit": "cover", "ae_dynamic_rules": [{"_id": "730c5ce"}]}, "elements": [], "isInner": false, "widgetType": "image", "elType": "widget"}, {"id": "a2019bf", "settings": {"background_background": "classic", "margin": {"unit": "px", "top": "-20", "right": 0, "bottom": "0", "left": 0, "isLinked": false}, "padding": {"unit": "px", "top": "10", "right": "0", "bottom": "0", "left": "0", "isLinked": false}, "__globals__": {"background_color": "globals/colors?id=secondary"}, "height_inner": "min-height", "custom_height_inner": {"unit": "px", "size": 63, "sizes": []}, "ae_dynamic_rules": [{"_id": "57035ad"}], "slider_delay": "5000"}, "elements": [{"id": "23b7c8d3", "settings": {"_column_size": 100, "_inline_size": null, "ae_dynamic_rules": [{"_id": "d79971f"}], "slider_delay": "5000"}, "elements": [{"id": "49f9fa9e", "settings": {"title": "Systems Programming & Commissioning", "title_color": "#FFFFFF", "typography_typography": "custom", "typography_font_family": "Nunito", "typography_font_size": {"unit": "px", "size": 22, "sizes": []}, "typography_font_weight": "600", "_padding": {"unit": "px", "top": "0", "right": "0", "bottom": "1", "left": "0", "isLinked": false}, "_background_background": "classic", "__globals__": {"_background_color": "globals/colors?id=secondary"}, "ae_dynamic_rules": [{"_id": "c77c5ae"}]}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}, {"id": "6ea89337", "settings": {"width": {"unit": "%", "size": 3, "sizes": []}, "width_tablet": {"unit": "px", "size": "", "sizes": []}, "width_mobile": {"unit": "px", "size": "", "sizes": []}, "text": "Divider", "color": "#F4A41C", "weight": {"unit": "px", "size": 4, "sizes": []}, "_margin": {"unit": "px", "top": "-30", "right": "0", "bottom": "-5", "left": "0", "isLinked": false}, "ae_dynamic_rules": [{"_id": "ed<PERSON><PERSON><PERSON>"}]}, "elements": [], "isInner": false, "widgetType": "divider", "elType": "widget"}], "isInner": true, "elType": "column"}], "isInner": true, "elType": "section"}, {"id": "3bf077d", "settings": {"gap": "no", "height_inner": "min-height", "custom_height_inner": {"unit": "px", "size": 375, "sizes": []}, "background_background": "classic", "border_radius": {"unit": "px", "top": "0", "right": "0", "bottom": "5", "left": "5", "isLinked": false}, "__globals__": {"background_color": "globals/colors?id=primary"}, "custom_height_inner_tablet": {"unit": "px", "size": 265, "sizes": []}, "ae_dynamic_rules": [{"_id": "5f68379"}], "slider_delay": "5000"}, "elements": [{"id": "3cde8941", "settings": {"_column_size": 100, "_inline_size": null, "ae_dynamic_rules": [{"_id": "2249b98"}], "slider_delay": "5000"}, "elements": [{"id": "61363000", "settings": {"editor": "<p>Software programming is the ultimate jewel of automation services. The program is the hidden element that defines the system performance and hence makes or breaks the quality of the whole automation process. Automation zone, for the systems it offers, ensures to have programmers certified and well experienced to perform this delicate and yet critical service it offers in many automation solutions. Automation Zone portfolio includes complete commissioning services for a variety of systems in the field of BMS, Fire Alarm, and Security. Commissioning services include turn-key responsibility from the production of method statements to running the systems tests from dry test to the final handing over of the systems to the clients.</p>", "text_color": "#FFFFFF", "typography_typography": "custom", "typography_font_family": "Nunito", "typography_font_size": {"unit": "px", "size": 16, "sizes": []}, "typography_font_weight": "700", "typography_line_height": {"unit": "em", "size": 1.7, "sizes": []}, "typography_letter_spacing": {"unit": "px", "size": -0.3, "sizes": []}, "_padding": {"unit": "px", "top": "10", "right": "10", "bottom": "10", "left": "10", "isLinked": false}, "_background_background": "classic", "__globals__": {"_background_color": "globals/colors?id=primary"}, "_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": false}, "ae_dynamic_rules": [{"_id": "b560d66"}]}, "elements": [], "isInner": false, "widgetType": "text-editor", "elType": "widget"}], "isInner": true, "elType": "column"}], "isInner": true, "elType": "section"}], "isInner": false, "elType": "column"}, {"id": "22bd5484", "settings": {"_column_size": 50, "_inline_size": null, "_inline_size_tablet": 100, "ae_dynamic_rules": [{"_id": "2de7546"}], "slider_delay": "5000"}, "elements": [{"id": "6999d7b5", "settings": {"image": {"url": "https://azoneus.com/wp-content/uploads/2022/02/paper-box-packaging-delivery-concept-1024x683.jpg", "id": "", "source": "url", "alt": ""}, "image_border_radius": {"unit": "px", "top": "5", "right": "5", "bottom": "0", "left": "0", "isLinked": false}, "width": {"unit": "%", "size": 100, "sizes": []}, "space": {"unit": "%", "size": 100, "sizes": []}, "height": {"unit": "px", "size": 353, "sizes": []}, "height_tablet": {"unit": "vh", "size": "", "sizes": []}, "height_mobile": {"unit": "vh", "size": "", "sizes": []}, "object-fit": "cover", "ae_dynamic_rules": [{"_id": "b182137"}]}, "elements": [], "isInner": false, "widgetType": "image", "elType": "widget"}, {"id": "4321ce08", "settings": {"background_background": "classic", "margin": {"unit": "px", "top": "-20", "right": 0, "bottom": "0", "left": 0, "isLinked": false}, "padding": {"unit": "px", "top": "10", "right": "0", "bottom": "0", "left": "0", "isLinked": false}, "__globals__": {"background_color": "globals/colors?id=secondary"}, "height_inner": "min-height", "custom_height_inner": {"unit": "px", "size": 63, "sizes": []}, "ae_dynamic_rules": [{"_id": "89728d1"}], "slider_delay": "5000"}, "elements": [{"id": "c2f7b1d", "settings": {"_column_size": 100, "_inline_size": null, "ae_dynamic_rules": [{"_id": "8a090ef"}], "slider_delay": "5000"}, "elements": [{"id": "314aa231", "settings": {"title": "System Handover", "title_color": "#FFFFFF", "typography_typography": "custom", "typography_font_family": "Nunito", "typography_font_size": {"unit": "px", "size": 22, "sizes": []}, "typography_font_weight": "600", "_padding": {"unit": "px", "top": "0", "right": "0", "bottom": "1", "left": "0", "isLinked": false}, "_background_background": "classic", "__globals__": {"_background_color": "globals/colors?id=secondary"}, "ae_dynamic_rules": [{"_id": "7878bcb"}]}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}, {"id": "aff5b81", "settings": {"width": {"unit": "%", "size": 3, "sizes": []}, "width_tablet": {"unit": "px", "size": "", "sizes": []}, "width_mobile": {"unit": "px", "size": "", "sizes": []}, "text": "Divider", "color": "#F4A41C", "weight": {"unit": "px", "size": 4, "sizes": []}, "_margin": {"unit": "px", "top": "-30", "right": "0", "bottom": "-5", "left": "0", "isLinked": false}, "ae_dynamic_rules": [{"_id": "8cff3d0"}]}, "elements": [], "isInner": false, "widgetType": "divider", "elType": "widget"}], "isInner": true, "elType": "column"}], "isInner": true, "elType": "section"}, {"id": "4f43cb12", "settings": {"gap": "no", "height_inner": "min-height", "custom_height_inner": {"unit": "px", "size": 375, "sizes": []}, "background_background": "classic", "border_radius": {"unit": "px", "top": "0", "right": "0", "bottom": "5", "left": "5", "isLinked": false}, "__globals__": {"background_color": "globals/colors?id=primary"}, "custom_height_inner_tablet": {"unit": "px", "size": 157, "sizes": []}, "ae_dynamic_rules": [{"_id": "8b59621"}], "slider_delay": "5000"}, "elements": [{"id": "51ff68b5", "settings": {"_column_size": 100, "_inline_size": null, "ae_dynamic_rules": [{"_id": "9285861"}], "slider_delay": "5000"}, "elements": [{"id": "741a4e02", "settings": {"editor": "<p>All phases of a project are important to the success of it, but the project handover phase plays a vital role in it. Automation Zone provides support for the contractor for the successful handover of the project. This includes support in Testing &amp; Commissioning, Exception List closure, Handover Documents etc.</p>", "text_color": "#FFFFFF", "typography_typography": "custom", "typography_font_family": "Nunito", "typography_font_size": {"unit": "px", "size": 16, "sizes": []}, "typography_font_weight": "700", "typography_line_height": {"unit": "em", "size": 1.7, "sizes": []}, "typography_letter_spacing": {"unit": "px", "size": -0.3, "sizes": []}, "_padding": {"unit": "px", "top": "10", "right": "10", "bottom": "10", "left": "10", "isLinked": false}, "_background_background": "classic", "__globals__": {"_background_color": "globals/colors?id=primary"}, "_element_id": "services-text", "ae_dynamic_rules": [{"_id": "140cc42"}]}, "elements": [], "isInner": false, "widgetType": "text-editor", "elType": "widget"}], "isInner": true, "elType": "column"}], "isInner": true, "elType": "section"}], "isInner": false, "elType": "column"}], "isInner": false, "elType": "section"}, {"id": "5b19cad4", "settings": {"structure": "20", "background_background": "classic", "background_color": "#F2F5F7", "margin": {"unit": "px", "top": "30", "right": 0, "bottom": "0", "left": 0, "isLinked": false}, "padding": {"unit": "px", "top": "30", "right": "0", "bottom": "30", "left": "0", "isLinked": false}, "ae_dynamic_rules": [{"_id": "5906e80"}], "slider_delay": "5000"}, "elements": [{"id": "3277d068", "settings": {"_column_size": 50, "_inline_size": null, "ae_dynamic_rules": [{"_id": "7607f52"}], "slider_delay": "5000"}, "elements": [{"id": "15c30e23", "settings": {"title": "Contact us", "typography_typography": "custom", "typography_font_family": "Nunito", "typography_font_size": {"unit": "px", "size": 40, "sizes": []}, "typography_font_weight": "600", "_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": false}, "ae_dynamic_rules": [{"_id": "6a7aee6"}]}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}, {"id": "3f081ea", "settings": {"shortcode": "[formidable id=2]", "ae_dynamic_rules": [{"_id": "8945032"}]}, "elements": [], "isInner": false, "widgetType": "shortcode", "elType": "widget"}], "isInner": false, "elType": "column"}, {"id": "52e4d05e", "settings": {"_column_size": 50, "_inline_size": null, "ae_dynamic_rules": [{"_id": "ab74ae7"}], "slider_delay": "5000"}, "elements": [{"id": "1307f857", "settings": {"title": "Please submit your query on the form aside or you can find our contact information on our Contact page if you would like to discuss your project requirements with our consultants.", "title_color": "#000000", "typography_typography": "custom", "typography_font_family": "Nunito", "typography_font_size": {"unit": "px", "size": 20, "sizes": []}, "typography_font_size_tablet": {"unit": "px", "size": 18, "sizes": []}, "typography_font_size_mobile": {"unit": "px", "size": 16, "sizes": []}, "typography_font_weight": "400", "_margin": {"unit": "px", "top": "30", "right": "0", "bottom": "0", "left": "0", "isLinked": false}, "ae_dynamic_rules": [{"_id": "6278cb3"}]}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}, {"id": "7b335e6e", "settings": {"image": {"url": "https://azoneus.com/wp-content/uploads/2022/02/contact-register-feedback-support-help-concept_53876-124243.jpg", "id": "", "source": "url"}, "space": {"unit": "%", "size": 100, "sizes": []}, "image_border_radius": {"unit": "px", "top": "10", "right": "10", "bottom": "10", "left": "10", "isLinked": true}, "ae_dynamic_rules": [{"_id": "f3380bc"}]}, "elements": [], "isInner": false, "widgetType": "image", "elType": "widget"}], "isInner": false, "elType": "column"}], "isInner": false, "elType": "section"}], "page_settings": [], "version": "0.4", "title": "Azoneus_services", "type": "page"}