{"content": [{"id": "2fda0f82", "settings": {"layout": "full_width", "gap": "no", "background_background": "classic", "background_color": "#EAEAEA", "padding": {"unit": "px", "top": "30", "right": "0", "bottom": "0", "left": "0", "isLinked": false}, "ae_dynamic_rules": [{"_id": "08f1e91"}], "slider_delay": "5000"}, "elements": [{"id": "4194425d", "settings": {"_column_size": 100, "_inline_size": null, "ae_dynamic_rules": [{"_id": "95a3b2c"}], "slider_delay": "5000"}, "elements": [{"id": "496b0765", "settings": {"ae_dynamic_rules": [{"_id": "e1386a7"}], "slider_delay": "5000"}, "elements": [{"id": "47c48f8b", "settings": {"_column_size": 100, "_inline_size": null, "ae_dynamic_rules": [{"_id": "1218f0f"}], "slider_delay": "5000"}, "elements": [{"id": "706d2a82", "settings": {"title": "The King <PERSON> University of Science and Technology (KAUST)", "typography_typography": "custom", "typography_font_family": "Nunito", "typography_font_size": {"unit": "px", "size": 40, "sizes": []}, "typography_font_size_mobile": {"unit": "px", "size": 26, "sizes": []}, "typography_font_weight": "600", "_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "-35", "left": "0", "isLinked": false}, "ae_dynamic_rules": [{"_id": "a243487"}]}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}, {"id": "1b8fc348", "settings": {"width": {"unit": "px", "size": 25, "sizes": []}, "width_tablet": {"unit": "px", "size": "", "sizes": []}, "width_mobile": {"unit": "px", "size": 20, "sizes": []}, "text": "Divider", "color": "#F4A41C", "weight": {"unit": "px", "size": 4, "sizes": []}, "gap": {"unit": "px", "size": 3, "sizes": []}, "_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": false}, "_margin_mobile": {"unit": "px", "top": "0", "right": "0", "bottom": "-7", "left": "0", "isLinked": false}, "_padding": {"unit": "px", "top": "15", "right": "0", "bottom": "15", "left": "0", "isLinked": false}, "ae_dynamic_rules": [{"_id": "cdd8b03"}]}, "elements": [], "isInner": false, "widgetType": "divider", "elType": "widget"}], "isInner": true, "elType": "column"}], "isInner": true, "elType": "section"}, {"id": "ed304ce", "settings": {"html": "<div class=\"elementor-shape elementor-shape-bottom\" data-negative=\"false\">\r\n\t\t\t<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 1000 100\" preserveAspectRatio=\"none\">\r\n\t<path class=\"elementor-shape-fill\" opacity=\"0.33\" d=\"M473,67.3c-203.9,88.3-263.1-34-320.3,0C66,119.1,0,59.7,0,59.7V0h1000v59.7 c0,0-62.1,26.1-94.9,29.3c-32.8,3.3-62.8-12.3-75.8-22.1C806,49.6,745.3,8.7,694.9,4.7S492.4,59,473,67.3z\"></path>\r\n\t<path class=\"elementor-shape-fill\" opacity=\"0.66\" d=\"M734,67.3c-45.5,0-77.2-23.2-129.1-39.1c-28.6-8.7-150.3-10.1-254,39.1 s-91.7-34.4-149.2,0C115.7,118.3,0,39.8,0,39.8V0h1000v36.5c0,0-28.2-18.5-92.1-18.5C810.2,18.1,775.7,67.3,734,67.3z\"></path>\r\n\t<path class=\"elementor-shape-fill\" d=\"M766.1,28.9c-200-57.5-266,65.5-395.1,19.5C242,1.8,242,5.4,184.8,20.6C128,35.8,132.3,44.9,89.9,52.5C28.6,63.7,0,0,0,0 h1000c0,0-9.9,40.9-83.6,48.1S829.6,47,766.1,28.9z\"></path>\r\n</svg>\t\t</div>", "_margin": {"unit": "px", "top": "83", "right": "0", "bottom": "0", "left": "0", "isLinked": false}, "_margin_tablet": {"unit": "px", "top": "25", "right": "0", "bottom": "0", "left": "0", "isLinked": false}, "_margin_mobile": {"unit": "px", "top": "-50", "right": "0", "bottom": "0", "left": "0", "isLinked": false}, "_padding": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": true}, "ae_dynamic_rules": [{"_id": "106a1fc"}]}, "elements": [], "isInner": false, "widgetType": "html", "elType": "widget"}], "isInner": false, "elType": "column"}], "isInner": false, "elType": "section"}, {"id": "1af5d2d0", "settings": {"ae_dynamic_rules": [{"_id": "d87bfa8"}], "slider_delay": "5000"}, "elements": [{"id": "2efdd9d8", "settings": {"_column_size": 100, "_inline_size": null, "ae_dynamic_rules": [{"_id": "bcbf492"}], "slider_delay": "5000"}, "elements": [{"id": "c6d942", "settings": {"image": {"url": "http://localhost/azoneus/wp-content/uploads/2022/09/kaust-300x171-1.jpg", "id": 609, "alt": "ka<PERSON>", "source": "library"}, "ae_dynamic_rules": [{"_id": "21908f7"}]}, "elements": [], "isInner": false, "widgetType": "image", "elType": "widget"}], "isInner": false, "elType": "column"}], "isInner": false, "elType": "section"}, {"id": "6f3648c0", "settings": {"ae_dynamic_rules": [{"_id": "04aaeb7"}], "slider_delay": "5000"}, "elements": [{"id": "227d7b5", "settings": {"_column_size": 100, "_inline_size": null, "ae_dynamic_rules": [{"_id": "855715d"}], "slider_delay": "5000"}, "elements": [{"id": "4a98474a", "settings": {"editor": "<p>The King <PERSON> University of Science and Technology (KAUST) is located on the coast of the Red Sea, approximately 80 KM north of Jeddah in Saudi Arabia. It is a globally renowned graduate research university that makes significant contributions to scientific and technological advancement, playing a crucial role in the development of the Kingdom of Saudi Arabia, the region, and the world. The construction of the university is an ongoing process with a goal towards sustainable development.</p>", "text_color": "#3A3A3A", "ae_dynamic_rules": [{"_id": "57ee8a6"}]}, "elements": [], "isInner": false, "widgetType": "text-editor", "elType": "widget"}, {"id": "5c7abb66", "settings": {"editor": "<h4 class=\"title_hd text_center blue\" style=\"color: #365f91;margin-bottom:20px\">Our Projects</h4>\n<ul style=\"margin-left: 20px;\">\n \t<li>Integrated Campus Automation System (ICAS)</li>\n \t<li>Integrated Fire Alarm System (IFAS)</li>\n \t<li>KAUST School</li>\n \t<li>Research Park Innovation Cluster (RPIC)</li>\n</ul>", "text_color": "#3A3A3A", "_margin": {"unit": "px", "top": "-20", "right": "0", "bottom": "0", "left": "0", "isLinked": false}, "ae_dynamic_rules": [{"_id": "3a15536"}]}, "elements": [], "isInner": false, "widgetType": "text-editor", "elType": "widget"}], "isInner": false, "elType": "column"}], "isInner": false, "elType": "section"}, {"id": "4f62ae1d", "settings": {"ae_dynamic_rules": [{"_id": "e94eb7a"}], "slider_delay": "5000"}, "elements": [{"id": "7bb55e54", "settings": {"_column_size": 100, "_inline_size": null, "ae_dynamic_rules": [{"_id": "e069407"}], "slider_delay": "5000"}, "elements": [{"id": "55470b5c", "settings": {"title": "Integrated Campus Automation System (ICAS)", "typography_typography": "custom", "typography_font_family": "Nunito", "typography_font_size": {"unit": "px", "size": 26, "sizes": []}, "typography_font_weight": "600", "__globals__": {"title_color": "globals/colors?id=primary"}, "ae_dynamic_rules": [{"_id": "04d9bd0"}]}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}, {"id": "4c1a2aaa", "settings": {"carousel": [{"id": 817, "url": "http://localhost/azoneus/wp-content/uploads/2022/10/ICAS-1-1024x827-1.jpg"}, {"id": 818, "url": "http://localhost/azoneus/wp-content/uploads/2022/10/ICAS-3-1024x827-1.jpg"}, {"id": 819, "url": "http://localhost/azoneus/wp-content/uploads/2022/10/ICAS-2-1024x827-1.jpg"}], "thumbnail_size": "full", "slides_to_show": "1", "ae_dynamic_rules": [{"_id": "ddb241b"}]}, "elements": [], "isInner": false, "widgetType": "image-carousel", "elType": "widget"}], "isInner": false, "elType": "column"}], "isInner": false, "elType": "section"}, {"id": "48364840", "settings": {"margin": {"unit": "px", "top": "0", "right": 0, "bottom": "24", "left": 0, "isLinked": false}, "ae_dynamic_rules": [{"_id": "fdacfce"}], "slider_delay": "5000"}, "elements": [{"id": "c50db4d", "settings": {"_column_size": 100, "_inline_size": null, "ae_dynamic_rules": [{"_id": "4fc090d"}], "slider_delay": "5000"}, "elements": [{"id": "21dddc12", "settings": {"selected_icon": {"value": "", "library": ""}, "title_text": "About This Project", "description_text": "The King <PERSON> University of Science and Technology (KAUST) is located on the coast of the Red Sea,approximately 80 KM north of Jeddah in SaudiArabia. It is a globally renowned graduate research university that makes significant contributions to scientific and technological advancement, playing a crucial role in the development of the Kingdom of Saudi Arabia, the region and the world. The construction of the university is an ongoing process with a goal towards sustainable development.\n<br><br>\nThe Campus comprises 19 buildings utilizing a common foundation for over 500K sqm of laboratories, classroom, administration and support facilities, including Asia’s largest super-computer – Shaheen. The entire facility is managed by a single Integrated Campus Automation System (ICAS) which monitors and/or controls about 375,000 devices from over 20 disparate sub-systems, i.e. lighting, HVAC, Access Controls, Laboratory Controls, Fire Detection and Suppression, Solar Hot-water, photovoltaics, Solar Cooling Towers, etc.", "title_size": "h2", "text_align": "left", "title_bottom_space": {"unit": "px", "size": 10, "sizes": []}, "title_typography_typography": "custom", "title_typography_font_family": "Nunito", "title_typography_font_size": {"unit": "rem", "size": 1.33, "sizes": []}, "title_typography_font_size_tablet": {"unit": "rem", "size": "", "sizes": []}, "title_typography_font_size_mobile": {"unit": "rem", "size": "", "sizes": []}, "title_typography_font_weight": "600", "description_color": "#000000", "description_typography_typography": "custom", "description_typography_font_family": "Nunito", "description_typography_font_weight": "400", "__globals__": {"title_color": "globals/colors?id=primary"}, "ae_dynamic_rules": [{"_id": "e04281d"}]}, "elements": [], "isInner": false, "widgetType": "icon-box", "elType": "widget"}, {"id": "4f0d34fd", "settings": {"selected_icon": {"value": "", "library": ""}, "title_text": "<PERSON><PERSON>", "description_text": "Supply, Installation and Commissioning of Integrated Campus Automation System upgrade.", "title_size": "h2", "text_align": "left", "title_bottom_space": {"unit": "px", "size": 10, "sizes": []}, "title_typography_typography": "custom", "title_typography_font_family": "Nunito", "title_typography_font_size": {"unit": "rem", "size": 1.33, "sizes": []}, "title_typography_font_size_tablet": {"unit": "rem", "size": "", "sizes": []}, "title_typography_font_size_mobile": {"unit": "rem", "size": "", "sizes": []}, "title_typography_font_weight": "600", "description_color": "#000000", "description_typography_typography": "custom", "description_typography_font_family": "Nunito", "description_typography_font_weight": "400", "__globals__": {"title_color": "globals/colors?id=primary"}, "ae_dynamic_rules": [{"_id": "c161c99"}]}, "elements": [], "isInner": false, "widgetType": "icon-box", "elType": "widget"}], "isInner": false, "elType": "column"}], "isInner": false, "elType": "section"}, {"id": "251a915c", "settings": {"ae_dynamic_rules": [{"_id": "31f4aae"}], "slider_delay": "5000"}, "elements": [{"id": "1c8b9609", "settings": {"_column_size": 100, "_inline_size": null, "ae_dynamic_rules": [{"_id": "8d43693"}], "slider_delay": "5000"}, "elements": [{"id": "3578bd7c", "settings": {"title": "Integrated Fire Alarm System (IFAS)", "typography_typography": "custom", "typography_font_family": "Nunito", "typography_font_size": {"unit": "px", "size": 26, "sizes": []}, "typography_font_weight": "600", "__globals__": {"title_color": "globals/colors?id=primary"}, "ae_dynamic_rules": [{"_id": "dbc80cd"}]}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}, {"id": "70939f3b", "settings": {"carousel": [{"id": 820, "url": "http://localhost/azoneus/wp-content/uploads/2022/10/IFAS-2.jpg"}, {"id": 821, "url": "http://localhost/azoneus/wp-content/uploads/2022/10/IFAS-1.jpg"}], "thumbnail_size": "full", "slides_to_show": "1", "ae_dynamic_rules": [{"_id": "cd7375d"}]}, "elements": [], "isInner": false, "widgetType": "image-carousel", "elType": "widget"}], "isInner": false, "elType": "column"}], "isInner": false, "elType": "section"}, {"id": "2790bf08", "settings": {"margin": {"unit": "px", "top": "0", "right": 0, "bottom": "24", "left": 0, "isLinked": false}, "ae_dynamic_rules": [{"_id": "6f7326a"}], "slider_delay": "5000"}, "elements": [{"id": "4cc2f3a3", "settings": {"_column_size": 100, "_inline_size": null, "ae_dynamic_rules": [{"_id": "519d2a4"}], "slider_delay": "5000"}, "elements": [{"id": "3b4a43e6", "settings": {"selected_icon": {"value": "", "library": ""}, "title_text": "About This Project", "description_text": "The King <PERSON> University of Science and Technology (KAUST) is located on the coast of the Red Sea,approximately 80 KM north of Jeddah in SaudiArabia.\n<br><br>\nIt is a globally renowned graduate research university that makes significant contributions to scientific and technological advancement, playing a crucial role in the development of the Kingdom of Saudi Arabia, the region and the world. The construction of the university is an ongoing process with a goal towards sustainable development.\n<br><br>\nResearch Park Innovation Cluster (RPIC) is a part of Research Parks in KAUST and was founded with industry collaboration in mind. RPIC work with companies, private sector entities, nonprofits and government organizations to transfer knowledge and technologies created at the University and achieve public benefit and economic development.", "title_size": "h2", "text_align": "left", "title_bottom_space": {"unit": "px", "size": 10, "sizes": []}, "title_typography_typography": "custom", "title_typography_font_family": "Nunito", "title_typography_font_size": {"unit": "rem", "size": 1.33, "sizes": []}, "title_typography_font_size_tablet": {"unit": "rem", "size": "", "sizes": []}, "title_typography_font_size_mobile": {"unit": "rem", "size": "", "sizes": []}, "title_typography_font_weight": "600", "description_color": "#000000", "description_typography_typography": "custom", "description_typography_font_family": "Nunito", "description_typography_font_weight": "400", "__globals__": {"title_color": "globals/colors?id=primary"}, "ae_dynamic_rules": [{"_id": "1881654"}]}, "elements": [], "isInner": false, "widgetType": "icon-box", "elType": "widget"}, {"id": "850f958", "settings": {"selected_icon": {"value": "", "library": ""}, "title_text": "<PERSON><PERSON>", "description_text": "Supply, Installation and Commissioning of Building Management System.\n<br><br>\nSupply, Installation and Commissioning of Integration of Building Management System to Integrated Campus Automation System (ICAS)", "title_size": "h2", "text_align": "left", "title_bottom_space": {"unit": "px", "size": 10, "sizes": []}, "title_typography_typography": "custom", "title_typography_font_family": "Nunito", "title_typography_font_size": {"unit": "rem", "size": 1.33, "sizes": []}, "title_typography_font_size_tablet": {"unit": "rem", "size": "", "sizes": []}, "title_typography_font_size_mobile": {"unit": "rem", "size": "", "sizes": []}, "title_typography_font_weight": "600", "description_color": "#000000", "description_typography_typography": "custom", "description_typography_font_family": "Nunito", "description_typography_font_weight": "400", "__globals__": {"title_color": "globals/colors?id=primary"}, "ae_dynamic_rules": [{"_id": "6a199c8"}]}, "elements": [], "isInner": false, "widgetType": "icon-box", "elType": "widget"}], "isInner": false, "elType": "column"}], "isInner": false, "elType": "section"}, {"id": "5ed083e7", "settings": {"ae_dynamic_rules": [{"_id": "5783608"}], "slider_delay": "5000"}, "elements": [{"id": "86dc5c5", "settings": {"_column_size": 100, "_inline_size": null, "ae_dynamic_rules": [{"_id": "bfb59ea"}], "slider_delay": "5000"}, "elements": [{"id": "711d94ce", "settings": {"title": "KAUST School", "typography_typography": "custom", "typography_font_family": "Nunito", "typography_font_size": {"unit": "px", "size": 26, "sizes": []}, "typography_font_weight": "600", "__globals__": {"title_color": "globals/colors?id=primary"}, "ae_dynamic_rules": [{"_id": "437bf0f"}]}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}, {"id": "77002ec8", "settings": {"image": {"url": "https://azoneus.com/wp-content/uploads/2022/03/school1-1024x864.jpg", "id": "", "source": "url"}, "ae_dynamic_rules": [{"_id": "ac4d410"}]}, "elements": [], "isInner": false, "widgetType": "image", "elType": "widget"}], "isInner": false, "elType": "column"}], "isInner": false, "elType": "section"}, {"id": "2ed4c711", "settings": {"margin": {"unit": "px", "top": "0", "right": 0, "bottom": "24", "left": 0, "isLinked": false}, "ae_dynamic_rules": [{"_id": "50bcb93"}], "slider_delay": "5000"}, "elements": [{"id": "26efdd39", "settings": {"_column_size": 100, "_inline_size": null, "ae_dynamic_rules": [{"_id": "b2c92bb"}], "slider_delay": "5000"}, "elements": [{"id": "3ec928e0", "settings": {"selected_icon": {"value": "", "library": ""}, "title_text": "About This Project", "description_text": "The King <PERSON> University of Science and Technology (KAUST) is located on the coast of the Red Sea,approximately 80 KM north of Jeddah in SaudiArabia. It is a globally renowned graduate research university that makes significant contributions to scientific and technological advancement, playing a crucial role in the development of the Kingdom of Saudi Arabia, the region and the world.\n<br><br>\nThe entire campus, including the 19 massive academic buildings, hundreds of town center buildings and over a thousand student and faculty buildings housing over 12,000 people on this 3,200 acre site. It is monitored via a system known as IFAS (Integrated Fire Alarm System).", "title_size": "h2", "text_align": "left", "title_bottom_space": {"unit": "px", "size": 10, "sizes": []}, "title_typography_typography": "custom", "title_typography_font_family": "Nunito", "title_typography_font_size": {"unit": "rem", "size": 1.33, "sizes": []}, "title_typography_font_size_tablet": {"unit": "rem", "size": "", "sizes": []}, "title_typography_font_size_mobile": {"unit": "rem", "size": "", "sizes": []}, "title_typography_font_weight": "600", "description_color": "#000000", "description_typography_typography": "custom", "description_typography_font_family": "Nunito", "description_typography_font_weight": "400", "__globals__": {"title_color": "globals/colors?id=primary"}, "ae_dynamic_rules": [{"_id": "118441f"}]}, "elements": [], "isInner": false, "widgetType": "icon-box", "elType": "widget"}, {"id": "6f2d6d96", "settings": {"selected_icon": {"value": "", "library": ""}, "title_text": "<PERSON><PERSON>", "description_text": "Supply, Installation and Commissioning of Integrated Fire Alarm System.", "title_size": "h2", "text_align": "left", "title_bottom_space": {"unit": "px", "size": 10, "sizes": []}, "title_typography_typography": "custom", "title_typography_font_family": "Nunito", "title_typography_font_size": {"unit": "rem", "size": 1.33, "sizes": []}, "title_typography_font_size_tablet": {"unit": "rem", "size": "", "sizes": []}, "title_typography_font_size_mobile": {"unit": "rem", "size": "", "sizes": []}, "title_typography_font_weight": "600", "description_color": "#000000", "description_typography_typography": "custom", "description_typography_font_family": "Nunito", "description_typography_font_weight": "400", "__globals__": {"title_color": "globals/colors?id=primary"}, "ae_dynamic_rules": [{"_id": "7e34287"}]}, "elements": [], "isInner": false, "widgetType": "icon-box", "elType": "widget"}], "isInner": false, "elType": "column"}], "isInner": false, "elType": "section"}, {"id": "610c8a6d", "settings": {"ae_dynamic_rules": [{"_id": "f418e17"}], "slider_delay": "5000"}, "elements": [{"id": "1836d86", "settings": {"_column_size": 100, "_inline_size": null, "ae_dynamic_rules": [{"_id": "886e77d"}], "slider_delay": "5000"}, "elements": [{"id": "1a70e35f", "settings": {"title": "Research Park Innovation Cluster (RPIC)", "typography_typography": "custom", "typography_font_family": "Nunito", "typography_font_size": {"unit": "px", "size": 26, "sizes": []}, "typography_font_weight": "600", "__globals__": {"title_color": "globals/colors?id=primary"}, "ae_dynamic_rules": [{"_id": "9d7d36f"}]}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}, {"id": "7a977494", "settings": {"carousel": [{"id": 822, "url": "http://localhost/azoneus/wp-content/uploads/2022/10/RPIC-1-1024x472-1.jpg"}, {"id": 823, "url": "http://localhost/azoneus/wp-content/uploads/2022/10/RPIC-2-1024x472-1.jpg"}, {"id": 824, "url": "http://localhost/azoneus/wp-content/uploads/2022/10/RPIC-3-1024x472-1.jpg"}], "thumbnail_size": "full", "slides_to_show": "1", "ae_dynamic_rules": [{"_id": "c321210"}]}, "elements": [], "isInner": false, "widgetType": "image-carousel", "elType": "widget"}], "isInner": false, "elType": "column"}], "isInner": false, "elType": "section"}, {"id": "146864c5", "settings": {"margin": {"unit": "px", "top": "0", "right": 0, "bottom": "24", "left": 0, "isLinked": false}, "ae_dynamic_rules": [{"_id": "a8a1cf8"}], "slider_delay": "5000"}, "elements": [{"id": "71a48153", "settings": {"_column_size": 100, "_inline_size": null, "ae_dynamic_rules": [{"_id": "1955882"}], "slider_delay": "5000"}, "elements": [{"id": "33601d8a", "settings": {"selected_icon": {"value": "", "library": ""}, "title_text": "About This Project", "description_text": "The King <PERSON> University of Science and Technology (KAUST) is located on the coast of the Red Sea,approximately 80 KM north of Jeddah in SaudiArabia.\n<br><br>\nIt is a globally renowned graduate research university that makes significant contributions to scientific and technological advancement, playing a crucial role in the development of the Kingdom of Saudi Arabia, the region and the world. The construction of the university is an ongoing process with a goal towards sustainable development.\n<br><br>\nResearch Park Innovation Cluster (RPIC) is a part of Research Parks in KAUST and was founded with industry collaboration in mind. RPIC work with companies, private sector entities, nonprofits and government organizations to transfer knowledge and technologies created at the University and achieve public benefit and economic development.", "title_size": "h2", "text_align": "left", "title_bottom_space": {"unit": "px", "size": 10, "sizes": []}, "title_typography_typography": "custom", "title_typography_font_family": "Nunito", "title_typography_font_size": {"unit": "rem", "size": 1.33, "sizes": []}, "title_typography_font_size_tablet": {"unit": "rem", "size": "", "sizes": []}, "title_typography_font_size_mobile": {"unit": "rem", "size": "", "sizes": []}, "title_typography_font_weight": "600", "description_color": "#000000", "description_typography_typography": "custom", "description_typography_font_family": "Nunito", "description_typography_font_weight": "400", "__globals__": {"title_color": "globals/colors?id=primary"}, "ae_dynamic_rules": [{"_id": "7f13480"}]}, "elements": [], "isInner": false, "widgetType": "icon-box", "elType": "widget"}, {"id": "5a6c0c0e", "settings": {"selected_icon": {"value": "", "library": ""}, "title_text": "<PERSON><PERSON>", "description_text": "Supply, Installation and Commissioning of Building Management System.\n<br><br>\nSupply, Installation and Commissioning of Integration of Building Management System to Integrated Campus Automation System (ICAS)", "title_size": "h2", "text_align": "left", "title_bottom_space": {"unit": "px", "size": 10, "sizes": []}, "title_typography_typography": "custom", "title_typography_font_family": "Nunito", "title_typography_font_size": {"unit": "rem", "size": 1.33, "sizes": []}, "title_typography_font_size_tablet": {"unit": "rem", "size": "", "sizes": []}, "title_typography_font_size_mobile": {"unit": "rem", "size": "", "sizes": []}, "title_typography_font_weight": "600", "description_color": "#000000", "description_typography_typography": "custom", "description_typography_font_family": "Nunito", "description_typography_font_weight": "400", "__globals__": {"title_color": "globals/colors?id=primary"}, "ae_dynamic_rules": [{"_id": "77d94ff"}]}, "elements": [], "isInner": false, "widgetType": "icon-box", "elType": "widget"}], "isInner": false, "elType": "column"}], "isInner": false, "elType": "section"}, {"id": "5361f94e", "settings": {"ae_dynamic_rules": [{"_id": "8c60021"}], "slider_delay": "5000"}, "elements": [{"id": "3e65d2d7", "settings": {"_column_size": 100, "_inline_size": null, "ae_dynamic_rules": [{"_id": "a8cbaf8"}], "slider_delay": "5000"}, "elements": [{"id": "6c86400d", "settings": {"title": "Our Partners", "align": "center", "typography_typography": "custom", "typography_font_family": "Nunito", "typography_font_size": {"unit": "px", "size": 40, "sizes": []}, "typography_font_weight": "600", "_margin": {"unit": "px", "top": "50", "right": "0", "bottom": "50", "left": "0", "isLinked": false}, "ae_dynamic_rules": [{"_id": "47e1cde"}]}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}, {"id": "53285228", "settings": {"html": "<div class=\"elementor-element elementor-element-ccddfde elementor-arrows-position-outside elementor-pagination-position-outside elementor-widget elementor-widget-image-carousel e-widget-swiper\" data-id=\"ccddfde\" data-element_type=\"widget\" data-settings=\"{&quot;slides_to_show&quot;:&quot;4&quot;,&quot;navigation&quot;:&quot;both&quot;,&quot;autoplay&quot;:&quot;yes&quot;,&quot;pause_on_hover&quot;:&quot;yes&quot;,&quot;pause_on_interaction&quot;:&quot;yes&quot;,&quot;autoplay_speed&quot;:5000,&quot;infinite&quot;:&quot;yes&quot;,&quot;speed&quot;:500}\" data-widget_type=\"image-carousel.default\">\r\n\t\t\t\t<div class=\"elementor-widget-container\">\r\n\t\t\t<style>/*! elementor - v3.7.6 - 15-09-2022 */\r\n.elementor-widget-image-carousel .swiper-container{position:static}.elementor-widget-image-carousel .swiper-container .swiper-slide figure{line-height:inherit}.elementor-widget-image-carousel .swiper-slide{text-align:center}.elementor-image-carousel-wrapper:not(.swiper-container-initialized) .swiper-slide{max-width:calc(100% / var(--e-image-carousel-slides-to-show, 3))}</style>\t\t<div class=\"elementor-image-carousel-wrapper swiper-container swiper-container-initialized swiper-container-horizontal\" dir=\"ltr\">\r\n\t\t\t<div class=\"elementor-image-carousel swiper-wrapper\" style=\"transform: translate3d(-1272.5px, 0px, 0px); transition-duration: 0ms;\"><div class=\"swiper-slide swiper-slide-duplicate\" data-swiper-slide-index=\"3\" style=\"width: 254.5px;\"><a data-elementor-open-lightbox=\"yes\" data-elementor-lightbox-slideshow=\"ccddfde\" data-elementor-lightbox-title=\"itcc-300x171\" e-action-hash=\"#elementor-action%3Aaction%3Dlightbox%26settings%3DeyJpZCI6NjEyLCJ1cmwiOiJodHRwOlwvXC9sb2NhbGhvc3RcL2F6b25ldXNcL3dwLWNvbnRlbnRcL3VwbG9hZHNcLzIwMjJcLzA5XC9pdGNjLTMwMHgxNzEtMS5qcGciLCJzbGlkZXNob3ciOiJjY2RkZmRlIn0%3D\" href=\"http://localhost/azoneus/itcc/\"><figure class=\"swiper-slide-inner\"><img class=\"swiper-slide-image\" src=\"http://localhost/azoneus/wp-content/uploads/2022/09/itcc-300x171-1.jpg\" alt=\"itcc\"></figure></a></div><div class=\"swiper-slide swiper-slide-duplicate\" data-swiper-slide-index=\"4\" style=\"width: 254.5px;\"><a data-elementor-open-lightbox=\"yes\" data-elementor-lightbox-slideshow=\"ccddfde\" data-elementor-lightbox-title=\"Screenshot_2022-02-21_195707-removebg-preview-e1645467046185-300x260\" e-action-hash=\"#elementor-action%3Aaction%3Dlightbox%26settings%3DeyJpZCI6NjEzLCJ1cmwiOiJodHRwOlwvXC9sb2NhbGhvc3RcL2F6b25ldXNcL3dwLWNvbnRlbnRcL3VwbG9hZHNcLzIwMjJcLzA5XC9TY3JlZW5zaG90XzIwMjItMDItMjFfMTk1NzA3LXJlbW92ZWJnLXByZXZpZXctZTE2NDU0NjcwNDYxODUtMzAweDI2MC0xLnBuZyIsInNsaWRlc2hvdyI6ImNjZGRmZGUifQ%3D%3D\" href=\"http://localhost/azoneus/about/\"><figure class=\"swiper-slide-inner\"><img class=\"swiper-slide-image\" src=\"http://localhost/azoneus/wp-content/uploads/2022/09/Screenshot_2022-02-21_195707-removebg-preview-e1645467046185-300x260-1.png\" alt=\"Screenshot_2022-02-21_195707-removebg-preview-e1645467046185\"></figure></a></div><div class=\"swiper-slide swiper-slide-duplicate\" data-swiper-slide-index=\"5\" style=\"width: 254.5px;\"><a data-elementor-open-lightbox=\"yes\" data-elementor-lightbox-slideshow=\"ccddfde\" data-elementor-lightbox-title=\"17171693.548c103186dbe-removebg-preview-1\" e-action-hash=\"#elementor-action%3Aaction%3Dlightbox%26settings%3DeyJpZCI6NjE0LCJ1cmwiOiJodHRwOlwvXC9sb2NhbGhvc3RcL2F6b25ldXNcL3dwLWNvbnRlbnRcL3VwbG9hZHNcLzIwMjJcLzA5XC8xNzE3MTY5My41NDhjMTAzMTg2ZGJlLXJlbW92ZWJnLXByZXZpZXctMS5wbmciLCJzbGlkZXNob3ciOiJjY2RkZmRlIn0%3D\" href=\"http://localhost/azoneus/kafd/\"><figure class=\"swiper-slide-inner\"><img class=\"swiper-slide-image\" src=\"http://localhost/azoneus/wp-content/uploads/2022/09/17171693.548c103186dbe-removebg-preview-1.png\" alt=\"17171693.548c103186dbe-removebg-preview-1\"></figure></a></div><div class=\"swiper-slide swiper-slide-duplicate\" data-swiper-slide-index=\"6\" style=\"width: 254.5px;\"><a data-elementor-open-lightbox=\"yes\" data-elementor-lightbox-slideshow=\"ccddfde\" data-elementor-lightbox-title=\"princes-nourah\" e-action-hash=\"#elementor-action%3Aaction%3Dlightbox%26settings%3DeyJpZCI6NjE1LCJ1cmwiOiJodHRwOlwvXC9sb2NhbGhvc3RcL2F6b25ldXNcL3dwLWNvbnRlbnRcL3VwbG9hZHNcLzIwMjJcLzA5XC9wcmluY2VzLW5vdXJhaC5qcGciLCJzbGlkZXNob3ciOiJjY2RkZmRlIn0%3D\" href=\"http://localhost/azoneus/pnu/\"><figure class=\"swiper-slide-inner\"><img class=\"swiper-slide-image\" src=\"http://localhost/azoneus/wp-content/uploads/2022/09/princes-nourah.jpg\" alt=\"princes-nourah\"></figure></a></div>\r\n\t\t\t\t\t\t\t\t<div class=\"swiper-slide swiper-slide-prev\" data-swiper-slide-index=\"0\" style=\"width: 254.5px;\"><a data-elementor-open-lightbox=\"yes\" data-elementor-lightbox-slideshow=\"ccddfde\" data-elementor-lightbox-title=\"sports-city-300x171\" e-action-hash=\"#elementor-action%3Aaction%3Dlightbox%26settings%3DeyJpZCI6NjEwLCJ1cmwiOiJodHRwOlwvXC9sb2NhbGhvc3RcL2F6b25ldXNcL3dwLWNvbnRlbnRcL3VwbG9hZHNcLzIwMjJcLzA5XC9zcG9ydHMtY2l0eS0zMDB4MTcxLTEuanBnIiwic2xpZGVzaG93IjoiY2NkZGZkZSJ9\" href=\"http://localhost/azoneus/kasc/\"><figure class=\"swiper-slide-inner\"><img class=\"swiper-slide-image\" src=\"http://localhost/azoneus/wp-content/uploads/2022/09/sports-city-300x171-1.jpg\" alt=\"sports-city\"></figure></a></div><div class=\"swiper-slide swiper-slide-active\" data-swiper-slide-index=\"1\" style=\"width: 254.5px;\"><a data-elementor-open-lightbox=\"yes\" data-elementor-lightbox-slideshow=\"ccddfde\" data-elementor-lightbox-title=\"kaust-300x171\" e-action-hash=\"#elementor-action%3Aaction%3Dlightbox%26settings%3DeyJpZCI6NjA5LCJ1cmwiOiJodHRwOlwvXC9sb2NhbGhvc3RcL2F6b25ldXNcL3dwLWNvbnRlbnRcL3VwbG9hZHNcLzIwMjJcLzA5XC9rYXVzdC0zMDB4MTcxLTEuanBnIiwic2xpZGVzaG93IjoiY2NkZGZkZSJ9\" href=\"http://localhost/azoneus/kaust/\"><figure class=\"swiper-slide-inner\"><img class=\"swiper-slide-image\" src=\"http://localhost/azoneus/wp-content/uploads/2022/09/kaust-300x171-1.jpg\" alt=\"kaust\"></figure></a></div><div class=\"swiper-slide swiper-slide-next\" data-swiper-slide-index=\"2\" style=\"width: 254.5px;\"><a data-elementor-open-lightbox=\"yes\" data-elementor-lightbox-slideshow=\"ccddfde\" data-elementor-lightbox-title=\"idb-300x171\" e-action-hash=\"#elementor-action%3Aaction%3Dlightbox%26settings%3DeyJpZCI6NjExLCJ1cmwiOiJodHRwOlwvXC9sb2NhbGhvc3RcL2F6b25ldXNcL3dwLWNvbnRlbnRcL3VwbG9hZHNcLzIwMjJcLzA5XC9pZGItMzAweDE3MS0xLmpwZyIsInNsaWRlc2hvdyI6ImNjZGRmZGUifQ%3D%3D\" href=\"http://localhost/azoneus/the-king-abdullah-university-of-science-and-technology-kaust/\"><figure class=\"swiper-slide-inner\"><img class=\"swiper-slide-image\" src=\"http://localhost/azoneus/wp-content/uploads/2022/09/idb-300x171-1.jpg\" alt=\"idb\"></figure></a></div><div class=\"swiper-slide\" data-swiper-slide-index=\"3\" style=\"width: 254.5px;\"><a data-elementor-open-lightbox=\"yes\" data-elementor-lightbox-slideshow=\"ccddfde\" data-elementor-lightbox-title=\"itcc-300x171\" e-action-hash=\"#elementor-action%3Aaction%3Dlightbox%26settings%3DeyJpZCI6NjEyLCJ1cmwiOiJodHRwOlwvXC9sb2NhbGhvc3RcL2F6b25ldXNcL3dwLWNvbnRlbnRcL3VwbG9hZHNcLzIwMjJcLzA5XC9pdGNjLTMwMHgxNzEtMS5qcGciLCJzbGlkZXNob3ciOiJjY2RkZmRlIn0%3D\" href=\"http://localhost/azoneus/information-technology-communication-complex-itcc-towers/\"><figure class=\"swiper-slide-inner\"><img class=\"swiper-slide-image\" src=\"http://localhost/azoneus/wp-content/uploads/2022/09/itcc-300x171-1.jpg\" alt=\"itcc\"></figure></a></div><div class=\"swiper-slide\" data-swiper-slide-index=\"4\" style=\"width: 254.5px;\"><a data-elementor-open-lightbox=\"yes\" data-elementor-lightbox-slideshow=\"ccddfde\" data-elementor-lightbox-title=\"Screenshot_2022-02-21_195707-removebg-preview-e1645467046185-300x260\" e-action-hash=\"#elementor-action%3Aaction%3Dlightbox%26settings%3DeyJpZCI6NjEzLCJ1cmwiOiJodHRwOlwvXC9sb2NhbGhvc3RcL2F6b25ldXNcL3dwLWNvbnRlbnRcL3VwbG9hZHNcLzIwMjJcLzA5XC9TY3JlZW5zaG90XzIwMjItMDItMjFfMTk1NzA3LXJlbW92ZWJnLXByZXZpZXctZTE2NDU0NjcwNDYxODUtMzAweDI2MC0xLnBuZyIsInNsaWRlc2hvdyI6ImNjZGRmZGUifQ%3D%3D\" href=\"http://localhost/azoneus/neom/\"><figure class=\"swiper-slide-inner\"><img class=\"swiper-slide-image\" src=\"http://localhost/azoneus/wp-content/uploads/2022/09/Screenshot_2022-02-21_195707-removebg-preview-e1645467046185-300x260-1.png\" alt=\"Screenshot_2022-02-21_195707-removebg-preview-e1645467046185\"></figure></a></div><div class=\"swiper-slide\" data-swiper-slide-index=\"5\" style=\"width: 254.5px;\"><a data-elementor-open-lightbox=\"yes\" data-elementor-lightbox-slideshow=\"ccddfde\" data-elementor-lightbox-title=\"17171693.548c103186dbe-removebg-preview-1\" e-action-hash=\"#elementor-action%3Aaction%3Dlightbox%26settings%3DeyJpZCI6NjE0LCJ1cmwiOiJodHRwOlwvXC9sb2NhbGhvc3RcL2F6b25ldXNcL3dwLWNvbnRlbnRcL3VwbG9hZHNcLzIwMjJcLzA5XC8xNzE3MTY5My41NDhjMTAzMTg2ZGJlLXJlbW92ZWJnLXByZXZpZXctMS5wbmciLCJzbGlkZXNob3ciOiJjY2RkZmRlIn0%3D\" href=\"http://localhost/azoneus/kafd/\"><figure class=\"swiper-slide-inner\"><img class=\"swiper-slide-image\" src=\"http://localhost/azoneus/wp-content/uploads/2022/09/17171693.548c103186dbe-removebg-preview-1.png\" alt=\"17171693.548c103186dbe-removebg-preview-1\"></figure></a></div><div class=\"swiper-slide\" data-swiper-slide-index=\"6\" style=\"width: 254.5px;\"><a data-elementor-open-lightbox=\"yes\" data-elementor-lightbox-slideshow=\"ccddfde\" data-elementor-lightbox-title=\"princes-nourah\" e-action-hash=\"#elementor-action%3Aaction%3Dlightbox%26settings%3DeyJpZCI6NjE1LCJ1cmwiOiJodHRwOlwvXC9sb2NhbGhvc3RcL2F6b25ldXNcL3dwLWNvbnRlbnRcL3VwbG9hZHNcLzIwMjJcLzA5XC9wcmluY2VzLW5vdXJhaC5qcGciLCJzbGlkZXNob3ciOiJjY2RkZmRlIn0%3D\" href=\"http://localhost/azoneus/pnu/\"><figure class=\"swiper-slide-inner\"><img class=\"swiper-slide-image\" src=\"http://localhost/azoneus/wp-content/uploads/2022/09/princes-nourah.jpg\" alt=\"princes-nourah\"></figure></a></div>\t\t\t<div class=\"swiper-slide swiper-slide-duplicate swiper-slide-duplicate-prev\" data-swiper-slide-index=\"0\" style=\"width: 254.5px;\"><a data-elementor-open-lightbox=\"yes\" data-elementor-lightbox-slideshow=\"ccddfde\" data-elementor-lightbox-title=\"sports-city-300x171\" e-action-hash=\"#elementor-action%3Aaction%3Dlightbox%26settings%3DeyJpZCI6NjEwLCJ1cmwiOiJodHRwOlwvXC9sb2NhbGhvc3RcL2F6b25ldXNcL3dwLWNvbnRlbnRcL3VwbG9hZHNcLzIwMjJcLzA5XC9zcG9ydHMtY2l0eS0zMDB4MTcxLTEuanBnIiwic2xpZGVzaG93IjoiY2NkZGZkZSJ9\" href=\"http://localhost/azoneus/about/\"><figure class=\"swiper-slide-inner\"><img class=\"swiper-slide-image\" src=\"http://localhost/azoneus/wp-content/uploads/2022/09/sports-city-300x171-1.jpg\" alt=\"sports-city\"></figure></a></div><div class=\"swiper-slide swiper-slide-duplicate swiper-slide-duplicate-active\" data-swiper-slide-index=\"1\" style=\"width: 254.5px;\"><a data-elementor-open-lightbox=\"yes\" data-elementor-lightbox-slideshow=\"ccddfde\" data-elementor-lightbox-title=\"kaust-300x171\" e-action-hash=\"#elementor-action%3Aaction%3Dlightbox%26settings%3DeyJpZCI6NjA5LCJ1cmwiOiJodHRwOlwvXC9sb2NhbGhvc3RcL2F6b25ldXNcL3dwLWNvbnRlbnRcL3VwbG9hZHNcLzIwMjJcLzA5XC9rYXVzdC0zMDB4MTcxLTEuanBnIiwic2xpZGVzaG93IjoiY2NkZGZkZSJ9\" href=\"http://localhost/azoneus/kaust/\"><figure class=\"swiper-slide-inner\"><img class=\"swiper-slide-image\" src=\"http://localhost/azoneus/wp-content/uploads/2022/09/kaust-300x171-1.jpg\" alt=\"kaust\"></figure></a></div><div class=\"swiper-slide swiper-slide-duplicate swiper-slide-duplicate-next\" data-swiper-slide-index=\"2\" style=\"width: 254.5px;\"><a data-elementor-open-lightbox=\"yes\" data-elementor-lightbox-slideshow=\"ccddfde\" data-elementor-lightbox-title=\"idb-300x171\" e-action-hash=\"#elementor-action%3Aaction%3Dlightbox%26settings%3DeyJpZCI6NjExLCJ1cmwiOiJodHRwOlwvXC9sb2NhbGhvc3RcL2F6b25ldXNcL3dwLWNvbnRlbnRcL3VwbG9hZHNcLzIwMjJcLzA5XC9pZGItMzAweDE3MS0xLmpwZyIsInNsaWRlc2hvdyI6ImNjZGRmZGUifQ%3D%3D\" href=\"http://localhost/azoneus/about/\"><figure class=\"swiper-slide-inner\"><img class=\"swiper-slide-image\" src=\"http://localhost/azoneus/wp-content/uploads/2022/09/idb-300x171-1.jpg\" alt=\"idb\"></figure></a></div><div class=\"swiper-slide swiper-slide-duplicate\" data-swiper-slide-index=\"3\" style=\"width: 254.5px;\"><a data-elementor-open-lightbox=\"yes\" data-elementor-lightbox-slideshow=\"ccddfde\" data-elementor-lightbox-title=\"itcc-300x171\" e-action-hash=\"#elementor-action%3Aaction%3Dlightbox%26settings%3DeyJpZCI6NjEyLCJ1cmwiOiJodHRwOlwvXC9sb2NhbGhvc3RcL2F6b25ldXNcL3dwLWNvbnRlbnRcL3VwbG9hZHNcLzIwMjJcLzA5XC9pdGNjLTMwMHgxNzEtMS5qcGciLCJzbGlkZXNob3ciOiJjY2RkZmRlIn0%3D\" href=\"http://localhost/azoneus/about/\"><figure class=\"swiper-slide-inner\"><img class=\"swiper-slide-image\" src=\"http://localhost/azoneus/wp-content/uploads/2022/09/itcc-300x171-1.jpg\" alt=\"itcc\"></figure></a></div></div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<div class=\"swiper-pagination swiper-pagination-clickable swiper-pagination-bullets\"><span class=\"swiper-pagination-bullet\" tabindex=\"0\" role=\"button\" aria-label=\"Go to slide 1\"></span><span class=\"swiper-pagination-bullet swiper-pagination-bullet-active\" tabindex=\"0\" role=\"button\" aria-label=\"Go to slide 2\"></span><span class=\"swiper-pagination-bullet\" tabindex=\"0\" role=\"button\" aria-label=\"Go to slide 3\"></span><span class=\"swiper-pagination-bullet\" tabindex=\"0\" role=\"button\" aria-label=\"Go to slide 4\"></span><span class=\"swiper-pagination-bullet\" tabindex=\"0\" role=\"button\" aria-label=\"Go to slide 5\"></span><span class=\"swiper-pagination-bullet\" tabindex=\"0\" role=\"button\" aria-label=\"Go to slide 6\"></span><span class=\"swiper-pagination-bullet\" tabindex=\"0\" role=\"button\" aria-label=\"Go to slide 7\"></span></div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<div class=\"elementor-swiper-button elementor-swiper-button-prev\" tabindex=\"0\" role=\"button\" aria-label=\"Previous slide\">\r\n\t\t\t\t\t\t<i aria-hidden=\"true\" class=\"eicon-chevron-left\"></i>\t\t\t\t\t\t<span class=\"elementor-screen-only\">Previous</span>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t<div class=\"elementor-swiper-button elementor-swiper-button-next\" tabindex=\"0\" role=\"button\" aria-label=\"Next slide\">\r\n\t\t\t\t\t\t<i aria-hidden=\"true\" class=\"eicon-chevron-right\"></i>\t\t\t\t\t\t<span class=\"elementor-screen-only\">Next</span>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t<span class=\"swiper-notification\" aria-live=\"assertive\" aria-atomic=\"true\"></span></div>\r\n\t\t\t\t</div>\r\n\t\t\t\t</div>", "ae_dynamic_rules": [{"_id": "2df1558"}]}, "elements": [], "isInner": false, "widgetType": "html", "elType": "widget"}], "isInner": false, "elType": "column"}], "isInner": false, "elType": "section"}], "page_settings": [], "version": "0.4", "title": "Azoneus_KAUST", "type": "page"}