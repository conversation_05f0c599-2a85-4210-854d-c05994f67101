{"content": [{"id": "363eb67a", "settings": {"layout": "full_width", "gap": "no", "background_background": "classic", "background_color": "#EAEAEA", "padding": {"unit": "px", "top": "30", "right": "0", "bottom": "0", "left": "0", "isLinked": false}, "ae_dynamic_rules": [{"_id": "a986cd9"}], "slider_delay": "5000"}, "elements": [{"id": "39895308", "settings": {"_column_size": 100, "_inline_size": null, "ae_dynamic_rules": [{"_id": "c0638a9"}], "slider_delay": "5000"}, "elements": [{"id": "2bb0e779", "settings": {"ae_dynamic_rules": [{"_id": "291ec56"}], "slider_delay": "5000"}, "elements": [{"id": "1e378ed6", "settings": {"_column_size": 100, "_inline_size": null, "ae_dynamic_rules": [{"_id": "c712a69"}], "slider_delay": "5000"}, "elements": [{"id": "45036707", "settings": {"title": "Our Solutions", "typography_typography": "custom", "typography_font_family": "Nunito", "typography_font_size": {"unit": "px", "size": 40, "sizes": []}, "typography_font_size_mobile": {"unit": "px", "size": 26, "sizes": []}, "typography_font_weight": "600", "_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "-35", "left": "0", "isLinked": false}, "ae_dynamic_rules": [{"_id": "d4d8ff9"}]}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}, {"id": "62cfb5cc", "settings": {"width": {"unit": "px", "size": 25, "sizes": []}, "width_tablet": {"unit": "px", "size": "", "sizes": []}, "width_mobile": {"unit": "px", "size": 20, "sizes": []}, "text": "Divider", "color": "#F4A41C", "weight": {"unit": "px", "size": 4, "sizes": []}, "gap": {"unit": "px", "size": 3, "sizes": []}, "_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": false}, "_margin_mobile": {"unit": "px", "top": "0", "right": "0", "bottom": "-7", "left": "0", "isLinked": false}, "_padding": {"unit": "px", "top": "15", "right": "0", "bottom": "15", "left": "0", "isLinked": false}, "ae_dynamic_rules": [{"_id": "58b5d6c"}]}, "elements": [], "isInner": false, "widgetType": "divider", "elType": "widget"}], "isInner": true, "elType": "column"}], "isInner": true, "elType": "section"}, {"id": "5a52c6d1", "settings": {"html": "<div class=\"elementor-shape elementor-shape-bottom\" data-negative=\"false\">\r\n\t\t\t<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 1000 100\" preserveAspectRatio=\"none\">\r\n\t<path class=\"elementor-shape-fill\" opacity=\"0.33\" d=\"M473,67.3c-203.9,88.3-263.1-34-320.3,0C66,119.1,0,59.7,0,59.7V0h1000v59.7 c0,0-62.1,26.1-94.9,29.3c-32.8,3.3-62.8-12.3-75.8-22.1C806,49.6,745.3,8.7,694.9,4.7S492.4,59,473,67.3z\"></path>\r\n\t<path class=\"elementor-shape-fill\" opacity=\"0.66\" d=\"M734,67.3c-45.5,0-77.2-23.2-129.1-39.1c-28.6-8.7-150.3-10.1-254,39.1 s-91.7-34.4-149.2,0C115.7,118.3,0,39.8,0,39.8V0h1000v36.5c0,0-28.2-18.5-92.1-18.5C810.2,18.1,775.7,67.3,734,67.3z\"></path>\r\n\t<path class=\"elementor-shape-fill\" d=\"M766.1,28.9c-200-57.5-266,65.5-395.1,19.5C242,1.8,242,5.4,184.8,20.6C128,35.8,132.3,44.9,89.9,52.5C28.6,63.7,0,0,0,0 h1000c0,0-9.9,40.9-83.6,48.1S829.6,47,766.1,28.9z\"></path>\r\n</svg>\t\t</div>", "_margin": {"unit": "px", "top": "83", "right": "0", "bottom": "0", "left": "0", "isLinked": false}, "_margin_tablet": {"unit": "px", "top": "25", "right": "0", "bottom": "0", "left": "0", "isLinked": false}, "_margin_mobile": {"unit": "px", "top": "-50", "right": "0", "bottom": "0", "left": "0", "isLinked": false}, "_padding": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": true}, "ae_dynamic_rules": [{"_id": "a0c5429"}]}, "elements": [], "isInner": false, "widgetType": "html", "elType": "widget"}], "isInner": false, "elType": "column"}], "isInner": false, "elType": "section"}, {"id": "4ecb740a", "settings": {"_element_id": "automation", "structure": "20", "margin": {"unit": "px", "top": "50", "right": 0, "bottom": "0", "left": 0, "isLinked": false}, "ae_dynamic_rules": [{"_id": "f4a92ee"}], "slider_delay": "5000"}, "elements": [{"id": "6c052ec4", "settings": {"_column_size": 50, "_inline_size": 37.823, "ae_dynamic_rules": [{"_id": "457fc34"}], "slider_delay": "5000"}, "elements": [{"id": "52c92bdf", "settings": {"image": {"url": "https://azoneus.com/wp-content/uploads/2022/03/274856864_283786763890782_1173564116682405651_n-768x467.png", "id": "", "source": "url"}, "space": {"unit": "px", "size": 440, "sizes": []}, "space_tablet": {"unit": "px", "size": 280, "sizes": []}, "space_mobile": {"unit": "px", "size": 335, "sizes": []}, "image_border_radius": {"unit": "px", "top": "5", "right": "5", "bottom": "5", "left": "5", "isLinked": true}, "ae_dynamic_rules": [{"_id": "4de2204"}]}, "elements": [], "isInner": false, "widgetType": "image", "elType": "widget"}], "isInner": false, "elType": "column"}, {"id": "44114d6d", "settings": {"_column_size": 50, "_inline_size": 62.177, "ae_dynamic_rules": [{"_id": "977bc39"}], "slider_delay": "5000"}, "elements": [{"id": "69057152", "settings": {"ae_dynamic_rules": [{"_id": "116935e"}], "slider_delay": "5000"}, "elements": [{"id": "345ca609", "settings": {"_column_size": 100, "_inline_size": null, "ae_dynamic_rules": [{"_id": "4898359"}], "slider_delay": "5000"}, "elements": [{"id": "2247d9ee", "settings": {"title": "Automation System Integrations\n", "align": "center", "typography_typography": "custom", "typography_font_family": "Nunito", "typography_font_size": {"unit": "px", "size": 30, "sizes": []}, "typography_font_weight": "300", "__globals__": {"title_color": "globals/colors?id=secondary"}, "ae_dynamic_rules": [{"_id": "b0a8857"}]}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}, {"id": "77e4bce1", "settings": {"editor": "<p>Utilizing the latest INTERNET-OF-THINGS concepts and offerings to implement it in commercial and residential facilities to allow having all facilities systems communicating in an intelligent and interactive way between systems to ensure comfort, safe, secured environment with optimum use of energy and resources.</p>", "align": "center", "text_color": "#000000", "typography_typography": "custom", "typography_font_family": "Nunito", "typography_font_weight": "400", "_padding": {"unit": "px", "top": "0", "right": "10", "bottom": "0", "left": "10", "isLinked": false}, "ae_dynamic_rules": [{"_id": "2b79afb"}]}, "elements": [], "isInner": false, "widgetType": "text-editor", "elType": "widget"}, {"id": "18e8330e", "settings": {"text": "View More", "link": {"url": "http://localhost/azoneus/automation-system-integrations/", "is_external": "", "nofollow": "", "custom_attributes": ""}, "align": "center", "typography_typography": "custom", "typography_font_family": "Nunito", "typography_font_size": {"unit": "px", "size": 15, "sizes": []}, "typography_font_weight": "500", "hover_animation": "grow", "__globals__": {"background_color": "globals/colors?id=secondary"}, "ae_dynamic_rules": [{"_id": "aa4fa86"}]}, "elements": [], "isInner": false, "widgetType": "button", "elType": "widget"}], "isInner": true, "elType": "column"}], "isInner": true, "elType": "section"}], "isInner": false, "elType": "column"}], "isInner": false, "elType": "section"}, {"id": "27f86e4b", "settings": {"structure": "20", "margin": {"unit": "px", "top": "50", "right": 0, "bottom": "0", "left": 0, "isLinked": false}, "_element_id": "building", "reverse_order_mobile": "reverse-mobile", "ae_dynamic_rules": [{"_id": "086cc72"}], "slider_delay": "5000"}, "elements": [{"id": "4ab3c058", "settings": {"_column_size": 50, "_inline_size": 63.723, "ae_dynamic_rules": [{"_id": "dc9a3cd"}], "slider_delay": "5000"}, "elements": [{"id": "4bd7b8f0", "settings": {"ae_dynamic_rules": [{"_id": "f0913e6"}], "slider_delay": "5000"}, "elements": [{"id": "6100d442", "settings": {"_column_size": 100, "_inline_size": null, "ae_dynamic_rules": [{"_id": "44e26f6"}], "slider_delay": "5000"}, "elements": [{"id": "2c73cc0e", "settings": {"title": "Building Management System", "align": "center", "typography_typography": "custom", "typography_font_family": "Nunito", "typography_font_size": {"unit": "px", "size": 30, "sizes": []}, "typography_font_weight": "300", "__globals__": {"title_color": "globals/colors?id=secondary"}, "ae_dynamic_rules": [{"_id": "0c79a6d"}]}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}, {"id": "5ddcb1e8", "settings": {"editor": "<p>Full management of Heating, Ventilation and Air Conditioning, and other related systems in the facility through a network of field sensors, actuators, valves, etc. to ensure the highest comfort quality of the environment in the most energy-efficient way.</p>", "align": "center", "text_color": "#000000", "typography_typography": "custom", "typography_font_family": "Nunito", "typography_font_weight": "400", "_padding": {"unit": "px", "top": "0", "right": "10", "bottom": "0", "left": "10", "isLinked": false}, "ae_dynamic_rules": [{"_id": "7542b51"}]}, "elements": [], "isInner": false, "widgetType": "text-editor", "elType": "widget"}, {"id": "6ffa9678", "settings": {"text": "View More", "link": {"url": "http://localhost/azoneus/building-management-system/", "is_external": "", "nofollow": "", "custom_attributes": ""}, "align": "center", "typography_typography": "custom", "typography_font_family": "Nunito", "typography_font_size": {"unit": "px", "size": 15, "sizes": []}, "typography_font_weight": "500", "hover_animation": "grow", "__globals__": {"background_color": "globals/colors?id=secondary"}, "ae_dynamic_rules": [{"_id": "80f453f"}]}, "elements": [], "isInner": false, "widgetType": "button", "elType": "widget"}], "isInner": true, "elType": "column"}], "isInner": true, "elType": "section"}], "isInner": false, "elType": "column"}, {"id": "6097bd9d", "settings": {"_column_size": 50, "_inline_size": 36.23, "ae_dynamic_rules": [{"_id": "eff7a08"}], "slider_delay": "5000"}, "elements": [{"id": "3fcc554a", "settings": {"image": {"url": "https://azoneus.com/wp-content/uploads/2022/03/1603915231alerton_nw.png", "id": "", "source": "url", "alt": ""}, "space": {"unit": "px", "size": 440, "sizes": []}, "space_tablet": {"unit": "px", "size": 280, "sizes": []}, "space_mobile": {"unit": "px", "size": 335, "sizes": []}, "image_border_radius": {"unit": "px", "top": "5", "right": "5", "bottom": "5", "left": "5", "isLinked": true}, "ae_dynamic_rules": [{"_id": "74dfe6b"}]}, "elements": [], "isInner": false, "widgetType": "image", "elType": "widget"}], "isInner": false, "elType": "column"}], "isInner": false, "elType": "section"}, {"id": "7858a570", "settings": {"structure": "20", "margin": {"unit": "px", "top": "30", "right": 0, "bottom": "0", "left": 0, "isLinked": false}, "_element_id": "power", "ae_dynamic_rules": [{"_id": "d7d4837"}], "slider_delay": "5000"}, "elements": [{"id": "47340442", "settings": {"_column_size": 50, "_inline_size": 38.411, "ae_dynamic_rules": [{"_id": "2b7fa0f"}], "slider_delay": "5000"}, "elements": [{"id": "13877411", "settings": {"image": {"url": "https://azoneus.com/wp-content/uploads/2022/03/EV-Header-Devices-1.png", "id": "", "source": "url", "alt": ""}, "space": {"unit": "px", "size": 440, "sizes": []}, "space_tablet": {"unit": "px", "size": 280, "sizes": []}, "space_mobile": {"unit": "px", "size": 335, "sizes": []}, "image_border_radius": {"unit": "px", "top": "5", "right": "5", "bottom": "5", "left": "5", "isLinked": true}, "ae_dynamic_rules": [{"_id": "5afcb77"}]}, "elements": [], "isInner": false, "widgetType": "image", "elType": "widget"}], "isInner": false, "elType": "column"}, {"id": "3cab854d", "settings": {"_column_size": 50, "_inline_size": 61.589, "ae_dynamic_rules": [{"_id": "a4908f3"}], "slider_delay": "5000"}, "elements": [{"id": "7951217c", "settings": {"margin": {"unit": "px", "top": "140", "right": 0, "bottom": "0", "left": 0, "isLinked": false}, "margin_tablet": {"unit": "px", "top": "0", "right": 0, "bottom": "0", "left": 0, "isLinked": true}, "margin_mobile": {"unit": "px", "top": "0", "right": 0, "bottom": "0", "left": 0, "isLinked": true}, "ae_dynamic_rules": [{"_id": "cce7d01"}], "slider_delay": "5000"}, "elements": [{"id": "2f7b5f90", "settings": {"_column_size": 100, "_inline_size": null, "ae_dynamic_rules": [{"_id": "f768f62"}], "slider_delay": "5000"}, "elements": [{"id": "72e6084a", "settings": {"title": "Energy Manager and Power Monitoring System", "align": "center", "typography_typography": "custom", "typography_font_family": "Nunito", "typography_font_size": {"unit": "px", "size": 30, "sizes": []}, "typography_font_weight": "300", "__globals__": {"title_color": "globals/colors?id=secondary"}, "ae_dynamic_rules": [{"_id": "e251912"}]}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}, {"id": "3bb8b637", "settings": {"editor": "<p>Monitoring all energy usage in the building including the power meters, the use of HVAC and equipment, and issuance of the energy bills for individual tenants and areas in the building.</p>", "align": "center", "text_color": "#000000", "typography_typography": "custom", "typography_font_family": "Nunito", "typography_font_weight": "400", "_padding": {"unit": "px", "top": "0", "right": "10", "bottom": "0", "left": "10", "isLinked": false}, "ae_dynamic_rules": [{"_id": "d4477e3"}]}, "elements": [], "isInner": false, "widgetType": "text-editor", "elType": "widget"}, {"id": "3c420773", "settings": {"text": "View More", "link": {"url": "http://localhost/azoneus/power-monitoring-system/", "is_external": "", "nofollow": "", "custom_attributes": ""}, "align": "center", "typography_typography": "custom", "typography_font_family": "Nunito", "typography_font_size": {"unit": "px", "size": 15, "sizes": []}, "typography_font_weight": "500", "hover_animation": "grow", "__globals__": {"background_color": "globals/colors?id=secondary"}, "ae_dynamic_rules": [{"_id": "35dcc60"}]}, "elements": [], "isInner": false, "widgetType": "button", "elType": "widget"}], "isInner": true, "elType": "column"}], "isInner": true, "elType": "section"}], "isInner": false, "elType": "column"}], "isInner": false, "elType": "section"}, {"id": "3ef8f871", "settings": {"structure": "20", "margin": {"unit": "px", "top": "30", "right": 0, "bottom": "0", "left": 0, "isLinked": false}, "_element_id": "fire", "reverse_order_mobile": "reverse-mobile", "ae_dynamic_rules": [{"_id": "074c51a"}], "slider_delay": "5000"}, "elements": [{"id": "25f6f138", "settings": {"_column_size": 50, "_inline_size": 61.78, "ae_dynamic_rules": [{"_id": "d1c78d3"}], "slider_delay": "5000"}, "elements": [{"id": "5c327289", "settings": {"margin": {"unit": "px", "top": "0", "right": 0, "bottom": "0", "left": 0, "isLinked": false}, "ae_dynamic_rules": [{"_id": "433a2c7"}], "slider_delay": "5000"}, "elements": [{"id": "1e96196e", "settings": {"_column_size": 100, "_inline_size": null, "ae_dynamic_rules": [{"_id": "0dc6a2e"}], "slider_delay": "5000"}, "elements": [{"id": "3081dc34", "settings": {"title": "Fire Alarm System", "align": "center", "typography_typography": "custom", "typography_font_family": "Nunito", "typography_font_size": {"unit": "px", "size": 30, "sizes": []}, "typography_font_weight": "300", "__globals__": {"title_color": "globals/colors?id=secondary"}, "ae_dynamic_rules": [{"_id": "275f8ca"}]}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}, {"id": "408df0c4", "settings": {"editor": "<p>Fire detection using intelligent addressable field devices; Smoke Detectors, Heat Detectors, and Manual Pull-Station. Reporting of alarm signaling notification throughout the facilities all the way to fire fighting teams. Taking fire and smoke actions based on disciplined cause and effect plans.</p>", "align": "center", "text_color": "#000000", "typography_typography": "custom", "typography_font_family": "Nunito", "typography_font_weight": "400", "_padding": {"unit": "px", "top": "0", "right": "10", "bottom": "0", "left": "10", "isLinked": false}, "ae_dynamic_rules": [{"_id": "0ce0f1f"}]}, "elements": [], "isInner": false, "widgetType": "text-editor", "elType": "widget"}, {"id": "646fa33d", "settings": {"text": "View More", "link": {"url": "http://localhost/azoneus/fire-alarm-system/", "is_external": "", "nofollow": "", "custom_attributes": ""}, "align": "center", "typography_typography": "custom", "typography_font_family": "Nunito", "typography_font_size": {"unit": "px", "size": 15, "sizes": []}, "typography_font_weight": "500", "hover_animation": "grow", "__globals__": {"background_color": "globals/colors?id=secondary"}, "ae_dynamic_rules": [{"_id": "71316ab"}]}, "elements": [], "isInner": false, "widgetType": "button", "elType": "widget"}], "isInner": true, "elType": "column"}], "isInner": true, "elType": "section"}], "isInner": false, "elType": "column"}, {"id": "6dd12b20", "settings": {"_column_size": 50, "_inline_size": 38.22, "ae_dynamic_rules": [{"_id": "f147080"}], "slider_delay": "5000"}, "elements": [{"id": "36a6a1c4", "settings": {"image": {"url": "https://azoneus.com/wp-content/uploads/2022/03/featere-768x403.jpg", "id": "", "source": "url", "alt": ""}, "space": {"unit": "px", "size": 440, "sizes": []}, "space_tablet": {"unit": "px", "size": 280, "sizes": []}, "space_mobile": {"unit": "px", "size": 335, "sizes": []}, "image_border_radius": {"unit": "px", "top": "5", "right": "5", "bottom": "5", "left": "5", "isLinked": true}, "ae_dynamic_rules": [{"_id": "6710a5e"}]}, "elements": [], "isInner": false, "widgetType": "image", "elType": "widget"}], "isInner": false, "elType": "column"}], "isInner": false, "elType": "section"}, {"id": "2fc8408f", "settings": {"structure": "20", "margin": {"unit": "px", "top": "80", "right": 0, "bottom": "0", "left": 0, "isLinked": false}, "_element_id": "lab", "ae_dynamic_rules": [{"_id": "3f13431"}], "slider_delay": "5000"}, "elements": [{"id": "3b494485", "settings": {"_column_size": 50, "_inline_size": 38.004, "ae_dynamic_rules": [{"_id": "d89c8fb"}], "slider_delay": "5000"}, "elements": [{"id": "334bfef4", "settings": {"image": {"url": "https://azoneus.com/wp-content/uploads/2022/03/la-768x512.jpg", "id": "", "source": "url", "alt": ""}, "space": {"unit": "px", "size": 440, "sizes": []}, "space_tablet": {"unit": "px", "size": 280, "sizes": []}, "space_mobile": {"unit": "px", "size": 335, "sizes": []}, "image_border_radius": {"unit": "px", "top": "5", "right": "5", "bottom": "5", "left": "5", "isLinked": true}, "ae_dynamic_rules": [{"_id": "05acb3e"}]}, "elements": [], "isInner": false, "widgetType": "image", "elType": "widget"}], "isInner": false, "elType": "column"}, {"id": "4520e466", "settings": {"_column_size": 50, "_inline_size": 61.996, "_inline_size_tablet": 55, "ae_dynamic_rules": [{"_id": "0db966a"}], "slider_delay": "5000"}, "elements": [{"id": "74e234e6", "settings": {"margin": {"unit": "px", "top": "0", "right": 0, "bottom": "0", "left": 0, "isLinked": false}, "ae_dynamic_rules": [{"_id": "bf89f16"}], "slider_delay": "5000"}, "elements": [{"id": "754c12f9", "settings": {"_column_size": 100, "_inline_size": null, "ae_dynamic_rules": [{"_id": "e56e4e0"}], "slider_delay": "5000"}, "elements": [{"id": "5785e960", "settings": {"title": "Lab Control System", "align": "center", "typography_typography": "custom", "typography_font_family": "Nunito", "typography_font_size": {"unit": "px", "size": 30, "sizes": []}, "typography_font_weight": "300", "__globals__": {"title_color": "globals/colors?id=secondary"}, "ae_dynamic_rules": [{"_id": "0e68c1a"}]}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}, {"id": "61bd381b", "settings": {"editor": "<p>We offer solutions for all critical airflow applications whether it’s research in wet chemistry laboratories, the multi-discipline environments of Life Sciences, or the wide-range of healthcare spaces requiring proper directional airflow. While these markets and applications may differ, they all share the requirement of maintaining proper pressurization of spaces. The user interface software presents both real-time and historical data from our airflow control solutions in a simple dashboard format. A complete laboratory control system front end, integration to your building management system only entails creating a bookmark and going to a web page.</p>", "align": "center", "text_color": "#000000", "typography_typography": "custom", "typography_font_family": "Nunito", "typography_font_weight": "400", "_padding": {"unit": "px", "top": "0", "right": "10", "bottom": "0", "left": "10", "isLinked": false}, "ae_dynamic_rules": [{"_id": "ea67987"}]}, "elements": [], "isInner": false, "widgetType": "text-editor", "elType": "widget"}], "isInner": true, "elType": "column"}], "isInner": true, "elType": "section"}], "isInner": false, "elType": "column"}], "isInner": false, "elType": "section"}, {"id": "e02fa02", "settings": {"structure": "20", "margin": {"unit": "px", "top": "110", "right": 0, "bottom": "0", "left": 0, "isLinked": false}, "padding": {"unit": "px", "top": "0", "right": "0", "bottom": "60", "left": "0", "isLinked": false}, "_element_id": "lighting", "reverse_order_mobile": "reverse-mobile", "ae_dynamic_rules": [{"_id": "263daf4"}], "slider_delay": "5000"}, "elements": [{"id": "5859be41", "settings": {"_column_size": 50, "_inline_size": 61.001, "ae_dynamic_rules": [{"_id": "dc3d769"}], "slider_delay": "5000"}, "elements": [{"id": "e830335", "settings": {"margin": {"unit": "px", "top": "35", "right": 0, "bottom": "0", "left": 0, "isLinked": false}, "margin_tablet": {"unit": "px", "top": "0", "right": 0, "bottom": "0", "left": 0, "isLinked": true}, "ae_dynamic_rules": [{"_id": "44aa18a"}], "slider_delay": "5000"}, "elements": [{"id": "36d19053", "settings": {"_column_size": 100, "_inline_size": null, "ae_dynamic_rules": [{"_id": "d526d61"}], "slider_delay": "5000"}, "elements": [{"id": "f70d353", "settings": {"title": "Lighting Control System", "align": "center", "typography_typography": "custom", "typography_font_family": "Nunito", "typography_font_size": {"unit": "px", "size": 30, "sizes": []}, "typography_font_weight": "300", "__globals__": {"title_color": "globals/colors?id=secondary"}, "ae_dynamic_rules": [{"_id": "1fc7165"}]}, "elements": [], "isInner": false, "widgetType": "heading", "elType": "widget"}, {"id": "18cc9d91", "settings": {"editor": "<p>Ensuring having an optimum-energy building by installing occupancy monitoring and outdoor lighting sensors to control and regulate the lighting levels to the required levelsWhether you are looking to reduce energy costs, increase comfort, or manage light control solutions, our solution offer the flexibility you need with the energy savings you want</p>", "align": "center", "text_color": "#000000", "typography_typography": "custom", "typography_font_family": "Nunito", "typography_font_weight": "400", "_padding": {"unit": "px", "top": "0", "right": "10", "bottom": "0", "left": "10", "isLinked": false}, "ae_dynamic_rules": [{"_id": "c7198bc"}]}, "elements": [], "isInner": false, "widgetType": "text-editor", "elType": "widget"}], "isInner": true, "elType": "column"}], "isInner": true, "elType": "section"}], "isInner": false, "elType": "column"}, {"id": "22381bd7", "settings": {"_column_size": 50, "_inline_size": 38.952, "ae_dynamic_rules": [{"_id": "5b891cb"}], "slider_delay": "5000"}, "elements": [{"id": "255f1560", "settings": {"image": {"url": "https://azoneus.com/wp-content/uploads/2022/03/2a-768x512.webp", "id": "", "source": "url", "alt": ""}, "space": {"unit": "px", "size": 440, "sizes": []}, "space_tablet": {"unit": "px", "size": 280, "sizes": []}, "space_mobile": {"unit": "px", "size": 335, "sizes": []}, "image_border_radius": {"unit": "px", "top": "5", "right": "5", "bottom": "5", "left": "5", "isLinked": true}, "ae_dynamic_rules": [{"_id": "da13a28"}]}, "elements": [], "isInner": false, "widgetType": "image", "elType": "widget"}], "isInner": false, "elType": "column"}], "isInner": false, "elType": "section"}], "page_settings": [], "version": "0.4", "title": "Azoneus_solutions", "type": "page"}